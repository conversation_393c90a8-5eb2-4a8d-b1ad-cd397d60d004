# bigdata/selectExtendGroupData 接口优化方案

## 问题描述

在 `bigdata/selectExtendGroupData` 接口中，当 `bus_category=2`（小游戏类别）时，需要将 `cha_id` 字段替换为 `put_cha_id` 的值。但是原有的实现方式存在问题：

### 原有实现的问题
1. **筛选失效**：在Java代码中进行字段替换，但SQL的WHERE条件仍使用原始的 `cha_id` 进行筛选
2. **逻辑分离**：业务逻辑分散在SQL查询和Java后处理两个地方
3. **性能问题**：需要查询所有数据后再进行字段替换，无法在数据库层面进行有效筛选

### 具体表现
- 用户筛选 `cha_id` 时，筛选条件无法匹配到已经被替换的 `put_cha_id` 值
- 导致筛选结果不准确或为空

## 解决方案

### 核心思路
将 `cha_id` 字段的替换逻辑从Java代码移到SQL查询层面，确保筛选和显示使用相同的逻辑。

### 1. SQL层面优化

#### 修改前的SQL结构
```sql
select
    cha_id,  -- 直接使用原始字段
    yy.bus_category,
    zz.put_cha_id,
    -- 其他字段...
from ${tableName} aa
left join dnwx_adt.app_info yy on aa.appid = yy.id
left join (select adsid sid,cha_id put_cha_id FROM dnwx_bi.dn_extend_adsid_manage) zz on aa.adsid = zz.sid
where tdate BETWEEN #{start_date} AND #{end_date}
    and cha_id in (${cha_id})  -- 筛选条件使用原始字段
```

#### 修改后的SQL结构
```sql
select
    CASE 
        WHEN IFNULL(yy.bus_category, '1') = '2' AND zz.put_cha_id IS NOT NULL 
        THEN zz.put_cha_id 
        ELSE aa.cha_id 
    END as cha_id,  -- 在查询时就进行字段替换
    yy.bus_category,
    zz.put_cha_id,
    -- 其他字段...
from ${tableName} aa
left join dnwx_adt.app_info yy on aa.appid = yy.id
left join (select adsid sid,cha_id put_cha_id FROM dnwx_bi.dn_extend_adsid_manage) zz on aa.adsid = zz.sid
where tdate BETWEEN #{start_date} AND #{end_date}
    and (
        CASE 
            WHEN IFNULL(yy.bus_category, '1') = '2' AND zz.put_cha_id IS NOT NULL 
            THEN zz.put_cha_id 
            ELSE aa.cha_id 
        END
    ) in (${cha_id})  -- 筛选条件也使用相同的替换逻辑
```

### 2. Java代码优化

#### 修改前的Java逻辑
```java
// 查询数据
List<Map<String, Object>> list = dnwxBiMapper.selectDnGroupData(paramMap);

// 后处理：替换cha_id字段
if(!BlankUtils.checkBlank(chaid_group)) {
    twoList.forEach(act -> {
        if ("2".equals(act.get("bus_category").toString())) {
            act.put("cha_id", act.get("put_cha_id").toString());
        }
    });
}
```

#### 修改后的Java逻辑
```java
// 查询数据（SQL已处理字段替换逻辑）
List<Map<String, Object>> list = dnwxBiMapper.selectDnGroupData(paramMap);

// 注意：bus_category=2时的cha_id替换逻辑已在SQL层面处理，无需在此处理
```

## 技术实现细节

### 1. CASE WHEN 逻辑说明
```sql
CASE 
    WHEN IFNULL(yy.bus_category, '1') = '2' AND zz.put_cha_id IS NOT NULL 
    THEN zz.put_cha_id 
    ELSE aa.cha_id 
END as cha_id
```

**逻辑解释**：
- 当 `bus_category = '2'` 且 `put_cha_id` 不为空时，使用 `put_cha_id`
- 否则使用原始的 `cha_id`
- `IFNULL(yy.bus_category, '1')` 确保空值时默认为 '1'

### 2. WHERE条件优化
```sql
and (
    CASE 
        WHEN IFNULL(yy.bus_category, '1') = '2' AND zz.put_cha_id IS NOT NULL 
        THEN zz.put_cha_id 
        ELSE aa.cha_id 
    END
) in (${cha_id})
```

**优势**：
- 筛选条件与SELECT字段使用相同的逻辑
- 确保筛选结果的一致性
- 在数据库层面进行筛选，提高性能

### 3. 数据表关联说明
- `dnwx_adt.app_info` (yy)：获取应用的 `bus_category` 信息
- `dnwx_bi.dn_extend_adsid_manage` (zz)：获取 `put_cha_id` 映射关系
- 通过 LEFT JOIN 确保即使没有映射关系也能正常查询

## 优化效果

### 1. 功能完善
- ✅ 筛选 `cha_id` 时能正确匹配 `put_cha_id` 值
- ✅ 显示的 `cha_id` 字段已经是替换后的值
- ✅ 筛选和显示逻辑完全一致

### 2. 性能提升
- ✅ 在数据库层面进行筛选，减少数据传输量
- ✅ 避免Java层面的循环处理，提高响应速度
- ✅ 减少内存使用，特别是大数据量查询时

### 3. 代码质量
- ✅ 业务逻辑集中在SQL层面，便于维护
- ✅ 减少Java代码复杂度
- ✅ 提高代码可读性和可维护性

## 测试验证

### 1. 功能测试用例

#### 测试用例1：bus_category=2且存在put_cha_id映射
```
输入参数：
- cha_id: "test_put_channel"
- 数据库中：bus_category=2, cha_id="original_channel", put_cha_id="test_put_channel"

预期结果：
- 能够查询到数据
- 返回的cha_id字段值为"test_put_channel"
```

#### 测试用例2：bus_category=2但不存在put_cha_id映射
```
输入参数：
- cha_id: "original_channel"
- 数据库中：bus_category=2, cha_id="original_channel", put_cha_id=null

预期结果：
- 能够查询到数据
- 返回的cha_id字段值为"original_channel"
```

#### 测试用例3：bus_category!=2
```
输入参数：
- cha_id: "normal_channel"
- 数据库中：bus_category=1, cha_id="normal_channel"

预期结果：
- 能够查询到数据
- 返回的cha_id字段值为"normal_channel"
```

### 2. 性能测试
- 对比优化前后的查询响应时间
- 验证大数据量情况下的性能表现
- 检查SQL执行计划是否合理

## 部署注意事项

### 1. 数据库兼容性
- 确认MySQL版本支持CASE WHEN语法
- 验证相关表的索引是否需要调整

### 2. 回滚方案
- 保留原有Java代码作为备份
- 可以通过配置开关控制使用新旧逻辑

### 3. 监控要点
- 监控SQL查询性能
- 检查筛选结果的准确性
- 观察系统整体响应时间

## 相关接口

本次优化同时影响以下接口：
1. `bigdata/selectExtendGroupData` - 主查询接口
2. `bigdata/exportExtendGroupData` - 数据导出接口

两个接口都使用相同的SQL查询逻辑，因此优化效果一致。

## 总结

通过将业务逻辑从Java层面下沉到SQL层面，完美解决了 `bus_category=2` 时 `cha_id` 筛选失效的问题。这种解决方案不仅修复了功能缺陷，还提升了系统性能和代码质量，是一个典型的"治本"型优化方案。
