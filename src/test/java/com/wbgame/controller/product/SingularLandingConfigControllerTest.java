package com.wbgame.controller.product;

import com.alibaba.fastjson.JSON;
import com.wbgame.pojo.product.SingularLandingConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description Singular落地页配置Controller测试类
 * @Date 2025/01/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SingularLandingConfigControllerTest {

    @Test
    public void testConfigEntity() {
        // 测试实体类的基本功能
        SingularLandingConfig config = new SingularLandingConfig();
        config.setConfigName("测试配置");
        config.setSdkKey("test_sdk_key");
        config.setSdkSecret("test_sdk_secret");
        config.setBundleId("com.test.app");
        config.setBaseLink("https://test.com");
        config.setButtonGroups("[{\"text\":\"下载\",\"url\":\"https://test.com/download\"}]");
        config.setStatus(1);
        config.setRemark("测试备注");
        
        System.out.println("配置实体测试:");
        System.out.println("配置名称: " + config.getConfigName());
        System.out.println("SDK Key: " + config.getSdkKey());
        System.out.println("Bundle ID: " + config.getBundleId());
        System.out.println("按钮组: " + config.getButtonGroups());
        
        // 测试JSON序列化
        String jsonStr = JSON.toJSONString(config);
        System.out.println("JSON序列化结果: " + jsonStr);
        
        // 测试JSON反序列化
        SingularLandingConfig parsedConfig = JSON.parseObject(jsonStr, SingularLandingConfig.class);
        System.out.println("反序列化配置名称: " + parsedConfig.getConfigName());
        
        assert config.getConfigName().equals(parsedConfig.getConfigName());
        assert config.getSdkKey().equals(parsedConfig.getSdkKey());
        
        System.out.println("实体类测试通过!");
    }
    
    @Test
    public void testButtonGroupsJson() {
        // 测试按钮组JSON格式
        String buttonGroupsJson = "[" +
            "{\"text\":\"下载游戏\",\"url\":\"https://example.com/download\",\"style\":\"primary\"}," +
            "{\"text\":\"了解更多\",\"url\":\"https://example.com/more\",\"style\":\"secondary\"}" +
            "]";
        
        try {
            com.alibaba.fastjson.JSONArray.parseArray(buttonGroupsJson);
            System.out.println("按钮组JSON格式验证通过: " + buttonGroupsJson);
        } catch (Exception e) {
            System.err.println("按钮组JSON格式验证失败: " + e.getMessage());
            throw e;
        }
    }
}
