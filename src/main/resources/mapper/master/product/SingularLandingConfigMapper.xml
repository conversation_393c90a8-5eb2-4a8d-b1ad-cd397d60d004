<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.product.SingularLandingConfigMapper">

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, config_name, background_image, sdk_key, sdk_secret, bundle_id, 
        base_link, button_groups, status, remark, create_time, update_time, 
        create_user, update_user
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where_Clause">
        <where>
            <if test="config.id != null">
                AND id = #{config.id}
            </if>
            <if test="config.configName != null and config.configName != ''">
                AND config_name LIKE CONCAT('%', #{config.configName}, '%')
            </if>
            <if test="config.sdkKey != null and config.sdkKey != ''">
                AND sdk_key LIKE CONCAT('%', #{config.sdkKey}, '%')
            </if>
            <if test="config.bundleId != null and config.bundleId != ''">
                AND bundle_id LIKE CONCAT('%', #{config.bundleId}, '%')
            </if>
            <if test="config.status != null">
                AND status = #{config.status}
            </if>
            <if test="config.createUser != null and config.createUser != ''">
                AND create_user = #{config.createUser}
            </if>
        </where>
    </sql>

    <!-- 查询配置列表 -->
    <select id="queryList" resultType="com.wbgame.pojo.product.SingularLandingConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM singular_landing_config
        <include refid="Query_Where_Clause"/>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询配置 -->
    <select id="queryById" resultType="com.wbgame.pojo.product.SingularLandingConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM singular_landing_config
        WHERE id = #{id}
    </select>

    <!-- 根据SDK Key查询配置 -->
    <select id="queryBySdkKey" resultType="com.wbgame.pojo.product.SingularLandingConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM singular_landing_config
        WHERE sdk_key = #{sdkKey} AND status = 1
        LIMIT 1
    </select>

    <!-- 根据Bundle ID查询配置 -->
    <select id="queryByBundleId" resultType="com.wbgame.pojo.product.SingularLandingConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM singular_landing_config
        WHERE bundle_id = #{bundleId} AND status = 1
        LIMIT 1
    </select>

    <!-- 新增配置 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="config.id">
        INSERT INTO singular_landing_config (
            config_name, background_image, sdk_key, sdk_secret, bundle_id,
            base_link, button_groups, status, remark, create_time, create_user
        ) VALUES (
            #{config.configName}, #{config.backgroundImage}, #{config.sdkKey}, 
            #{config.sdkSecret}, #{config.bundleId}, #{config.baseLink}, 
            #{config.buttonGroups}, #{config.status}, #{config.remark}, 
            NOW(), #{config.createUser}
        )
    </insert>

    <!-- 更新配置 -->
    <update id="update">
        UPDATE singular_landing_config
        <set>
            <if test="config.configName != null and config.configName != ''">
                config_name = #{config.configName},
            </if>
            <if test="config.backgroundImage != null">
                background_image = #{config.backgroundImage},
            </if>
            <if test="config.sdkKey != null and config.sdkKey != ''">
                sdk_key = #{config.sdkKey},
            </if>
            <if test="config.sdkSecret != null and config.sdkSecret != ''">
                sdk_secret = #{config.sdkSecret},
            </if>
            <if test="config.bundleId != null and config.bundleId != ''">
                bundle_id = #{config.bundleId},
            </if>
            <if test="config.baseLink != null and config.baseLink != ''">
                base_link = #{config.baseLink},
            </if>
            <if test="config.buttonGroups != null">
                button_groups = #{config.buttonGroups},
            </if>
            <if test="config.status != null">
                status = #{config.status},
            </if>
            <if test="config.remark != null">
                remark = #{config.remark},
            </if>
            <if test="config.updateUser != null and config.updateUser != ''">
                update_user = #{config.updateUser},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{config.id}
    </update>

    <!-- 删除配置 -->
    <delete id="deleteById">
        DELETE FROM singular_landing_config WHERE id = #{id}
    </delete>

    <!-- 批量删除配置 -->
    <delete id="batchDelete">
        DELETE FROM singular_landing_config 
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 检查SDK Key是否已存在 -->
    <select id="checkSdkKeyExists" resultType="int">
        SELECT COUNT(1) FROM singular_landing_config 
        WHERE sdk_key = #{sdkKey}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查Bundle ID是否已存在 -->
    <select id="checkBundleIdExists" resultType="int">
        SELECT COUNT(1) FROM singular_landing_config 
        WHERE bundle_id = #{bundleId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
