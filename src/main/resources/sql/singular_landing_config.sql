/*
Singular落地页配置管理表

Source Database: wb-ssds
Target Server Type: MYSQL
File Encoding: 65001

Date: 2025-01-27
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for singular_landing_config
-- ----------------------------
DROP TABLE IF EXISTS `singular_landing_config`;
CREATE TABLE `singular_landing_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `background_image` varchar(500) DEFAULT NULL COMMENT '背景图URL',
  `sdk_key` varchar(200) NOT NULL COMMENT 'SDK Key',
  `sdk_secret` varchar(200) NOT NULL COMMENT 'SDK Secret',
  `bundle_id` varchar(200) NOT NULL COMMENT 'Bundle ID',
  `base_link` varchar(500) NOT NULL COMMENT '基础链接',
  `button_groups` text COMMENT '按钮组信息(JSON格式)',
  `status` tinyint(2) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_sdk_key` (`sdk_key`),
  KEY `idx_bundle_id` (`bundle_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Singular落地页配置表';

-- ----------------------------
-- 示例数据
-- ----------------------------
INSERT INTO `singular_landing_config` (`config_name`, `background_image`, `sdk_key`, `sdk_secret`, `bundle_id`, `base_link`, `button_groups`, `status`, `remark`, `create_user`) VALUES
('测试配置1', 'https://example.com/bg1.jpg', 'test_sdk_key_001', 'test_sdk_secret_001', 'com.example.app1', 'https://example.com/base1', '[{"text":"下载游戏","url":"https://example.com/download1","style":"primary"},{"text":"了解更多","url":"https://example.com/more1","style":"secondary"}]', 1, '测试用配置', 'admin'),
('测试配置2', 'https://example.com/bg2.jpg', 'test_sdk_key_002', 'test_sdk_secret_002', 'com.example.app2', 'https://example.com/base2', '[{"text":"立即体验","url":"https://example.com/download2","style":"primary"},{"text":"查看详情","url":"https://example.com/detail2","style":"secondary"},{"text":"分享好友","url":"https://example.com/share2","style":"outline"}]', 1, '测试用配置2', 'admin');
