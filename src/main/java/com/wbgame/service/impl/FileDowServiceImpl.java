package com.wbgame.service.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.DnFileDownMapper;
import com.wbgame.pojo.DnFileDown;
import com.wbgame.service.IFileDowService;
import com.wbgame.utils.export.AbstractDownFile;
import com.wbgame.utils.export.MultiSheetExcelUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: zhangY
 * @createDate: 2023/03/08 008
 * @class: FileDowServiceImpl
 * @description:
 */
@Service
public class FileDowServiceImpl implements IFileDowService {

    @Resource
    private DnFileDownMapper dnFileDownMapper;

    @Override
    public void saveFileUrl() {

        try {

            List<MultiSheetExcelUtils.Entity> entityList = new ArrayList<>();
            for (Map.Entry<String, MultiSheetExcelUtils.Entity> entry : MultiSheetExcelUtils.DOWNLOAD_MAP.entrySet()) {
                MultiSheetExcelUtils.Entity value = entry.getValue();
                if (value.getStatus() == 2) {

                    entityList.add(value);
                    MultiSheetExcelUtils.DOWNLOAD_MAP.remove(entry.getKey());
                }
            }

            if (!entityList.isEmpty()) {

                dnFileDownMapper.insert(entityList.stream().map(entity -> {

                    DnFileDown dnFileDown = new DnFileDown();
                    BeanUtils.copyProperties(entity, dnFileDown);
                    dnFileDown.setStatus((byte)entity.getStatus());
                    dnFileDown.setUrl(MultiSheetExcelUtils.DNOW_URL
                            + MultiSheetExcelUtils.OBJ_NAME + entity.getName() + ".xlsx");
                    return dnFileDown;
                }).collect(Collectors.toList()));

                System.out.println("文件地址入库完成...");
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String gainFileUrl(String name) {

        // 先看map里边状态 如果有返回地址
        MultiSheetExcelUtils.Entity entity = MultiSheetExcelUtils.getEnObj().downLoad(name);
        //MultiSheetExcelUtils.Entity entity = MultiSheetExcelUtils.DOWNLOAD_MAP.get(name);
        if (entity != null) {

            return entity.getFileName();
        }
        // 如果map里边没有可能是入库的时候删除了
        DnFileDown dnFileDown = new DnFileDown();
        dnFileDown.setName(name);
        List<DnFileDown> dnFileDowns = dnFileDownMapper.selectByExample(dnFileDown);

        // 获取入库的数据返回地址
        if (ObjectUtils.isEmpty(dnFileDowns)) {

            return null;
        }
        return dnFileDowns.get(0).getUrl();
    }

    @Override
    public int getFileStatus(String name) {

        int status = MultiSheetExcelUtils.getEnObj().getDowStatus(name);

        if (status == -1) {

            DnFileDown dnFileDown = new DnFileDown();
            dnFileDown.setName(name);
            List<DnFileDown> dnFileDowns = dnFileDownMapper.selectByExample(dnFileDown);

            status = dnFileDowns.isEmpty() ? -1 : dnFileDowns.get(0).getStatus();
        }

        return status;
    }

    @Override
    public List<DnFileDown> selectAllFile(Integer pageNum, Integer pageSize) {

        PageHelper.startPage(pageNum, pageSize);
        return dnFileDownMapper.selectByExample(null);
    }

    @Override
    public void put(String name, MultiSheetExcelUtils.Entity entity) {

        AbstractDownFile.DOWNLOAD_MAP.put(name, entity);
        saveFileUrl();
    }
}
