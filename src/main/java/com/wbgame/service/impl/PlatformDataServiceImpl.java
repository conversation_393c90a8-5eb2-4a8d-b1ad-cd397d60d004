package com.wbgame.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.ApiException;
import com.wbgame.common.constants.RobotConstants;
import com.wbgame.mapper.adb.ADBUmengMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.PlatformDataMapper;
import com.wbgame.mapper.master.WbConfigMapper;
import com.wbgame.mapper.master.mobile.ApiPacketParaConfigMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.UmengUserChannelTotal;
import com.wbgame.pojo.adv2.*;
import com.wbgame.pojo.adv2.platform.PlatformMsgInfoVo;
import com.wbgame.pojo.adv2.platform.PlatformReservationDataVo;
import com.wbgame.pojo.mobile.hw.HwCommonInfoVo;
import com.wbgame.pojo.mobile.request.ApiPacketParaConfigParam;
import com.wbgame.pojo.mobile.response.ApiPacketParaConfigResponseParam;
import com.wbgame.service.CommonService;
import com.wbgame.service.PlatformDataService;
import com.wbgame.service.impl.platform.PlatformDataConstants;
import com.wbgame.service.impl.platform.huawei.HuaweiPageDataServiceImpl;
import com.wbgame.service.impl.platform.oppo.OppoAppInfoServiceImpl;
import com.wbgame.service.impl.platform.oppo.OppoPageDataServiceImpl;
import com.wbgame.service.impl.platform.vivo.VivoAppInfoServiceImpl;
import com.wbgame.service.impl.platform.vivo.VivoPageDataServiceImpl;
import com.wbgame.service.impl.platform.xiaomi.XiaomiDataServiceImpl;
import com.wbgame.utils.*;
import com.wbgame.utils.jettison.StringUtil;
import com.wbgame.utils.platform.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.mail.Address;
import javax.mail.internet.InternetAddress;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname PlatformDataServiceImpl
 * @Description TODO
 * @Date 2021/12/6 12:19
 */
@Service
public class PlatformDataServiceImpl implements PlatformDataService {

    private static final Logger logger = LoggerFactory.getLogger(PlatformDataServiceImpl.class);

    private static final String HW_REPORT_API_URL_DOWNLOAD = "https://connect-api.cloud.huawei.com/api/report/distribution-operation-quality/v1/appDownloadExport/";

    private static final String HW_REPORT_API_URL_USERDATA = "https://connect-api.cloud.huawei.com/api/report/distribution-operation-quality/v1/addAdKpExport/";

    private static final String HW_REPORT_API_URL_PAYDATA ="https://connect-api.cloud.huawei.com/api/report/distribution-operation-quality/v1/IAPExport/";

    private static final String HW_REPORT_API_URL_RESERVATIONDATA = "https://connect-api.cloud.huawei.com/api/report/distribution-operation-quality/v1/gameReservationExport/";

    @Autowired
    PlatformDataMapper platformDataMapper;

    @Autowired
    private AdMapper adMapper;

    @Autowired
    private WbConfigMapper wbConfigMapper;

    @Autowired
    private TfxtMapper tfxtMapper;

    @Autowired
    private CommonService commonService;
    @Autowired
    private ADBUmengMapper adbUmengMapper;
    @Autowired
    private ApiPacketParaConfigMapper apiPacketParaConfigMapper;

    public static String MSG_URL = "https://edc.vigame.cn:6115/fs/sendMsg?robot=robot1&uname=sunwf&msg=";

    private static final String WARN_FLAG = "1";
    private static final String NO_WARN_FLAG = "0";

    @Override
    public List<PlatformAppInfoVo> getPlatformAppInfoList(PlatformAppInfoVo vo) {
        return platformDataMapper.getPlatformAppInfoList(vo);
    }

    @Override
    public int savePlatformAppInfo(PlatformAppInfoVo vo) {
        return platformDataMapper.savePlatformAppInfo(vo);
    }

    @Override
    public int updatePlatformAppInfo(PlatformAppInfoVo vo) {
        return platformDataMapper.updatePlatformAppInfo(vo);
    }
    @Override
    public int batchAddAppidTag(PlatformAppInfoVo vo) {
        return platformDataMapper.batchAddAppidTag(vo);
    }
    @Override
    public int batchUpdatePlatformAppInfoAsAppidTag(List<PlatformAppInfoVo> list) {
        return platformDataMapper.batchUpdatePlatformAppInfoAsAppidTag(list);
    }

    @Override
    public int delPlatformAppInfo(PlatformAppInfoVo vo) {
        return platformDataMapper.delPlatformAppInfo(vo);
    }

    /**
     * 根据id批量删除
     *
     * @param ids
     * @return 删除个数
     */
    @Override
    public int batchDelAppidTag(Map<String, String> params) {
        return platformDataMapper.batchDelAppidTag(params);
    }

    /**
     * 根据所选id批量更新产品自定义分组数据
     *
     * @param vo
     */
    @Override
    public int batchUpdateAppidTag(Map<String, String> params) {
        return platformDataMapper.batchUpdateAppidTag(params);
    }

    @Override
    public List<PlatformPageDataVo> getPlatformPageDataList(Map map) {
        return platformDataMapper.getPlatformPageDataList(map);
    }

    /**
     * 查询广告平台数据--汇总
     *
     * @param map 查询条件
     * @return 查询结果
     */
    @Override
    public PlatformPageDataVo getPlatformPageDataSum(Map map) {
        return platformDataMapper.getPlatformPageDataSum(map);
    }

    @Override
    public List<PlatformGradeDataVo> getPlatformGradeDataList(Map map) {
        return platformDataMapper.getPlatformGradeDataList(map);
    }

    @Override
//    @Async("aaaScheduler")
    public void syncPlatformDetailPageData(String startTime, String endTime, String platform,String account) {
        List<PlatformPageDataVo> list = new ArrayList<>();
        try {
            if ("oppo".equals(platform)) {
                list = oppoDetailPageData(startTime, endTime,account,"page");
                // oppo小游戏数据拉取
                List<PlatformPageDataVo> oppoQuickGameList = syncOppoQuickGameList(startTime, endTime,account);
                if (CollectionUtils.isEmpty(list)) {
                    list = oppoQuickGameList;
                } else {
                    list.addAll(oppoQuickGameList);
                }
            } else if ("xiaomi".equals(platform)) {
                list = xiaomiPageData(startTime, endTime,account,"page");
            } else if ("huawei".equals(platform)) {
                list = huaweiPageDataNew(startTime, endTime,account,"page");
                // 华为小游戏数据拉取
                List<PlatformPageDataVo> hwQuickGameList = syncHuaweiQuickGameList(startTime, endTime,account);
                if (CollectionUtils.isEmpty(list)) {
                    list = hwQuickGameList;
                } else {
                    list.addAll(hwQuickGameList);
                }
            } else if ("vivo".equals(platform)) {
                list = vivoPageData(startTime, endTime,account,"page");
            } else if ("honor".equals(platform)) {
                list = honorPageData(startTime, endTime,account,"page");
            }
            if (!CollectionUtils.isEmpty(list)) {
                platformDataMapper.savePlatformPageData(list);
                logger.info("平台:" + platform + "详情页分发分析拉取完成");
            } else {
                logger.info("平台:" + platform + "详情页分发分析未拉取到数据");
            }
        } catch (Exception e) {
            logger.error("syncPlatformDetailPageData error:", e);
        }
    }

    private List<PlatformPageDataVo> honorPageData(String startTime, String endTime, String account, String page) {
        logger.info("荣耀拉取渠道详情页转化分析 start, account:{}, time:{}",account, startTime + "-" + endTime);
        try {
            List<PlatformPageDataVo> list = new ArrayList<>();
            PlatformAppInfoVo vo = new PlatformAppInfoVo();
            vo.setPlatform("honor");
            vo.setTaccount(account);
            List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);
            Map<String, PlatformAppInfoVo> collect = appList.stream().collect(Collectors.toMap(
                    PlatformAppInfoVo::getPackagename, app -> app, (k1, k2) -> k1));

            String sql = "select account as mapkey,tttoken,ttparam,cname,company_type from app_channel_config where channel = 'honor' and status=1 ";
            if (!BlankUtils.checkBlank(account)) {
                sql = sql + " and account ='" + account + "' ";
            }
            Map<String, Map<String, Object>> tokenMap = adMapper.queryListMapOfKey(sql);
            for (Map.Entry<String, Map<String, Object>> entry : tokenMap.entrySet()) {
                String cookie = BlankUtils.getString(entry.getValue().get("tttoken"));
                HashMap<String, String> header = new HashMap<>();
                header.put("cookie", cookie);
                header.put("developer-csrftoken", BlankUtils.getString(entry.getValue().get("ttparam")));
                header.put("content-type", "application/json;charset=UTF-8");
                header.put("referer", "https://developer.honor.com/cn/manageCenter/reportCommon?id=26&cardId=11&cartName=%E6%B8%B8%E6%88%8F%E8%81%94%E8%BF%90%E6%8A%A5%E8%A1%A8&menuId=pg2024031800100-idx-1");

                HashMap<String, Object> params = new HashMap<>();
                params.put("pageId", "pg2024031800100");
                params.put("filterType", "androidgame-pkgname");
                params.put("columnKeyName", "app_pkg_name");

                String ret = HttpClientUtils.getInstance().httpPostThrow(
                        "https://developer.honor.com/reportPortal/v2/queryFilterData",
                        JSONObject.toJSONString(params), header);
                JSONObject jsonObject = JSONObject.parseObject(ret);
                String code = jsonObject.getString("code");
                if (!"200".equals(code)) {
                    logger.error("荣耀拉取渠道详情页转化分析 error:{} - {}", jsonObject.getString("code"),jsonObject.getString("msg"));
                    // 限定游戏账号的数据拉取失败告警
                    if ("游戏".equals(entry.getValue().getOrDefault("company_type","").toString())) {
                        String alertMsg = "荣耀拉取渠道详情页转化分析数据爬取失败，媒体：honor，报错账号：" + entry.getKey() + "-" +entry.getValue().get("cname") + "，异常原因："+ jsonObject.getString("msg") +"，请及时关注！";;
                        commonService.onceDailyAlertMsg("onceHonorPageData:" + entry.getKey(),alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                    }
                    return null;
                }

                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (CollectionUtils.isEmpty(jsonArray)) {
                    logger.error("荣耀拉取渠道详情页转化分析 获取appid为空 account：{}", account);
                    return null;
                }
                List<String> packageList = jsonArray.stream().map(o -> (JSONObject) o)
                        .map(o -> o.getString("key")).collect(Collectors.toList());

                for (String packageName : packageList) {
                    PlatformAppInfoVo platformAppInfoVo = collect.get(packageName);
                    if (platformAppInfoVo == null) {
                        continue;
                    }
                    ArrayList<PlatformPageDataVo> platformPageDataVos = honorDownloadProcess(platformAppInfoVo, header, startTime, endTime);
                    if (CollectionUtils.isEmpty(platformPageDataVos)) {
                        continue;
                    }

                    Map<String, PlatformPageDataVo> dataGroupByDate = platformPageDataVos.stream().collect(
                            Collectors.toMap(PlatformPageDataVo::getTdate, k1 -> k1, (k1, k2) -> k1));
                    honorPayProcess(platformAppInfoVo, header, startTime, endTime, dataGroupByDate);
                    honorRetainProcess(platformAppInfoVo, header, startTime, endTime, dataGroupByDate);
                    list.addAll(platformPageDataVos);
                }
            }
            return list;

        } catch (Exception e) {
            logger.error("honorPageData error:", e);
        }
        return null;
    }

    private void honorRetainProcess(PlatformAppInfoVo platformAppInfoVo, HashMap<String, String> header, String startTime, String endTime, Map<String, PlatformPageDataVo> dataGroupByDate) {
        header.put("referer", "https://developer.honor.com/cn/manageCenter/reportCommon?id=26&cardId=11&cartName=%E6%B8%B8%E6%88%8F%E8%81%94%E8%BF%90%E6%8A%A5%E8%A1%A8&menuId=pg2024031800203-idx-2_3");

        HashMap<String, Object> params = new HashMap<>();
        params.put("pageId","pg2024031800203");
        params.put("componentId","xzlckb2");
        ArrayList<HashMap<String, String>> filters = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("fieldName","app_pkg_name");
        map.put("expression","eq");
        map.put("fieldValue",platformAppInfoVo.getPackagename());
        filters.add(map);
        HashMap<String, String> map2 = new HashMap<>();
        map2.put("fieldName","pt_d");
        map2.put("expression","gte");
        map2.put("fieldValue",startTime);
        filters.add(map2);
        HashMap<String, String> map3 = new HashMap<>();
        map3.put("fieldName","pt_d");
        map3.put("expression","lte");
        map3.put("fieldValue",endTime);
        filters.add(map3);
        params.put("filters", filters);

        String ret = HttpClientUtils.getInstance().httpPostThrow("https://developer.honor.com/reportPortal/v2/queryComponentData",
                JSONObject.toJSONString(params), header);
        JSONObject jsonObject = JSONObject.parseObject(ret);
        String code = jsonObject.getString("code");
        if (!"200".equals(code)) {
            logger.error("荣耀拉取渠道详情页转化分析 error:{} - {}", jsonObject.getString("code"),jsonObject.getString("msg"));
            return;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject reportTableVo = data.getJSONObject("reportTableVo");
        JSONArray jsonArray = reportTableVo.getJSONArray("values");
        if (CollectionUtils.isEmpty(jsonArray)) {
            logger.error("荣耀拉取渠道详情页转化分析 获取付费为空 package：{}", platformAppInfoVo.getPackagename());
            return;
        }
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject dt = jsonArray.getJSONObject(i);
            String ptD = dt.getString("pt_d");
            if (dataGroupByDate.containsKey(ptD)) {
                PlatformPageDataVo platformPageDataVo = dataGroupByDate.get(ptD);
                platformPageDataVo.setAddnum(dt.getString("up_new_active_qty"));
                platformPageDataVo.setDau(dt.getString("up_active_qty"));
                try {
                    String new_retention_2d = dt.getString("new_retention_2d");
                    if (BlankUtils.isNotBlank(new_retention_2d)) {
                        new_retention_2d = new_retention_2d.replace("%", "");
                        BigDecimal bd = new BigDecimal(new_retention_2d).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                        new_retention_2d = bd.toString();
                    }
                    platformPageDataVo.setKeep1(new_retention_2d);
                } catch (Exception e) {
                    logger.error("荣耀拉取渠道详情页转化分析 转换百分比错误， package： {}", platformAppInfoVo.getPackagename());
                }
                try {
                    String new_retention_2d = dt.getString("new_retention_3d");
                    if (BlankUtils.isNotBlank(new_retention_2d)) {
                        new_retention_2d = new_retention_2d.replace("%", "");
                        BigDecimal bd = new BigDecimal(new_retention_2d).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                        new_retention_2d = bd.toString();
                    }
                    platformPageDataVo.setKeep3(new_retention_2d);
                } catch (Exception e) {
                    logger.error("荣耀拉取渠道详情页转化分析 转换百分比错误， package： {}", platformAppInfoVo.getPackagename());
                }
                try {
                    String new_retention_2d = dt.getString("new_retention_7d");
                    if (BlankUtils.isNotBlank(new_retention_2d)) {
                        new_retention_2d = new_retention_2d.replace("%", "");
                        BigDecimal bd = new BigDecimal(new_retention_2d).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                        new_retention_2d = bd.toString();
                    }
                    platformPageDataVo.setKeep3(new_retention_2d);
                } catch (Exception e) {
                    logger.error("荣耀拉取渠道详情页转化分析 转换百分比错误， package： {}", platformAppInfoVo.getPackagename());
                }
            }
        }
    }

    private void honorPayProcess(PlatformAppInfoVo platformAppInfoVo, HashMap<String, String> header, String startTime, String endTime, Map<String, PlatformPageDataVo> platformPageDataVos) {
        header.put("referer", "https://developer.honor.com/cn/manageCenter/reportCommon?id=26&cardId=11&cartName=%E6%B8%B8%E6%88%8F%E8%81%94%E8%BF%90%E6%8A%A5%E8%A1%A8&menuId=pg2024031800301-idx-3_1");

        HashMap<String, Object> params = new HashMap<>();
        params.put("pageId","pg2024031800301");
        params.put("componentId","yynff1");
        ArrayList<HashMap<String, String>> filters = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("fieldName","app_pkg_name");
        map.put("expression","eq");
        map.put("fieldValue",platformAppInfoVo.getPackagename());
        filters.add(map);
        HashMap<String, String> map2 = new HashMap<>();
        map2.put("fieldName","pt_d");
        map2.put("expression","gte");
        map2.put("fieldValue",startTime);
        filters.add(map2);
        HashMap<String, String> map3 = new HashMap<>();
        map3.put("fieldName","pt_d");
        map3.put("expression","lte");
        map3.put("fieldValue",endTime);
        filters.add(map3);
        params.put("filters", filters);

        String ret = HttpClientUtils.getInstance().httpPostThrow("https://developer.honor.com/reportPortal/v2/queryComponentData",
                JSONObject.toJSONString(params), header);
        JSONObject jsonObject = JSONObject.parseObject(ret);
        String code = jsonObject.getString("code");
        if (!"200".equals(code)) {
            logger.error("荣耀拉取渠道详情页转化分析 error:{} - {}", jsonObject.getString("code"),jsonObject.getString("msg"));
            return;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject reportTableVo = data.getJSONObject("reportTableVo");
        JSONArray jsonArray = reportTableVo.getJSONArray("values");
        if (CollectionUtils.isEmpty(jsonArray)) {
            logger.error("荣耀拉取渠道详情页转化分析 获取付费为空 package：{}", platformAppInfoVo.getPackagename());
            return;
        }
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject dt = jsonArray.getJSONObject(i);
            String ptD = dt.getString("pt_d");
            if (platformPageDataVos.containsKey(ptD)) {
                PlatformPageDataVo platformPageDataVo = platformPageDataVos.get(ptD);
                platformPageDataVo.setPay_num(dt.getString("pay_userid_success_qty"));
                platformPageDataVo.setPay_total(dt.getString("pay_money_qty"));
                platformPageDataVo.setPay_times(dt.getString("pay_success_qty"));
                platformPageDataVo.setKobe_coupon(dt.getString("coupon_amount_cp_honor"));
                try {
                    String paySuccess = dt.getString("pay_success");
                    if (BlankUtils.isNotBlank(paySuccess)) {
                        paySuccess = paySuccess.replace("%", "");
                        BigDecimal bd = new BigDecimal(paySuccess).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                        paySuccess = bd.toString();
                    }
                    platformPageDataVo.setPay_succ_ratio(paySuccess);
                } catch (Exception e) {
                    logger.error("荣耀拉取渠道详情页转化分析 转换百分比错误， package： {}", platformAppInfoVo.getPackagename());
                }
            }
        }
    }

    private ArrayList<PlatformPageDataVo> honorDownloadProcess(PlatformAppInfoVo platformAppInfoVo, HashMap<String, String> header, String startTime, String endTime) {
        header.put("referer", "https://developer.honor.com/cn/manageCenter/reportCommon?id=26&cardId=11&cartName=%E6%B8%B8%E6%88%8F%E8%81%94%E8%BF%90%E6%8A%A5%E8%A1%A8&menuId=pg2024031800201-idx-2_1");

        HashMap<String, Object> params = new HashMap<>();
        params.put("pageId","pg2024031800201");
        params.put("componentId","gamereportdownloadinstall002wk");
        ArrayList<HashMap<String, String>> filters = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("fieldName","app_pkg_name");
        map.put("expression","eq");
        map.put("fieldValue",platformAppInfoVo.getPackagename());
        filters.add(map);
        HashMap<String, String> map2 = new HashMap<>();
        map2.put("fieldName","pt_d");
        map2.put("expression","gte");
        map2.put("fieldValue",startTime);
        filters.add(map2);
        HashMap<String, String> map3 = new HashMap<>();
        map3.put("fieldName","pt_d");
        map3.put("expression","lte");
        map3.put("fieldValue",endTime);
        filters.add(map3);
        params.put("filters", filters);

        String ret = HttpClientUtils.getInstance().httpPostThrow("https://developer.honor.com/reportPortal/v2/queryComponentData",
                JSONObject.toJSONString(params), header);

        JSONObject jsonObject = JSONObject.parseObject(ret);
        if (!"200".equals(jsonObject.getString("code"))) {
            logger.error("荣耀拉取渠道详情页转化分析 error:{} - {}", jsonObject.getString("code"),jsonObject.getString("msg"));
            return null;
        }

        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject reportTableVo = data.getJSONObject("reportTableVo");
        JSONArray jsonArray = reportTableVo.getJSONArray("values");
        if (CollectionUtils.isEmpty(jsonArray)) {
            logger.error("荣耀拉取渠道详情页转化分析 获取下载为空 account：{}", platformAppInfoVo.getPackagename());
            return null;
        }
        ArrayList<PlatformPageDataVo> platformPageDataVos = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject dt = jsonArray.getJSONObject(i);
            PlatformPageDataVo vo = new PlatformPageDataVo();
            org.springframework.beans.BeanUtils.copyProperties(platformAppInfoVo, vo);
            String first_install_success_ratio = dt.getString("first_install_success_ratio");
            String first_install_download_success_ratio = dt.getString("first_install_download_success_ratio");
            String first_install_download_success = dt.getString("first_install_download_success");
            String market_first_download_qty = dt.getString("market_first_download_qty");
            String pt_d = dt.getString("pt_d");
            vo.setNet_download(market_first_download_qty);
            vo.setTotal_download(first_install_download_success);
            vo.setTdate(pt_d);
            try {
                if (BlankUtils.isNotBlank(first_install_success_ratio)) {
                    first_install_success_ratio = first_install_success_ratio.replace("%", "");
                    BigDecimal bd = new BigDecimal(first_install_success_ratio).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                    first_install_success_ratio = bd.toString();
                }
                vo.setInstall_rate(first_install_success_ratio);
            } catch (Exception e) {
                logger.error("荣耀拉取渠道详情页转化分析 转换百分比错误， package： {}", platformAppInfoVo.getPackagename());
            }
            try {
                if (BlankUtils.isNotBlank(first_install_download_success_ratio)) {
                    first_install_download_success_ratio = first_install_download_success_ratio.replace("%", "");
                    BigDecimal bd = new BigDecimal(first_install_download_success_ratio).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                    first_install_download_success_ratio = bd.toString();
                }
                vo.setDownload_rate(first_install_download_success_ratio);
            } catch (Exception e) {
                logger.error("荣耀拉取渠道详情页转化分析 转换百分比错误， package： {}", platformAppInfoVo.getPackagename());
            }

            platformPageDataVos.add(vo);

        }
        return platformPageDataVos;
    }

    @Override
    @Async("aaaScheduler")
    public void syncPlatformGradeData(String platform) {
        List<PlatformGradeDataVo> list = new ArrayList<>();
        try {
            if ("oppo".equals(platform)) {
                list = oppoGradeData();
            }
            if (list.size() > 0) {
                platformDataMapper.savePlatformGradeData(list);
            } else {
                logger.info("平台:" + platform + "渠道评分未拉取到数据");
            }
        } catch (Exception e) {
            logger.error("syncPlatformGradeData error:", e);
        }
    }

    @Override
    @Async("platformAppInfoScheduler")
    public void syncPlatformAppInfoData(String platform,String account) {
        try {
            if ("oppo".equals(platform)) {
                oppoPlatformAppInfoDataNew(account);
                //h5_oppo 小游戏获取平台状态信息,是通过爬虫方式获取的
                h5oppoPlatformAppInfoData(account);
            } else if ("xiaomi".equals(platform)) {
                xiaomiPlatformAppInfoData(account);
            } else if ("huawei".equals(platform)) {
                huaweiPlatformAppInfoData(account);
            } else if ("vivo".equals(platform)) {
                vivoPlatformAppInfoDataNew(account);
                //h5_vivo小游戏获取平台状态信息，通过爬虫的方式获取
                h5vivoPlatformAppInfoData(account);
            } else if ("honor".equals(platform)) {
                honorPlatformAppInfoData(account);
            }
        } catch (Exception e) {
            logger.error("syncPlatformAppInfoData error:", e);
        }
    }

    /**
     * 通过爬虫的方式获取vivo小游戏的状态信息
     * @param account 账号
     */
    private void h5vivoPlatformAppInfoData(String account) {

        logger.info("h5vivoPlatformAppInfoData start");
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setPlatform(PlatformDataConstants.PLATFORM.VIVO);
        query.setTaccount(account);
        query.setChannel("'h5_vivo'");
        List<PlatformAppInfoVo> list = platformDataMapper.getPlatformAppInfoList(query);
        Map<String,List<PlatformAppInfoVo>> listMap = list.stream().filter(t->!BlankUtils.checkBlank(t.getPackagename())).collect(Collectors.groupingBy(PlatformAppInfoVo::getTaccount));

        String app_config_sql = "select account as mapkey,tttoken,company_type,cname from app_channel_config where channel = 'vivo' and tttoken != '' and status = 1";
        Map<String, Map<String, Object>> accountConfigMap = adMapper.queryListMapOfKey(app_config_sql);

        for (Map.Entry<String,List<PlatformAppInfoVo>> map:listMap.entrySet()){
            String account_key = map.getKey();
            //每次执行前更新拉取状态置空
            platformDataMapper.updateChannelAppInfoNotSuccessState("h5_vivo",account_key,PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_INIT);
            List<PlatformAppInfoVo> appList = map.getValue();
            //备份老状态
            List<PlatformAppInfoVo> baseList = BeanUtils.copyList(appList);
            //告警
            List<PlatformAppInfoVo> warnList = new ArrayList<>();
            List<PlatformAppInfoVo> rejectList = new ArrayList<>();
            if (!accountConfigMap.containsKey(account_key)) {
                logger.error("不存在配置的账号信息");
                continue;
            }
            Map<String, Object> accountMap = accountConfigMap.get(account_key);
            //账号类型
            String companyType = accountMap.get("company_type") + "";
            try {
                //拉取h5_vivo小游戏产品状态数据
                VivoAppInfoServiceImpl.syncH5vivoAppInfo(appList, accountMap);
                for (PlatformAppInfoVo vo:appList){
                    String key = vo.getTappid();
                    PlatformAppInfoVo base = baseList.stream().filter(t->t.getTappid().equals(key)).findFirst().orElse(null);
                    if (base!=null&&vo.getState()!=null&&!vo.getState().equals(base.getState())){
                        vo.setStateTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                        //新增通知SDK状态变更为上线  //******** 新增定时发布状态下发配置工具，通知SDK状态变更 待发布
                        if (PlatformDataConstants.APPINFO.ONLINE_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.PUBLISH_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.OFFLINE_STATE.equals(vo.getState())){
                            warnList.add(vo);
                        }
                        if (PlatformDataConstants.APPINFO.REJECT_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(vo.getState())){
                            /** 审核驳回和运营打回  -孙文凤.*********/
                            rejectList.add(vo);
                        }
                    }
                }
                //根据对应状态更新
                platformDataMapper.updatePlatformAppOhterInfo(appList);
                //通知SDK告警 20250424-sunwf:Vivo小游戏暂时不用处理消息发送,当前Vivo小游戏维护少，等后续项目再启动的时候再考虑维护
                /*if (warnList.size()>0){
                    PlatformDataConstants.sendSdkOnlineMessage(warnList);
                }
                if (rejectList.size()>0){
                    PlatformDataConstants.sendSdkRejectMessage(rejectList);
                }*/
                //未知错误或者token失效，发送每日报警
                boolean warnFlag = appList.stream().anyMatch(data -> "2".equals(data.getSyncState()) || "3".equals(data.getSyncState()));
                if ("游戏".equals(companyType) && warnFlag) {
                    //需要告警操作
                    String alertMsg = "h5_vivo渠道产品id关联配置数据拉取失败，媒体：vivo，报错账号：" + account_key + "-" + accountMap.get("cname") + "，异常原因：token失效或未知错误，请及时关注！";
                    commonService.onceDailyAlertMsg("onceH5vivoAppInfoData:" + account_key,alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                }
            } catch (Exception e){
                logger.error("h5vivoPlatformAppInfoData fail,account:"+account_key+",error:",e);
                if ("游戏".equals(companyType)) {
                    //需要告警操作
                    String alertMsg = "h5_vivo渠道产品id关联配置数据拉取失败，媒体：vivo，报错账号：" + account_key + "-" + accountMap.get("cname") + "，异常原因：服务器异常，请及时关注！";;
                    commonService.onceDailyAlertMsg("onceH5vivoAppInfoData:" + account_key,alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                }
            }
        }
        logger.info("h5vivoPlatformAppInfoData end");
    }

    @Override
    @Async("aaaScheduler")
    public void syncPlatformReservationData(String startTime, String endTime, String platform, String account) {
        List<PlatformReservationDataVo> list = new ArrayList<>();
        try {
            if ("oppo".equals(platform)) {
                list = oppoDetailPageData(startTime,endTime,account,"reservation");
            } else if ("xiaomi".equals(platform)) {
                list = xiaomiPageData(startTime, endTime,account,"reservation");
            } else if ("huawei".equals(platform)) {
                list = huaweiPageDataNew(startTime, endTime,account,"reservation");
            } else if ("vivo".equals(platform)) {
                list = vivoPageData(startTime, endTime,account,"reservation");
            }
            if (list.size() > 0) {
                platformDataMapper.savePlatformReservationData(list);
            } else {
                logger.info("平台:" + platform + "渠道预约转化分析未拉取到数据");
            }
        } catch (Exception e) {
            logger.error("syncPlatformDetailPageData error:", e);
        }
    }

    @Override
    public List<PlatformReservationDataVo> getPlatformReservationDataList(Map map) {
        return platformDataMapper.getPlatformReservationDataList(map);
    }

    @Override
    public PlatformReservationDataVo getPlatformReservationDataSum(Map map) {
        return platformDataMapper.getPlatformReservationDataSum(map);
    }

    @Override
    public int insertHwPlatformDataConfig(HwCommonInfoVo vo) {
        return platformDataMapper.insertHwPlatformDataConfig(vo);
    }

    @Override
    public int insertHwLoginConfig(HwCommonInfoVo vo) {
        return platformDataMapper.insertHwLoginConfig(vo);
    }

    @Override
    public int insertHwAppConfig(HwCommonInfoVo vo) {
        return platformDataMapper.insertHwAppConfig(vo);
    }

    /**
     * oppo平台详情页分发分析数据
     *
     * @param startTime
     * @param endTime
     * @return
     * @throws UnsupportedEncodingException 
     */
    public List oppoDetailPageData(String startTime, String endTime,String account,String dataType) throws UnsupportedEncodingException {

        logger.info("oppoDetailPageData start");
        try {
            List<PlatformPageDataVo> list = new ArrayList<>();
            List<PlatformReservationDataVo> reservationList = new ArrayList<>();

            Map<String, PlatformPageDataVo> dataMap = new HashMap<>();
            Map<String,PlatformReservationDataVo> reservationDataMap = new HashMap<>();

            PlatformAppInfoVo vo = new PlatformAppInfoVo();
            vo.setPlatform("oppo");
            vo.setTaccount(account);
            List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);

            String sql = "select account as mapkey,tttoken,cname,company_type from app_channel_config where channel = 'oppo' and status=1 ";
            if (!BlankUtils.checkBlank(account)){
                sql = sql + " and account ='" + account + "' ";
            }
            Map<String, Map<String, Object>> tokenMap = adMapper.queryListMapOfKey(sql);

            Map<String,List<PlatformAppInfoVo>> appGroupMap = appList.stream().collect(Collectors.groupingBy(t->t.getTappid()));
            DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
            //分单条与多条配置
            //告警数据存储，key：account,value:true/false
            Map<String,String> warnMap = new HashMap<>();
            for (Map.Entry<String,List<PlatformAppInfoVo>> map:appGroupMap.entrySet()){
                Map<String, PlatformPageDataVo> eachMap = new HashMap<>();
                Map<String,PlatformReservationDataVo> reservationEachMap = new HashMap<>();

                List<PlatformAppInfoVo> dataList = map.getValue();
                //处理一个平台产品id多个动能产品id
                TreeMap<String,PlatformAppInfoVo> bindEndTimeMap = new TreeMap<>();
                if (dataList.size()>0){
                    dataList = dataList.stream().sorted(Comparator.comparing(PlatformAppInfoVo::getBindEndTime)).collect(Collectors.toList());
                    for (PlatformAppInfoVo app:dataList){
                        bindEndTimeMap.put(app.getBindEndTime(),app);
                    }
                }
                //多条配置
                if (dataList.size()>1){
                    List<String> dateList = DateUtil.getDays(startTime,endTime);
                    for (String dateStr:dateList){
                        DateTime date = DateTime.parse(dateStr,format);
                        for (Map.Entry<String,PlatformAppInfoVo> bindEndTime:bindEndTimeMap.entrySet()){
                            DateTime bindEndTimeDate = DateTime.parse(bindEndTime.getKey(),format).plusDays(1);
                            List<PlatformAppInfoVo> eachDateDataList = new ArrayList<>();
                            if (date.isBefore(bindEndTimeDate)){
                                PlatformAppInfoVo app = bindEndTime.getValue();
                                eachDateDataList.add(app);
                                if ("page".equals(dataType)){
                                    Map<String,PlatformPageDataVo> eachDateDataMap = OppoPageDataServiceImpl.commonOppoPageData(eachDateDataList,dateStr,dateStr,tokenMap);
                                    if (eachDateDataMap.get(dateStr+"_"+app.getTappid())!=null){
                                        eachMap.put(dateStr+"_"+app.getTappid(),eachDateDataMap.get(dateStr+"_"+app.getTappid()));
                                    }
                                }
                                if ("reservation".equals(dataType)){
                                    Map<String,PlatformReservationDataVo> eachReservationDataMap = OppoPageDataServiceImpl.oppoCommonReservationDataHandle(eachDateDataList,dateStr,dateStr,tokenMap);
                                    if (eachReservationDataMap.get(dateStr+"_"+app.getTappid())!=null){
                                        reservationEachMap.put(dateStr+"_"+app.getTappid(),eachReservationDataMap.get(dateStr+"_"+app.getTappid()));
                                    }
                                }
                                break;
                            }
                        }
                    }
                }else {
                    if ("page".equals(dataType)){
                        eachMap = OppoPageDataServiceImpl.commonOppoPageData(dataList,startTime,endTime,tokenMap);
                    }
                    if ("reservation".equals(dataType)){
                        reservationEachMap = OppoPageDataServiceImpl.oppoCommonReservationDataHandle(dataList,startTime,endTime,tokenMap);
                    }
                }
                //汇总结果
                if (eachMap.size()>0){
                    for (Map.Entry<String,PlatformPageDataVo> eachDataMap:eachMap.entrySet()){
                        dataMap.put(eachDataMap.getKey(),eachDataMap.getValue());
                        warnMap.put(eachDataMap.getValue().getTaccount(),NO_WARN_FLAG);
                    }
                } else {
                    //需要告警：warnMap tokenMap
                    String taccount = dataList.get(0).getTaccount();
                    boolean warnAccountFlag = tokenMap.containsKey(taccount) && "游戏".equals(tokenMap.get(taccount).getOrDefault("company_type","").toString());
                    if (warnAccountFlag && !NO_WARN_FLAG.equals(warnMap.get(taccount))){
                        warnMap.put(dataList.get(0).getTaccount(),WARN_FLAG);
                    }
                }

                if (reservationEachMap.size()>0){
                    for (Map.Entry<String,PlatformReservationDataVo> eachDataMap:reservationEachMap.entrySet()){
                        reservationDataMap.put(eachDataMap.getKey(),eachDataMap.getValue());
                    }
                }
            }

            if (dataMap.size() > 0) {
                for (Map.Entry<String, PlatformPageDataVo> map : dataMap.entrySet()) {
                    PlatformPageDataVo mapVo = map.getValue();
                    list.add(mapVo);
                }
            }

            if (reservationDataMap.size() > 0) {
                for (Map.Entry<String, PlatformReservationDataVo> map : reservationDataMap.entrySet()) {
                    PlatformReservationDataVo mapVo = map.getValue();
                    reservationList.add(mapVo);
                }
            }
            logger.info("oppoDetailPageData end");
            //发送拉取失败邮件
            //未拉取到数据账号告警操作
            if ("page".equals(dataType) && warnMap.containsValue(WARN_FLAG)) {
                for (Map.Entry<String, String> entry : warnMap.entrySet()) {
                    if (WARN_FLAG.equals(entry.getValue())) {
                        //需要告警的主体
                        //爬取数据返回异常消息
                        String taccount = entry.getKey();
                        String alertMsg = "oppo平台详情页分发分析数据爬取失败，媒体：oppo，报错账号：" + taccount + "-" + tokenMap.get(taccount).get("cname") + "，媒体报错返回：：爬取数据异常，请及时关注！";
                        commonService.onceDailyAlertMsg("onceOppoPageData:"+ account ,alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                    }
                }
            }

            if (list.size() > 0) {
                //List<String> appids = list.stream().map(PlatformPageDataVo::getAppid).collect(Collectors.toList());
                //sendFailMail("oppo", "page", appids, appList);
                StringBuffer bf=new StringBuffer("渠道详情页转化分析数据拉取成功,");
                bf.append("平台：oppo");
                for (PlatformPageDataVo data:list){
                    String acc=data.getTaccount();
                    bf.append(",账号：");
                    bf.append(acc);
                    bf.append(",主体：");
                    bf.append(tokenMap.get(acc).get("cname"));
                }
                HttpClientUtils.getInstance().httpGet(MSG_URL+bf.toString());
                return list;
            }
            if (reservationList.size()>0){
                List<String> appids = list.stream().map(PlatformPageDataVo::getAppid).collect(Collectors.toList());
                sendFailMail("oppo", "reservation", appids, appList);
                return reservationList;
                
            }
            return list;
        } catch (Exception e) {
            logger.error("oppoDetailPageData error,msg:{}",e);
            e.printStackTrace();
        }
        return null;
        
    }



    /**
     * 获取oppo应用信息
     * @param account
     */
    public void oppoPlatformAppInfoDataNew(String account){
        logger.info("oppoPlatformAppInfoData start");
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setPlatform(PlatformDataConstants.PLATFORM.OPPO);
        query.setTaccount(account);
        List<PlatformAppInfoVo> list = platformDataMapper.getPlatformAppInfoList(query);
        Map<String,List<PlatformAppInfoVo>> listMap = list.stream().filter(t->!BlankUtils.checkBlank(t.getPackagename())).collect(Collectors.groupingBy(t->t.getTaccount()));

        String app_config_sql = "select account,ttparam from app_channel_config where channel = 'oppo' and ttparam !='' ";
        List<Map<String, Object>> app_config_list = adMapper.queryListMap(app_config_sql);
        //2024-12-03:渠道产品id关联配置-应用状态拉取 存在工具配置数据不存在apiKey,防止影响其他地方，导入配置enable=0，所以取值时取消enable=1的限制
        String sql = "select account,oppo_client_id,oppo_client_secret from  dn_oppo_account";
        List<Map<String, Object>> accountList = tfxtMapper.queryListMap(sql);

        for (Map.Entry<String,List<PlatformAppInfoVo>> map:listMap.entrySet()){
            String account_key = map.getKey();

            //每次执行前更新拉取状态置空
            platformDataMapper.updatePlatformAppInfoNotSuccessState("oppo",account_key,PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_INIT);

            //查 ttparam
            String ttparam = "";
            for(Map<String,Object> configMap:app_config_list){
                if (account_key.equals(configMap.get("account")+"")){
                    ttparam = configMap.get("ttparam")+"";
                }
            }
            //查oppo account
            if (!BlankUtils.checkBlank(ttparam)){
                List<PlatformAppInfoVo> appList = map.getValue();
                List<PlatformAppInfoVo> baseList = BeanUtils.copyList(appList);
                // 通过更新状态的list
                List<PlatformAppInfoVo> warnList = new ArrayList<>();
                List<PlatformAppInfoVo> rejectList = new ArrayList<>();

                for (Map<String,Object> accountMap:accountList){
                    if (ttparam.equals(accountMap.get("account")+"")){
                        OppoAppInfoServiceImpl.syncOppoAppInfo(appList,accountMap);
                        for (PlatformAppInfoVo vo:appList){
                            String key = vo.getTappid();
                            PlatformAppInfoVo base = baseList.stream().filter(t->t.getTappid().equals(key)).findFirst().orElse(null);
                            if (base!=null&&vo.getState()!=null&&!vo.getState().equals(base.getState())){
                                vo.setStateTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                                //新增通知SDK状态变更为上线
                                if (PlatformDataConstants.APPINFO.ONLINE_STATE.equals(vo.getState())
                                        || PlatformDataConstants.APPINFO.PUBLISH_STATE.equals(vo.getState())
                                        || PlatformDataConstants.APPINFO.OFFLINE_STATE.equals(vo.getState())){
                                    //******** 下线状态应用发送至sdk
                                    warnList.add(vo);
                                }
                                if (PlatformDataConstants.APPINFO.REJECT_STATE.equals(vo.getState())
                                        || PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(vo.getState())){
                                    /** 审核驳回和运营打回  -孙文凤.*********/
                                    rejectList.add(vo);

                                }
                            }
                        }
                    }
                }
                //根据对应状态更新
                platformDataMapper.updatePlatformAppOhterInfo(appList);

                //通知SDK告警
                if (warnList.size()>0){
                    PlatformDataConstants.sendSdkOnlineMessage(warnList);
                }
                if (rejectList.size()>0){
                    PlatformDataConstants.sendSdkRejectMessage(rejectList);
                }
            }
        }
        logger.info("oppoPlatformAppInfoData end");

    }


    private void h5oppoPlatformAppInfoData(String account) {
        logger.info("h5oppoPlatformAppInfoData start");
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setPlatform(PlatformDataConstants.PLATFORM.OPPO);
        query.setChannel("'h5_oppo'");
        query.setTaccount(account);
        List<PlatformAppInfoVo> list = platformDataMapper.getPlatformAppInfoList(query);
        Map<String,List<PlatformAppInfoVo>> listMap = list.stream().filter(t->!BlankUtils.checkBlank(t.getPackagename())).collect(Collectors.groupingBy(PlatformAppInfoVo::getTaccount));

        String app_config_sql = "select account as mapkey,tttoken,ttparam from app_channel_config where channel = 'oppo' and tttoken != '' and status = 1";
        Map<String, Map<String, Object>> appConfigMap = adMapper.queryListMapOfKey(app_config_sql);

        for (Map.Entry<String,List<PlatformAppInfoVo>> map:listMap.entrySet()){
            String account_key = map.getKey();

            //每次执行前更新拉取状态置空
            platformDataMapper.updateChannelAppInfoNotSuccessState("h5_oppo",account_key,PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_INIT);

            if (!appConfigMap.containsKey(account_key)) {
                //不存在账号相关数据，不进行处理操作
                continue;
            }
            Map<String, Object> accountMap = appConfigMap.get(account_key);
            List<PlatformAppInfoVo> appList = map.getValue();
            List<PlatformAppInfoVo> baseList = BeanUtils.copyList(appList);
            // 通过更新状态的list
            List<PlatformAppInfoVo> warnList = new ArrayList<>();
            List<PlatformAppInfoVo> rejectList = new ArrayList<>();
            OppoAppInfoServiceImpl.syncH5oppoAppInfo(appList,accountMap);
            for (PlatformAppInfoVo vo:appList){
                String key = vo.getTappid();
                PlatformAppInfoVo base = baseList.stream().filter(t->t.getTappid().equals(key)).findFirst().orElse(null);
                if (base!=null&&vo.getState()!=null&&!vo.getState().equals(base.getState())){
                    vo.setStateTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                    //新增通知SDK状态变更为上线
                    if (PlatformDataConstants.APPINFO.ONLINE_STATE.equals(vo.getState())){
                        //******** 下线状态应用发送至sdk
                        warnList.add(vo);
                    }
                    if (PlatformDataConstants.APPINFO.REJECT_STATE.equals(vo.getState())
                            || PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(vo.getState())){
                        // 审核驳回和运营打回  -孙文凤.********
                        rejectList.add(vo);
                    }
                }
            }
            //根据对应状态更新
            platformDataMapper.updatePlatformAppOhterInfo(appList);
            //通知SDK告警
            //oppo小游戏状态变更通知sdk:暂时只进行上线状态的通知
            if (warnList.size()>0){
                warnList.forEach(PlatformDataConstants::sendSdkMiniGameOnlineMessage);
            }
        }
        logger.info("h5oppoPlatformAppInfoData end");
    }



    /**
     * 按账号拉取vivo应用状态信息
     * @param account
     */
    public void vivoPlatformAppInfoDataNew(String account){
        logger.info("vivoPlatformAppInfoData start");
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setPlatform(PlatformDataConstants.PLATFORM.VIVO);
        query.setTaccount(account);
        List<PlatformAppInfoVo> list = platformDataMapper.getPlatformAppInfoList(query);
        Map<String,List<PlatformAppInfoVo>> listMap = list.stream().filter(t->!BlankUtils.checkBlank(t.getPackagename())).collect(Collectors.groupingBy(t->t.getTaccount()));
        //2024-12-03:渠道产品id关联配置-应用状态拉取存在工具配置数据不存在apiKey的情况,防止影响其他地方，导入配置enable=0，所以取值时取消enable=1的限制
        String sql = "select account,vivo_client_id,vivo_client_secret from dn_vivo_account where vivo_client_id !='' ";
        List<Map<String, Object>> accountList = adMapper.queryListMap(sql);

        //爬虫账号配置，用于获取商店状态操作
        String app_config_sql = "select account as mapkey,tttoken,company_type,cname from app_channel_config where channel = 'vivo' and tttoken != '' and status = 1";
        Map<String, Map<String, Object>> accountConfigMap = adMapper.queryListMapOfKey(app_config_sql);

        for (Map.Entry<String,List<PlatformAppInfoVo>> map:listMap.entrySet()){
            String account_key = map.getKey();
            //每次执行前更新拉取状态置空
            platformDataMapper.updatePlatformAppInfoNotSuccessState("vivo",account_key,PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_INIT);
            List<PlatformAppInfoVo> appList = map.getValue();
            //备份老状态
            List<PlatformAppInfoVo> baseList =BeanUtils.copyList(appList);
            //告警
            List<PlatformAppInfoVo> warnList = new ArrayList<>();
            List<PlatformAppInfoVo> rejectList = new ArrayList<>();
            try {
                for (Map<String, Object> accountMap : accountList) {
                    if (account_key.equals(accountMap.get("account") + "")) {
                        VivoAppInfoServiceImpl.syncVivoAppInfo(appList, accountMap);
                        for (PlatformAppInfoVo vo:appList){
                            String key = vo.getTappid();
                            PlatformAppInfoVo base = baseList.stream().filter(t->t.getTappid().equals(key)).findFirst().orElse(null);
                            if (base!=null&&vo.getState()!=null&&!vo.getState().equals(base.getState())){
                                vo.setStateTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                                //新增通知SDK状态变更为上线  //******** 新增定时发布状态下发配置工具，通知SDK状态变更 待发布
                                if (PlatformDataConstants.APPINFO.ONLINE_STATE.equals(vo.getState())
                                        || PlatformDataConstants.APPINFO.PUBLISH_STATE.equals(vo.getState())
                                        || PlatformDataConstants.APPINFO.OFFLINE_STATE.equals(vo.getState())){
                                    warnList.add(vo);
                                }
                                if (PlatformDataConstants.APPINFO.REJECT_STATE.equals(vo.getState())
                                        || PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(vo.getState())){
                                    /** 审核驳回和运营打回  -孙文凤.*********/
                                    rejectList.add(vo);
                                }
                            }
                        }
                    }
                }
                //拉取商店状态数据(通过爬虫的方式获取)
                ftechAppStoreSale(accountConfigMap, account_key, appList);
                //根据对应状态更新
                platformDataMapper.updatePlatformAppOhterInfo(appList);
                //通知SDK告警
                if (warnList.size()>0){
                    PlatformDataConstants.sendSdkOnlineMessage(warnList);
                }
                if (rejectList.size()>0){
                    PlatformDataConstants.sendSdkRejectMessage(rejectList);
                }
            }catch (Exception e){
                logger.error("vivoPlatformAppInfoDataNew fail,account:"+account_key+",error:",e);
            }

        }
        logger.info("vivoPlatformAppInfoData end");
    }

    /**
     * 获取产品的商店状态数据
     * @param accountConfigMap 爬虫配置
     * @param account_key 账号
     * @param appList 产品数据
     */
    private void ftechAppStoreSale(Map<String, Map<String, Object>> accountConfigMap, String account_key, List<PlatformAppInfoVo> appList) {
        //爬虫账号配置不存在，不进行爬取操作
        if (!accountConfigMap.containsKey(account_key)) {
            return;
        }
        Map<String, Object> accountMap = accountConfigMap.get(account_key);
        //账号类型
        String companyType = accountMap.get("company_type") + "";
        try {
            //获取app的商店状态
            VivoAppInfoServiceImpl.syncVivoAppStoreSale(appList, accountMap);
        } catch (Exception e) {
            logger.error("vivo app get sale status fail,account:"+ account_key +",error:",e);
            if ("游戏".equals(companyType)) {
                //需要告警操作
                String alertMsg = "vivo渠道产品id关联配置数据拉取商店状态失败，媒体：vivo，报错账号：" + account_key + "-" + accountMap.get("cname") + "，可能异常原因：token失效，请及时关注！";;
                commonService.onceDailyAlertMsg("oncevivoAppStoreSaleData:" + account_key,alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
            }
        }
    }

    public void xiaomiPlatformAppInfoData(String account){
        logger.info("xiaomiPlatformAppInfoData start");
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setTaccount(account);
        query.setPlatform(PlatformDataConstants.PLATFORM.XIAOMI);
        List<PlatformAppInfoVo> list = platformDataMapper.getPlatformAppInfoList(query);
        Map<String,List<PlatformAppInfoVo>> listMap = list.stream().collect(Collectors.groupingBy(t->t.getTaccount()));

        String sql = "SELECT tttoken, ttappid, account as mapkey, ttparam,cname company,`status` FROM yyhz_0308.app_channel_config WHERE channel = 'xiaomi' ";
        if (!BlankUtils.checkBlank(account)){
            sql = sql + " and account = '"+account +"' ";
        }
        Map<String, Map<String, Object>> tokenMap = adMapper.queryListMapOfKey(sql);
        for (Map.Entry<String,Map<String,Object>> map:tokenMap.entrySet()){
            String taccount = map.getKey();
            String token = map.getValue().get("ttappid").toString();
            String ttparam = map.getValue().get("ttparam").toString();

            //每次执行前更新拉取状态置空
            platformDataMapper.updatePlatformAppInfoNotSuccessState(PlatformDataConstants.PLATFORM.XIAOMI,taccount,PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_INIT);

            JSONObject fetchRet = XiaomiDataServiceImpl.fetchXiaomiPlatformAppInfoData(taccount,token,ttparam);
            String state = fetchRet.getString("state");
            if (PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_SUCCESS.equals(state)){
                //比较配置表内应用id与拉取到数据是否一致,不一致修改拉取数据原因
                List<PlatformAppInfoVo> noMatchList = new ArrayList<>();
                List<PlatformAppInfoVo> warnList = new ArrayList<>();
                List<PlatformAppInfoVo> rejectList = new ArrayList<>();
                List<PlatformAppInfoVo> dataList = (List<PlatformAppInfoVo>)fetchRet.get("data");
                Map<String,PlatformAppInfoVo> dataMap = new HashMap<>();
                if (dataList.size()>0){
                    for (PlatformAppInfoVo vo:dataList){
                        dataMap.put(vo.getTappid(),vo);
                    }
                }
                List<PlatformAppInfoVo> dataBaseList = listMap.get(taccount);
                if (dataBaseList!=null){
                    for (PlatformAppInfoVo vo:dataBaseList){
                        if (dataMap.get(vo.getTappid())==null){
                            vo.setSyncState(PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_NOMATCH);
                            vo.setState("");
                            vo.setVersion("");
                            noMatchList.add(vo);
                        }
                    }
                }
                //比对老数据
                for (PlatformAppInfoVo vo:dataList){
                    String key = vo.getTappid();
                    PlatformAppInfoVo base = dataBaseList.stream().filter(t->t.getTappid().equals(key)).findFirst().orElse(null);
                    if (base!=null&&vo.getState()!=null&&!vo.getState().equals(base.getState())){
                        vo.setStateTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                        //新增通知SDK状态变更为上线 //******** 新增定时发布状态下发配置工具，通知SDK状态变更 待发布
                        if (PlatformDataConstants.APPINFO.ONLINE_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.PUBLISH_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.OFFLINE_STATE.equals(vo.getState())){
                            vo.setChannel(base.getChannel());
                            vo.setPackagename(base.getPackagename());
                            vo.setAppid(base.getAppid());
                            vo.setAppname(base.getAppname());
                            vo.setTappname(base.getTappname());
                            vo.setBindEndTime(base.getBindEndTime());
                            warnList.add(vo);
                        }
                        if (PlatformDataConstants.APPINFO.REJECT_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(vo.getState())){
                            // 审核驳回和运营打回
                            vo.setChannel(base.getChannel());
                            vo.setPackagename(base.getPackagename());
                            vo.setAppid(base.getAppid());
                            vo.setAppname(base.getAppname());
                            vo.setTappname(base.getTappname());
                            vo.setBindEndTime(base.getBindEndTime());

                            /** 审核驳回和运营打回  -孙文凤.*********/
                            rejectList.add(vo);
                        }
                    }
                }
                //更新正常的应用数据
                platformDataMapper.updatePlatformAppOhterInfo(dataList);

                //通知SDK告警
                if (warnList.size()>0){
                    PlatformDataConstants.sendSdkOnlineMessage(warnList);
                }
                if (rejectList.size()>0){
                    PlatformDataConstants.sendSdkRejectMessage(rejectList);
                }

                //更新匹配不正确的
                if (noMatchList.size()>0){
                    platformDataMapper.updatePlatformAppOhterInfo(noMatchList);
                }

            }
            if (PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_TOKEN_EXPARED.equals(state)
                    ||PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_UNKOWN_ERROR.equals(state)){
                //更新当前账号下所有应用未token失效
                platformDataMapper.updatePlatformAppInfoNotSuccessState(PlatformDataConstants.PLATFORM.XIAOMI,taccount,state);
                boolean enable = "1".equals(map.getValue().getOrDefault("status","").toString());
                if (enable) {
                    String company = ObjectUtils.isEmpty(map.getValue().get("company")) ? "" : map.getValue().get("company").toString();
                    String alertMsg = "小米渠道产品id关联配置数据爬取失败，媒体：xiaomi，报错账号：" + taccount + "-" + company + "，异常原因：token失效或未知错误，请及时关注！";
                    commonService.onceDailyAlertMsg("onceXiaomiPlatformAppInfo:"+taccount,alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                }
            }

        }
        logger.info("xiaomiPlatformAppInfoData end");
    }

    public void huaweiPlatformAppInfoData(String account){
        logger.info("huaweiPlatformAppInfoData start");
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setTaccount(account);
        query.setPlatform(PlatformDataConstants.PLATFORM.HUAWEI);
        List<PlatformAppInfoVo> list = platformDataMapper.getPlatformAppInfoList(query);
        Map<String,List<PlatformAppInfoVo>> listMap = list.stream().collect(Collectors.groupingBy(t->t.getTaccount()));
        //2024-12-03：将代码中adv_hw_platform_account表的使用替换为api_packet_platform_account表
        ApiPacketParaConfigParam configParam = new ApiPacketParaConfigParam();
        configParam.setChannel(DataTransUtils.transToSql(PlatformDataConstants.PLATFORM.HUAWEI));
        List<ApiPacketParaConfigResponseParam> hwConfigList = apiPacketParaConfigMapper.list(configParam);

        Map<String,ApiPacketParaConfigResponseParam> configMap = hwConfigList.stream().collect(Collectors.toMap(ApiPacketParaConfigResponseParam::getDn_account, Function.identity()));

        for (Map.Entry<String,List<PlatformAppInfoVo>> map:listMap.entrySet()){
            String taccount = map.getKey();
            List<PlatformAppInfoVo> eachList = map.getValue();
            List<PlatformAppInfoVo> baseList = BeanUtils.copyList(eachList);
            List<PlatformAppInfoVo> warnList = new ArrayList<>();
            List<PlatformAppInfoVo> rejectList = new ArrayList<>();
            List<PlatformAppInfoVo> h5WarnList = new ArrayList<>();
            ApiPacketParaConfigResponseParam config = configMap.get(taccount);

            JSONObject fetchRet = HuaweiPageDataServiceImpl.fetchHuaweiPlatformAppInfoData(eachList,config);
            if (fetchRet.size()==0){
                logger.warn("huaweiPlatformAppInfoData fail,account:"+taccount);
                continue;
            }
            List<PlatformAppInfoVo> dataList = (List<PlatformAppInfoVo>) fetchRet.get("data");
            if (dataList.size()>0){
                //比对老数据
                for (PlatformAppInfoVo vo:dataList){
                    String key = vo.getTappid();
                    PlatformAppInfoVo base = baseList.stream().filter(t->t.getTappid().equals(key)).findFirst().orElse(null);
                    if (base!=null&&vo.getState()!=null&&!vo.getState().equals(base.getState())){
                        vo.setStateTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                        //新增通知SDK状态变更为上线 //******** 新增定时发布状态下发配置工具，通知SDK状态变更 待发布
                        if (PlatformDataConstants.APPINFO.ONLINE_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.PUBLISH_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.OFFLINE_STATE.equals(vo.getState())){
                            vo.setChannel(base.getChannel());
                            vo.setPackagename(base.getPackagename());
                            vo.setAppid(base.getAppid());
                            vo.setAppname(base.getAppname());
                            vo.setTappname(base.getTappname());
                            warnList.add(vo);
                            h5WarnList.add(vo);
                        }

                        if (PlatformDataConstants.APPINFO.REJECT_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(vo.getState())){
                            // 审核驳回和运营打回
                            vo.setChannel(base.getChannel());
                            vo.setPackagename(base.getPackagename());
                            vo.setAppid(base.getAppid());
                            vo.setAppname(base.getAppname());
                            vo.setTappname(base.getTappname());
                            vo.setBindEndTime(base.getBindEndTime());

                            /** 审核驳回和运营打回  -孙文凤.*********/
                            rejectList.add(vo);
                            h5WarnList.add(vo);
                        }
                        if (PlatformDataConstants.APPINFO.OFFLINE_REJECT_STATE.equals(vo.getState())) {
                            h5WarnList.add(vo);
                        }
                    }
                }
                //直接更新状态
                platformDataMapper.updatePlatformAppOhterInfo(dataList);

                //通知SDK告警

                //通知SDK告警
                if (warnList.size()>0){
                    //暂时关掉小游戏的，包括 h5_huawei和 h5_huaweiyx的告警操作,后续通过sdk转发飞书群后需要取消这个限定
                    List<PlatformAppInfoVo> collect = warnList.stream().filter(data -> !"h5_huawei".equals(data.getChannel()) && !"h5_huaweiyx".equals(data.getChannel())).collect(Collectors.toList());
                    PlatformDataConstants.sendSdkOnlineMessage(collect);
                }
                if (rejectList.size()>0){
                    //暂时关掉小游戏的，包括 h5_huawei和 h5_huaweiyx的告警操作，后续通过sdk转发飞书群后需要取消这个限定
                    List<PlatformAppInfoVo> collect = rejectList.stream().filter(data -> !"h5_huawei".equals(data.getChannel()) && !"h5_huaweiyx".equals(data.getChannel())).collect(Collectors.toList());
                    PlatformDataConstants.sendSdkRejectMessage(collect);
                }
                if (h5WarnList.size()>0) {
                    //h5_huawei的状态变更需要发送消息至华为群里
                    List<PlatformAppInfoVo> sendList = h5WarnList.stream().filter(data -> "h5_huawei".equals(data.getChannel()) || "h5_huaweiyx".equals(data.getChannel())).collect(Collectors.toList());
                    sendMsgToChatByH5huawei(sendList);
                }
            }
        }
        logger.info("huaweiPlatformAppInfoData end");
    }

    /**
     * 子渠道为h5_huawei状态变更需要消息提醒至华为出包处理群
     * @param sendList 告警数据
     */
    private void sendMsgToChatByH5huawei(List<PlatformAppInfoVo> sendList) {
        if (CollectionUtils.isEmpty(sendList)) {
            return;
        }
        //测试群
        final String HUAWEI_CHAT_ID_TEST = "oc_8380fcf641abd15766a266d2b27ac298";
        //华为-快游戏
        final String HUAWEI_QUICK_GAME_CHAT_ID = "oc_ef8b0c35bb7db23792ef82beaf9311df";
        //华为-快应用包装H5游戏
        final String HUAWEI_QUICK_APP_CHAT_ID = "oc_3ac7a6db68266c611290f43dae472fbe";

        //发送状态变更消息至华为
        for (PlatformAppInfoVo appInfoVo : sendList) {
            //告警模板
            String temp = "\n" + "【产品名称】：%s\n" + "【渠道名称】：%s\n" + "【子渠道】：%s\n" + "【版本号】：%s\n";
            String format = String.format(temp, appInfoVo.getAppname(), appInfoVo.getTappname(), appInfoVo.getChannel(), appInfoVo.getVersion());
            //根据不同的状态赋值不同的消息标题和颜色
            String state = appInfoVo.getState();
            //根据子渠道的不同发送到不同的群和人
            String sendChatId;
            String userNames;
            if ("h5_huawei".equals(appInfoVo.getChannel())) {
                //h5_huawei的包发到 华为-快游戏，艾特任柏岭、梁萍、林小敏、谢润露
                sendChatId = HUAWEI_QUICK_GAME_CHAT_ID;
                //sendChatId = HUAWEI_CHAT_ID_TEST;
                userNames = "zhangmt,renbl,liangp,linxm,xierl";
            } else {
                //h5_huaweiyx的包发到华为-快应用包装H5游戏，艾特范进忠、张少峰、袁贺、梁萍、谢润露
                sendChatId = HUAWEI_QUICK_APP_CHAT_ID;
                //sendChatId = HUAWEI_CHAT_ID_TEST;
                //"zhangmt,liaozk,hehp"
                userNames = "zhangmt,fanjz,zhangsf,yuanhe,liangp,xierl";
            }
            if (PlatformDataConstants.APPINFO.ONLINE_STATE.equals(state)) {
                // 上线状态的需要发送变更通知
                String remark = PlatformDataConstants.sendSdkMiniGameOnlineMessage(appInfoVo);
                //添加上备注信息发送群消息
                String message = format + "【备注】：" + remark + "\n";
                //上线状态
                FeishuUtils.sendMsgCardToGroupRobot(sendChatId, RobotConstants.CONFIG_TOOL_ROBOT,message, "上线信息", "green", userNames);
            } else if (PlatformDataConstants.APPINFO.PUBLISH_STATE.equals(state)) {
                //定时发布
                FeishuUtils.sendMsgCardToGroupRobot(sendChatId, RobotConstants.CONFIG_TOOL_ROBOT,format, "定时发布信息", "blue", userNames);
            } else if (PlatformDataConstants.APPINFO.OFFLINE_STATE.equals(state)) {
                //下线信息
                FeishuUtils.sendMsgCardToGroupRobot(sendChatId, RobotConstants.CONFIG_TOOL_ROBOT,format, "下线信息", "red", userNames);
            } else if (PlatformDataConstants.APPINFO.REJECT_STATE.equals(state)) {
                //审核驳回
                FeishuUtils.sendMsgCardToGroupRobot(sendChatId, RobotConstants.CONFIG_TOOL_ROBOT,format, "审核驳回信息", "red", userNames);
            } else if (PlatformDataConstants.APPINFO.OFFLINE_REJECT_STATE.equals(state)) {
                //下线驳回
                FeishuUtils.sendMsgCardToGroupRobot(sendChatId, RobotConstants.CONFIG_TOOL_ROBOT,format, "下线驳回信息", "red", userNames);
            }
        }
    }

    public void honorPlatformAppInfoData(String account){
        logger.info("honorPlatformAppInfoData start");
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setTaccount(account);
        query.setPlatform(PlatformDataConstants.PLATFORM.HONOR);
        List<PlatformAppInfoVo> list = platformDataMapper.getPlatformAppInfoList(query);
        Map<String,List<PlatformAppInfoVo>> listMap = list.stream().collect(Collectors.groupingBy(t->t.getTaccount()));

        List<PlatformHonorConfigVo> configList = platformDataMapper.getPlatformHonorConfigList();

        Map<String,PlatformHonorConfigVo> configMap = configList.stream().collect(Collectors.toMap(PlatformHonorConfigVo::getAccount, Function.identity()));

        for (Map.Entry<String,List<PlatformAppInfoVo>> map:listMap.entrySet()){
            String taccount = map.getKey();
            List<PlatformAppInfoVo> eachList = map.getValue();
            List<PlatformAppInfoVo> baseList = BeanUtils.copyList(eachList);
            List<PlatformAppInfoVo> warnList = new ArrayList<>();
            List<PlatformAppInfoVo> rejectList = new ArrayList<>();
            PlatformHonorConfigVo config = configMap.get(taccount);

//            JSONObject fetchRet = fetchHonorPlatformAppInfoData(eachList,config);
//            if (fetchRet.size()==0){
//                logger.warn("honorPlatformAppInfoData fail,account:"+taccount);
//                continue;
//            }
            List<PlatformAppInfoVo> dataList = fetchHonorPlatformAppInfoData(eachList,config);
            if (dataList.size()>0){
                //比对老数据
                for (PlatformAppInfoVo vo:dataList){
                    String key = vo.getTappid();
                    PlatformAppInfoVo base = baseList.stream().filter(t->t.getTappid().equals(key)).findFirst().orElse(null);

                    if (base!=null&&vo.getState()!=null&&!vo.getState().equals(base.getState())){
                        logger.info("状态变化检测: tappid={}, 旧状态={}, 新状态={}",
                                vo.getTappid(), base.getState(), vo.getState());

                        vo.setStateTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
                        //新增通知SDK状态变更为上线
                        if (PlatformDataConstants.APPINFO.ONLINE_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.PUBLISH_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.OFFLINE_STATE.equals(vo.getState())){
                            vo.setChannel(base.getChannel());
                            vo.setPackagename(base.getPackagename());
                            vo.setAppid(base.getAppid());
                            vo.setAppname(base.getAppname());
                            vo.setTappname(base.getTappname());
                            warnList.add(vo);
                        }

                        // 只有当新状态是拒绝状态，且旧状态不是拒绝状态时，才添加到rejectList
                        if ((PlatformDataConstants.APPINFO.REJECT_STATE.equals(vo.getState())
                                || PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(vo.getState()))
                                && !PlatformDataConstants.APPINFO.REJECT_STATE.equals(base.getState())
                                && !PlatformDataConstants.APPINFO.REJECT_TWO_STATE.equals(base.getState())){
                            // 审核驳回和运营打回
                            vo.setChannel(base.getChannel());
                            vo.setPackagename(base.getPackagename());
                            vo.setAppid(base.getAppid());
                            vo.setAppname(base.getAppname());
                            vo.setTappname(base.getTappname());
                            vo.setBindEndTime(base.getBindEndTime());

                            /** 审核驳回和运营打回  -孙文凤.*********/
                            rejectList.add(vo);
                        }

                    }
                }
                //直接更新状态
                platformDataMapper.updatePlatformAppOhterInfo(dataList);

                //通知SDK告警

                //通知SDK告警
                if (warnList.size()>0){
                    PlatformDataConstants.sendSdkOnlineMessage(warnList);
                }
                if (rejectList.size()>0){
                    PlatformDataConstants.sendSdkRejectMessage(rejectList);
                }
            }
        }
        logger.info("honorPlatformAppInfoData end");
    }

    /**
     * put("0","未提交");
     *          put("1","已上线");
     *          put("2","已下线");
     *          put("3","未发布");
     *          put("4","自动化审核中");
     *          put("5","审核中");
     *          put("6","审核驳回");
     *          put("7","定时发布");
     *          put("8","资质审核");
     *          put("9","资质驳回");
     *          put("10","资质通过");
     *          put("11","已冻结");
     *          put("12","报备成功");
     *          put("13","撤销上线");
     *          put("14","测试中");
     *          put("15", "升级中");
     *          put("16", "下线审核");
     *          put("17", "未定义");
     *          put("18", "下线驳回");
     *          put("19", "审核通过");
     *          put("20", "运营打回");
     *          put("21", "运营通过");
     *          put("22", "其他");
     */
    HashMap<String, String> auditMap = new HashMap<String, String>(){{
        put("0", "5");
        put("1", "1");
        put("2", "6");
        put("3", "22");
        put("4", "0");
    }};
    private List<PlatformAppInfoVo> fetchHonorPlatformAppInfoData(List<PlatformAppInfoVo> eachList, PlatformHonorConfigVo config) {
        try {
            String clientId = config.getClient_id();
            String clientSecret = config.getClient_secret();
            HashMap<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/x-www-form-urlencoded");

            HashMap<String, String> param = new HashMap<>();
            param.put("grant_type", "client_credentials");
            param.put("client_id", clientId);
            param.put("client_secret", clientSecret);

            String tokenStr = HttpClientUtils.getInstance().httpPostThrow("https://iam.developer.honor.com/auth/token", param, header);
            String access_token = "";

            if (BlankUtils.isNotBlank(tokenStr)) {
                JSONObject jsonObject = JSONObject.parseObject(tokenStr);
                access_token = jsonObject.getString("access_token");
            }
            if (BlankUtils.isBlank(access_token)) {
                logger.error("荣耀拉取发版状态失败， token获取失败， {}", tokenStr);
                eachList.forEach(t -> t.setSyncState("2"));
                return eachList;
            }
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("Authorization", "Bearer " + access_token);
            headerMap.put("Content-Type", "application/json");

            for (PlatformAppInfoVo platformAppInfoVo : eachList) {
                String tappid = platformAppInfoVo.getTappid();

                String appidMsg = HttpClientUtils.getInstance().httpGetThrow(
                        "https://appmarket-openapi-drcn.cloud.honor.com/openapi/v1/publish/get-app-current-release?appId=" + tappid,
                        headerMap, true);
				System.out.println(DateTime.now().toLocalDateTime()+""+String.format(" tappid: %s, appidMsg: %s", tappid, appidMsg));

                if (BlankUtils.isNotBlank(appidMsg)) {
                    JSONObject jsonObject = JSONObject.parseObject(appidMsg);
                    if (jsonObject.getInteger("code") == 0) {
                        JSONObject object = jsonObject.getJSONObject("data");
                        String auditCode = object.getString("auditResult");
                        String status = auditMap.getOrDefault(auditCode, "22");
                        if (status.equals("22")) {
                            // 状态为22不进行处理
                            continue;
                        }

                        platformAppInfoVo.setState(status);
                        platformAppInfoVo.setVersion(object.getString("versionName"));
                        if (status.equals("6")) {
                            platformAppInfoVo.setRefuse_reason(object.getString("auditMessage"));
                        }
                        platformAppInfoVo.setSyncState("1");
                    } else {
                        platformAppInfoVo.setSyncState("2");
                    }
                }
            }
            return eachList;
        } catch (Exception e) {
            logger.error("荣耀拉取发版状态失败， 账户：{},  ", config.getAccount(), e);
            eachList.forEach(t -> t.setSyncState("3"));
            return eachList;
        }
    }


    /**
     * oppo评分数据拉取
     *
     * @return
     */
    public List<PlatformGradeDataVo> oppoGradeData() {

        List<PlatformGradeDataVo> list = new ArrayList<>();
        String tdate = DateTime.now().toString("yyyy-MM-dd");
        String sql = "select account as mapkey,tttoken from app_channel_config where channel = 'oppo' and status=1  ORDER BY createtime asc";
        Map<String, Map<String, Object>> tokenMap = adMapper.queryListMapOfKey(sql);

        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("oppo");
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);
        //此处只需要取多条配置中时间最大的配置
        Map<String,List<PlatformAppInfoVo>> appGroupMap = appList.stream().collect(Collectors.groupingBy(PlatformAppInfoVo::getTappid));

        List<PlatformAppInfoVo> queryAppList = new ArrayList<>();
        for (Map.Entry<String,List<PlatformAppInfoVo>> map:appGroupMap.entrySet()){
            List<PlatformAppInfoVo> apps = map.getValue();
            if (apps.size()>1){
                apps = apps.stream().sorted(Comparator.comparing(PlatformAppInfoVo::getBindEndTime)).collect(Collectors.toList());
                queryAppList.add(apps.get(apps.size()-1));
            }else {
                queryAppList.addAll(apps);
            }
        }


        Map<String, String> headMap = new HashMap<>();
        headMap.put(":scheme", "https");
        headMap.put("accept", "application/json, text/plain, */*");
        headMap.put("accept-encoding", "gzip, deflate, br");
        headMap.put("accept-language", "zh-CN,zh;q=0.9");
        headMap.put("cache-control", "no-cache");

        headMap.put("pragma", "no-cache");
        headMap.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.86 Safari/537.36");

        for (PlatformAppInfoVo app : appList) {
            String token = tokenMap.get(app.getTaccount()) != null && tokenMap.get(app.getTaccount()).get("tttoken") != null ? tokenMap.get(app.getTaccount()).get("tttoken") + "" : "";
            if (BlankUtils.checkBlank(token)) {
                continue;
            }
            //设置token
            headMap.put("cookie", token);
            String url = "https://open.oppomobile.com/resource/data/market?app_id=" + app.getTappid();
            String html = HttpClientUtils.getInstance().httpGet(url, headMap);
            Document doc = Jsoup.parse(html);
            //总评论次数
            String totalGradeTimes = "";
            Elements e_total_grade_times = doc.getElementsByClass("fontblod");
            if (e_total_grade_times != null) {
                Element element = e_total_grade_times.first();
                if (element != null) {
                    totalGradeTimes = element.text();
                }
            }
            //总评分数
            String totalGrade = "";
            Elements e_total_grade = doc.getElementsByClass("avg_member");
            for (Element e : e_total_grade) {
                totalGrade = e.text();
            }
            //1星评分人数
            //2星评分人数
            //3星评分人数
            //4星评分人数
            //5星评分人数
            String star1 = "";
            Elements e_star1 = doc.getElementsByClass("start_num1");
            for (Element e : e_star1) {
                star1 = e.text();
            }
            String star2 = "";
            Elements e_star2 = doc.getElementsByClass("start_num2");
            for (Element e : e_star2) {
                star2 = e.text();
            }
            String star3 = "";
            Elements e_star3 = doc.getElementsByClass("start_num3");
            for (Element e : e_star3) {
                star3 = e.text();
            }
            String star4 = "";
            Elements e_star4 = doc.getElementsByClass("start_num4");
            for (Element e : e_star4) {
                star4 = e.text();
            }
            String star5 = "";
            Elements e_star5 = doc.getElementsByClass("start_num5");
            for (Element e : e_star5) {
                star5 = e.text();
            }

            PlatformGradeDataVo po = new PlatformGradeDataVo();
            po.setTdate(tdate);
            po.setAppid(app.getAppid());
            po.setPlatform(app.getPlatform());
            po.setTappid(app.getTappid());
            po.setTotal_grade(totalGrade);
            po.setTotal_grade_times(totalGradeTimes);
            po.setStar1(star1);
            po.setStar2(star2);
            po.setStar3(star3);
            po.setStar4(star4);
            po.setStar5(star5);
            //只有有评分才记录
            if (!BlankUtils.checkBlank(totalGradeTimes)) {
                list.add(po);
            }
        }
        //发送拉取失败邮件
        if (list.size() > 0) {
            List<String> appids = list.stream().map(PlatformGradeDataVo::getAppid).collect(Collectors.toList());
            sendFailMail("oppo", "grade", appids, appList);
        }
        return list;
    }

    /**
     * xiaomi 页面数据拉取
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List xiaomiPageData(String startTime, String endTime,String account,String dataType) {
        List<PlatformPageDataVo> list = new ArrayList<>();
        List<PlatformReservationDataVo> reservationList = new ArrayList<>();

        String sql = "SELECT account,ttappid,ttparam,cname,company_type FROM yyhz_0308.app_channel_config WHERE channel = 'xiaomi' and status=1 ";
        if (!BlankUtils.checkBlank(account)){
            sql += "and account='"+account+"' ";
        }
        List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);
        Map<String, String> cookieMap = new HashMap<>();
        Map<String,String> useridMap = new HashMap<>();
        Map<String, String> comMap = new HashMap<>();
        Map<String, String> gameComMap = new HashMap<>();
        for (Map<String, Object> token : tokenList) {
            cookieMap.put(token.get("account").toString(), token.get("ttappid").toString());
            comMap.put(token.get("account").toString(), token.get("cname").toString());
            useridMap.put(token.get("account").toString(), token.get("ttparam")!=null?token.get("ttparam").toString():"");
            //gameComMap
            if ("游戏".equals(token.getOrDefault("company_type","").toString())) {
                gameComMap.put(token.get("account").toString(), token.get("cname").toString());
            }
        }
        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("xiaomi");
        //过滤包名为空的
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);
        if (appList.size()>0){
            if (!BlankUtils.checkBlank(account)){
                appList = appList.stream()
                        .filter(t->!BlankUtils.checkBlank(t.getPackagename())&&account.equals(t.getTaccount()))
                        .collect(Collectors.toList());
            }else {
                appList = appList.stream().filter(t->!BlankUtils.checkBlank(t.getPackagename())).collect(Collectors.toList());
            }

        }
        Map<String,PlatformPageDataVo> dataMap = new HashMap<>();
        Map<String,PlatformReservationDataVo> reservationDataMap = new HashMap<>();

        //告警数据存储，key：account,value:true/false
        Map<String,String> warnMap = new HashMap<>();

        Map<String,List<PlatformAppInfoVo>> appGroupMap = appList.stream().collect(Collectors.groupingBy(t->t.getTappid()));
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
        //分单条与多条配置
        for (Map.Entry<String,List<PlatformAppInfoVo>> map:appGroupMap.entrySet()){
            Map<String, PlatformPageDataVo> eachMap = new HashMap<>();
            Map<String,PlatformReservationDataVo> reservationEachMap = new HashMap<>();

            List<PlatformAppInfoVo> dataList = map.getValue();
            //处理一个平台产品id多个动能产品id
            TreeMap<String,PlatformAppInfoVo> bindEndTimeMap = new TreeMap<>();
            if (dataList.size()>0){
                dataList = dataList.stream().sorted(Comparator.comparing(PlatformAppInfoVo::getBindEndTime)).collect(Collectors.toList());
                for (PlatformAppInfoVo app:dataList){
                    bindEndTimeMap.put(app.getBindEndTime(),app);
                }
            }
            //多条配置
            if (dataList.size()>1){
                List<String> dateList = DateUtil.getDays(startTime,endTime);
                for (String dateStr:dateList){
                    DateTime date = DateTime.parse(dateStr,format);
                    for (Map.Entry<String,PlatformAppInfoVo> bindEndTime:bindEndTimeMap.entrySet()){
                        DateTime bindEndTimeDate = DateTime.parse(bindEndTime.getKey(),format).plusDays(1);
                        if (date.isBefore(bindEndTimeDate)){
                            PlatformAppInfoVo app = bindEndTime.getValue();
                            if ("page".equals(dataType)){
                                Map<String,PlatformPageDataVo> eachDateDataMap = XiaomiDataServiceImpl.xiaomiCommonPageDataHandle(app,cookieMap,useridMap,dateStr,dateStr);
                                if (eachDateDataMap.get(dateStr+"_"+app.getTappid())!=null){
                                    eachMap.put(dateStr+"_"+app.getTappid(),eachDateDataMap.get(dateStr+"_"+app.getTappid()));
                                }
                            }
                            if ("reservation".equals(dataType)){
                                Map<String,PlatformReservationDataVo> eachReservationDataMap = XiaomiDataServiceImpl.xiaomiCommonReservationDataHandle(app,cookieMap,useridMap,dateStr,dateStr);
                                if (eachReservationDataMap.get(dateStr+"_"+app.getTappid())!=null){
                                    reservationEachMap.put(dateStr+"_"+app.getTappid(),eachReservationDataMap.get(dateStr+"_"+app.getTappid()));
                                }
                            }
                            break;
                        }
                    }
                }
            }else {
                if ("page".equals(dataType)){
                    eachMap = XiaomiDataServiceImpl.xiaomiCommonPageDataHandle(dataList.get(0),cookieMap,useridMap,startTime,endTime);
                }
                if ("reservation".equals(dataType)){
                    reservationEachMap = XiaomiDataServiceImpl.xiaomiCommonReservationDataHandle(dataList.get(0),cookieMap,useridMap,startTime,endTime);
                }
            }
            //汇总结果
            if (eachMap.size()>0){
                for (Map.Entry<String,PlatformPageDataVo> eachDataMap:eachMap.entrySet()){
                    dataMap.put(eachDataMap.getKey(),eachDataMap.getValue());
                    warnMap.put(eachDataMap.getValue().getTaccount(),NO_WARN_FLAG);
                }
            } else {
                //需要告警：warnList comMap
                String taccount = dataList.get(0).getTaccount();
                if (gameComMap.containsKey(taccount) && !NO_WARN_FLAG.equals(warnMap.get(taccount))){
                    warnMap.put(dataList.get(0).getTaccount(),WARN_FLAG);
                }
            }

            if (reservationEachMap.size()>0){
                for (Map.Entry<String,PlatformReservationDataVo> eachDataMap:reservationEachMap.entrySet()){
                    reservationDataMap.put(eachDataMap.getKey(),eachDataMap.getValue());
                }
            }

        }
        if (dataMap.size() > 0) {
            for (Map.Entry<String, PlatformPageDataVo> map : dataMap.entrySet()) {
                PlatformPageDataVo mapVo = map.getValue();
                list.add(mapVo);
            }
        }

        if (reservationDataMap.size() > 0) {
            for (Map.Entry<String, PlatformReservationDataVo> map : reservationDataMap.entrySet()) {
                PlatformReservationDataVo mapVo = map.getValue();
                reservationList.add(mapVo);
            }
        }

        //发送拉取失败邮件
        //未拉取到数据账号告警操作
        if ("page".equals(dataType) && warnMap.containsValue(WARN_FLAG)) {
            for (Map.Entry<String, String> entry : warnMap.entrySet()) {
                if (WARN_FLAG.equals(entry.getValue())) {
                    //需要告警的主体
                    // api调用返回为空，为异常数据，爬取数据异常，需要告警通知
                    String taccount = entry.getKey();
                    String alertMsg = "xiaomi平台详情页分发分析数据爬取失败，媒体：xiaomi，报错账号：" + taccount + "-" + gameComMap.get(taccount) + "，媒体报错返回：爬取数据异常，请及时关注！";
                    commonService.onceDailyAlertMsg("onceXiaomiPageData:"+ taccount ,alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                }
            }
        }

        if (list.size() > 0) {
//            List<String> appids = list.stream().map(PlatformPageDataVo::getAppid).collect(Collectors.toList());
//            sendFailMail("xiaomi", "page", appids, appList);
            StringBuffer bf=new StringBuffer("渠道详情页转化分析数据拉取成功, ");
            bf.append("平台：xiaomi");
            for (PlatformPageDataVo data:list){
                String acc=data.getTaccount();
                bf.append(",账号：");
                bf.append(acc);
                bf.append(",主体：");
                bf.append(comMap.get("cname"));
            }
            try {
                HttpClientUtils.getInstance().httpGet(MSG_URL+URLEncoder.encode(bf.toString(),"UTF-8"));
            }catch (Exception e){
                logger.error("send Xiaomi Success error:",e);
            }
            return list;
        }

        if (reservationList.size()>0){
            List<String> appids = list.stream().map(PlatformPageDataVo::getAppid).collect(Collectors.toList());
            sendFailMail("xiaomi", "reservation", appids, appList);
            return reservationList;
        }
        return list;
    }

    /**
     * xiaomi评分数据拉取
     *
     * @return
     */
    public List<PlatformGradeDataVo> xiaomiGradeData() {
        List<PlatformGradeDataVo> list = new ArrayList<>();

        String tdate = DateTime.now().toString("yyyy-MM-dd");

        String sql = "SELECT account,token  FROM yyhz_0308.app_channel_config WHERE channel = 'xiaomi'";
        List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);
        Map<String, String> tokenMap = new HashMap<>();
        for (Map<String, Object> token : tokenList) {
            tokenMap.put(token.get("account").toString(), token.get("token").toString());
        }

        List<Map> xiaomiAppList = getXiaomiAppList(tokenList);

        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("xiaomi");
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);

        Map<String, PlatformAppInfoVo> appMap = appList.stream().collect(Collectors.toMap(PlatformAppInfoVo::getTappid, Function.identity(), (key1, key2) -> key2));
        for (Map map : xiaomiAppList) {
            String tappId = map.get("appId") + "";
            String packageName = map.get("packageName") + "";

            String appid = appMap.get(tappId) != null ? appMap.get(tappId).getAppid() : "";
            if (BlankUtils.checkBlank(appid)) {
                continue;
            }

            String account = appMap.get(tappId) != null ? appMap.get(tappId).getTaccount() : "";
            if (BlankUtils.checkBlank(account)) {
                continue;
            }
            String cookie = tokenMap.get(account) + "";
            if (BlankUtils.checkBlank(cookie)) {
                continue;
            }
            String userId = appMap.get(tappId) != null ? appMap.get(tappId).getTaccount() : "";
            if (BlankUtils.checkBlank(userId)) {
                continue;
            }
            List<PlatformGradeDataVo> gradeDataList = getXiaomiGrardeDataList(tdate, appid, tappId, userId, packageName, cookie);
            if (gradeDataList.size() > 0) {
                list.addAll(gradeDataList);
            }
        }



        //发送拉取失败邮件
        if (list.size() > 0) {
            List<String> appids = list.stream().map(PlatformGradeDataVo::getAppid).collect(Collectors.toList());
            sendFailMail("xiaomi", "grade", appids, appList);
        }
        return list;
    }


    /**
     * 获取小米平台应用数据 结构 appId,packageName
     *
     * @param tokenList
     * @return
     */
    public List<Map> getXiaomiAppList(List<Map<String, Object>> tokenList) {
        List<Map> appList = new ArrayList<>();
        try {
            for (Map<String, Object> map : tokenList) {
                String userId = map.get("ttparam") + "";
                String cookie = map.get("token") + "";
                Integer pageNum = 1;
                String url = "https://dev.mi.com/uiueapi/myitems/0/0?pageSize=10&statusType=0&userId=" + userId + "&pageNum=" + (pageNum - 1);
                Map<String, String> headMap = new HashMap<>();
                headMap.put("cookie", URLEncoder.encode(cookie,"utf-8"));
                Boolean hasNext = true;
                while (hasNext) {
                    String result = HttpRequest.get(url, headMap);
                    if (!BlankUtils.checkBlank(result)) {
                        JSONObject ret = JSONObject.parseObject(result);
                        if ("0".equals(ret.getString("code"))) {
                            JSONObject data = ret.getJSONObject("data");
                            Integer totalPage = Integer.parseInt(data.getString("totalPage"));
                            if (pageNum >= totalPage) {
                                hasNext = false;
                            }
                            //具体应用列表
                            JSONArray itemArray = data.getJSONArray("appItemList");
                            for (Object obj : itemArray) {
                                JSONObject json = (JSONObject) obj;
                                appList.add(json);
                            }
                            pageNum++;
                        } else {
                            hasNext = false;
                        }
                    } else {
                        hasNext = false;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("getXiaomiAppList error:", e);
        }
        return appList;
    }


    /**
     * 获取评分信息
     *
     * @param tdate
     * @param appid
     * @param tappid
     * @param userId
     * @param packageName
     * @param cookie
     * @return
     */
    public List<PlatformGradeDataVo> getXiaomiGrardeDataList(String tdate, String appid, String tappid,
                                                             String userId, String packageName, String cookie) {
        List<PlatformGradeDataVo> list = new ArrayList<>();
        Map<String, String> headMap = new HashMap<>();
        headMap.put("cookie", cookie);
        String url = "https://dev.mi.com/uiueapi/comment/usercomment/scores?packageName=" + packageName + "&userId=" + userId;
        String result = HttpRequest.get(url, headMap);
        if (!BlankUtils.checkBlank(result)) {
            try {
                JSONObject ret = JSONObject.parseObject(result);
                if ("0".equals(ret.getString("code"))) {
                    JSONObject data = ret.getJSONObject("data");
                    JSONObject score = data.getJSONObject("score");
                    PlatformGradeDataVo vo = new PlatformGradeDataVo();
                    vo.setAppid(appid);
                    vo.setTappid(tappid);
                    vo.setTdate(tdate);
                    vo.setPlatform("xiaomi");
                    vo.setTotal_grade(score.getString("score"));
                    vo.setTotal_grade_times(score.getString("count"));
                    vo.setStar1(score.getString("count1"));
                    vo.setStar2(score.getString("count2"));
                    vo.setStar3(score.getString("count3"));
                    vo.setStar4(score.getString("count4"));
                    vo.setStar5(score.getString("count5"));
                    if (!BlankUtils.checkBlank(vo.getTotal_grade())) {
                        list.add(vo);
                    }
                }
            } catch (Exception e) {
                logger.error("getXiaomiGrardeDataList error:", e);
            }
        }

        return list;
    }

    /**
     * 统一发送拉取数据失败邮件
     *
     * @param platform
     * @param appList
     * @param appList
     */
    public void sendFailMail(String platform, String dataType, List<String> appids, List<PlatformAppInfoVo> appList) {
        try {
            Address[] addresses = new Address[]{
                    new InternetAddress("<EMAIL>")
            };
            String description = "";
            if (dataType.equals("page")) {
                description = "-详情页分发分析数据-";
                sendWithData(addresses,platform,description,appids,appList);
            } else if (dataType.equals("grade")) {
                description = "-渠道评分数据-";
                sendWithData(addresses,platform,description,appids,appList);
            }else if (dataType.equals("appinfo")){
                description = "-渠道产品id关联配置-拉取应用信息-";
                sendWithMessageOnly(addresses,platform,description);
            }
        }catch (Exception e){

        }

    }

    private void sendWithMessageOnly(Address[] addresses,String platform,String description){

    }

    private void sendWithData(Address[] addresses,String platform,String description,
                              List<String> appids, List<PlatformAppInfoVo> appList){
        //发送拉取失败产品邮件
        try {
            List<Map<String, Object>> nameList = wbConfigMapper.selectAppInfoList(" where app_category = '3' ");
            List<String> appliactionList = new ArrayList<>();
            for (Map<String, Object> map : nameList) {
                appliactionList.add(map.get("id").toString());
            }
            //过滤应用产品
            List<PlatformAppInfoVo> pList = appList.stream().filter(t -> !appids.contains(t.getAppid()) && !appliactionList.contains(t.getAppid())).collect(Collectors.toList());

            if (pList != null && pList.size() > 0) {

                StringBuffer sb = new StringBuffer();
                sb.append("<table>");
                sb.append("<tr><td><span style='color:red'>平台:" + platform + description + "未拉取到数据产品：</span></td></tr>");
                for (PlatformAppInfoVo to : pList) {
                    sb.append("<tr><td><span>产品id:" + to.getAppid() + ",渠道产品名称:" + to.getTappname() + "</span></td></tr>");
                }
                sb.append("</table>");
                String mailState = MailToolTwo.sendMails(sb.toString(), addresses);
                logger.info("sendFailMail mailState:" + mailState);
            }
        } catch (Exception e) {
            logger.error("sendFailMail error:", e);
        }
    }




    /**
     * 拉取华为详情页数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List<PlatformPageDataVo> huaweiPageData(String startTime, String endTime) {
        List<PlatformPageDataVo> list = new ArrayList<>();
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("huawei");

        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);
        Map<String, String> headMap = new HashMap<>();

        String sql = "SELECT tttoken, account, ttparam,ttappid  FROM yyhz_0308.app_channel_config WHERE channel = 'huawei'";
        List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);
        //cookie
        Map<String, String> tokenMap = new HashMap<>();
        //X-HD-CSRF + agcTeamId
        Map<String, String> paramMap = new HashMap<>();
        for (Map<String, Object> token : tokenList) {
            tokenMap.put(token.get("account").toString(), token.get("tttoken").toString());
            paramMap.put(token.get("account").toString(), token.get("ttparam").toString() + "_" + token.get("ttappid").toString());
        }
        String url = "https://agc-drcn.developer.huawei.com/agc/edge/rs/distribution-operation-quality/v1/appDownloadAnalysis?language=zh-CN&" +
                "groupBy=date&filterCondition=countryId&" +
                "filterConditionValue=CN%2CAL%2CAD%2CAT%2CBE%2CBA%2CBG%2CHR%2CCY%2CCZ%2CDK%2CEE%2CFO%2CFI%2CFR%2CDE%2CGI%2CGR%2CGL%2CHU%2CIS%2CIE%2CIT%2CLV%2CLI%2CLT%2CLU%2CMK%2CMT%2CMD%2CMC%2CME%2CNL%2CNO%2CPL%2CPT%2CRO%2CRU%2CSM%2CRS%2CSK%2CSI%2CES%2CSE%2CCH%2CGB%2CVA%2CDZ%2CAO%2CMA%2CSS%2CAI%2CVC%2CBJ%2CBW%2CBF%2CBI%2CCM%2CCV%2CCF%2CTD%2CKM%2CCD%2CDJ%2CEG%2CGQ%2CER%2CET%2CGA%2CGM%2CGH%2CGN%2CGW%2CCI%2CKE%2CLS%2CLR%2CLY%2CMG%2CMW%2CML%2CMR%2CMU%2CYT%2CMZ%2CNA%2CNE%2CNG%2CCG%2CRE%2CRW%2CST%2CSN%2CSC%2CSL%2CSO%2CZA%2CSZ%2CTZ%2CTG%2CTN%2CUG%2CYE%2CZM%2CZW%2CAF%2CBH%2CIQ%2CIL%2CJO%2CKW%2CLB%2COM%2CPK%2CPS%2CQA%2CSA%2CAE%2CCA%2CAM%2CAZ%2CBY%2CGE%2CKZ%2CKG%2CMN%2CTJ%2CTR%2CTM%2CUA%2CUZ%2CKR%2CAU%2CAS%2CVU%2CBD%2CBN%2CKH%2CCK%2CFJ%2CPF%2CHK%2CIN%2CID%2CJP%2CLA%2CMO%2CMY%2CMV%2CMM%2CNR%2CNP%2CNZ%2CPG%2CPH%2CSG%2CSB%2CLK%2CTW%2CTH%2CTO%2CVN%2CAG%2CAR%2CAW%2CBS%2CBB%2CBZ%2CBO%2CBR%2CKY%2CCL%2CCO%2CCR%2CDO%2CEC%2CSV%2CGF%2CGD%2CGP%2CGT%2CGY%2CHT%2CHN%2CJM%2CMQ%2CMX%2CMS%2CAN%2CNI%2CPA%2CPY%2CPE%2CPR%2CLC%2CSR%2CTT%2CUY%2CVE%2CVG%2Cothers&" +
                "startTime=" + startTime.replace("-", "") + "&endTime=" + endTime.replace("-", "");
        try {
            for (PlatformAppInfoVo app : appList) {
                headMap.put("appId", app.getTappid());
                String cookie = tokenMap.get(app.getTaccount());
                String csrf = paramMap.get(app.getTaccount()).split("_")[0];
                String agcTeamId = paramMap.get(app.getTaccount()).split("_")[1];
                headMap.put("X-HD-CSRF", csrf);
                headMap.put("Cookie", cookie);
                headMap.put("agcTeamId", agcTeamId);
                String result = HttpRequest.get(url, headMap);
                try {
                    if (!BlankUtils.checkBlank(result)) {
                        JSONObject ret = JSONObject.parseObject(result);
                        if ("0".equals(ret.getJSONObject("ret").getString("code"))) {
                            JSONArray dataArray = ret.getJSONArray("overallAnalysisList");
                            if (dataArray != null) {
                                for (Object obj : dataArray) {
                                    JSONObject data = (JSONObject) obj;
                                    DateTime dateTime = DateTime.parse(data.getString("time"), format);
                                    String tdate = dateTime.toString("yyyy-MM-dd");
                                    PlatformPageDataVo po = new PlatformPageDataVo();
                                    po.setTdate(tdate);
                                    po.setPlatform("huawei");
                                    po.setAppid(app.getAppid());
                                    po.setTappid(app.getTappid());
                                    po.setNet_download(data.getString("downCnt"));
                                    po.setUpdate_download(data.getString("updateDownCnt"));
                                    String detail_transform = "";
                                    String nonUpdateDownCnt = data.getString("nonUpdateDownCnt");
                                    String detailsPageCnt = data.getString("detailsPageCnt");
                                    if (!BlankUtils.checkBlank(detailsPageCnt) && !BlankUtils.checkBlank(nonUpdateDownCnt)) {
                                        BigDecimal transform = new BigDecimal(nonUpdateDownCnt).divide(new BigDecimal(detailsPageCnt), 4, RoundingMode.HALF_UP);
                                        detail_transform = transform.toString();
                                    }
                                    po.setDetail_transform(detail_transform);
                                    po.setInstall_rate(data.getString("installSuccessRate"));
                                    list.add(po);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    continue;
                }
            }
            //发送拉取失败邮件
            if (list.size() > 0) {
                List<String> appids = list.stream().map(PlatformPageDataVo::getAppid).collect(Collectors.toList());
                sendFailMail("huawei", "page", appids, appList);
            }
        } catch (Exception e) {
            logger.error("huaweiPageData error:", e);
        }
        return list;
    }

    /**
     * 拉取华为详情页数据
     *
     * @return
     */
    public List<PlatformGradeDataVo> huaweiGradeData() {
        List<PlatformGradeDataVo> list = new ArrayList<>();
        String tdate = DateTime.now().toString("yyyy-MM-dd");
        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("huawei");

        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);
        Map<String, String> headMap = new HashMap<>();

        String sql = "SELECT tttoken, account, ttparam,ttappid  FROM yyhz_0308.app_channel_config WHERE channel = 'huawei'";
        List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);
        //cookie
        Map<String, String> tokenMap = new HashMap<>();
        //X-HD-CSRF + agcTeamId
        Map<String, String> paramMap = new HashMap<>();
        for (Map<String, Object> token : tokenList) {
            tokenMap.put(token.get("account").toString(), token.get("tttoken").toString());
            paramMap.put(token.get("account").toString(), token.get("ttparam").toString() + "_" + token.get("ttappid").toString());
        }

        try {
            for (PlatformAppInfoVo app : appList) {
                String url = "https://agc-drcn.developer.huawei.com/agc/edge/review/v1/manage/developer/ratingStat?entityType=1&entityId=" + app.getTappid();
                headMap.put("appId", app.getTappid());
                String cookie = tokenMap.get(app.getTaccount());
                String csrf = paramMap.get(app.getTaccount()).split("_")[0];
                String agcTeamId = paramMap.get(app.getTaccount()).split("_")[1];
                headMap.put("X-HD-CSRF", csrf);
                headMap.put("Cookie", cookie);
                headMap.put("agcTeamId", agcTeamId);
                String result = HttpRequest.get(url, headMap);
                try {
                    if (!BlankUtils.checkBlank(result)) {
                        JSONObject ret = JSONObject.parseObject(result);
                        if ("0".equals(ret.getJSONObject("ret").getString("rtnCode"))) {
                            JSONObject data = ret.getJSONObject("data");
                            if (data != null) {
                                PlatformGradeDataVo po = new PlatformGradeDataVo();
                                po.setTdate(tdate);
                                po.setPlatform("huawei");
                                po.setAppid(app.getAppid());
                                po.setTappid(app.getTappid());
                                po.setTotal_grade(data.getString("averageScore"));
                                po.setTotal_grade_times(data.getString("totalRatings"));
                                po.setStar1(data.getString("oneStarTimes"));
                                po.setStar2(data.getString("twoStarTimes"));
                                po.setStar3(data.getString("threeStarTimes"));
                                po.setStar4(data.getString("fourStarTimes"));
                                po.setStar5(data.getString("fiveStarTimes"));
                                list.add(po);
                            }
                        }
                    }
                } catch (Exception e) {
                    continue;
                }
            }
            //发送拉取失败邮件
            if (list.size() > 0) {
                List<String> appids = list.stream().map(PlatformGradeDataVo::getAppid).collect(Collectors.toList());
                sendFailMail("huawei", "grade", appids, appList);
            }
        } catch (Exception e) {
            logger.error("huaweiGradeData error:", e);
        }
        return list;
    }

    /**
     * @param startTime
     * @param endTime
     * @return
     */
    public List vivoPageData(String startTime, String endTime,String account,String dataType) {
        List<PlatformPageDataVo> list = new ArrayList<>();
        List<PlatformReservationDataVo> reservationList = new ArrayList<>();

        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("vivo");
        vo.setTaccount(account);
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);
        String sql = "SELECT tttoken, account, ttparam,cname,company_type FROM yyhz_0308.app_channel_config WHERE channel = 'vivo' and status=1 ";
        if (!BlankUtils.checkBlank(account)){
            sql = sql + " and account = '"+account+"' ";
        }
        List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);
        Map<String, String> tokenMap = new HashMap<>();
        Map<String, String> crsfMap = new HashMap<>();
        Map<String, String> comMap = new HashMap<>();
        Map<String, String> gameComMap = new HashMap<>();
        for (Map<String, Object> token : tokenList) {
            comMap.put(token.get("account").toString(), token.get("cname").toString());
            tokenMap.put(token.get("account").toString(), token.get("tttoken").toString());
            crsfMap.put(token.get("account").toString(), token.get("ttparam") != null ? token.get("ttparam").toString() : "");
            //gameComMap
            if ("游戏".equals(token.getOrDefault("company_type","").toString())) {
                gameComMap.put(token.get("account").toString(), token.get("cname").toString());
            }
        }

        Map<String,List<PlatformAppInfoVo>> appGroupMap = appList.stream().collect(Collectors.groupingBy(t->t.getTappid()));
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");

        Map<String,PlatformPageDataVo> dataMap = new HashMap<>();
        Map<String,PlatformReservationDataVo> reservationDataMap = new HashMap<>();

        //告警数据存储，key：account,value:true/false
        Map<String,String> warnMap = new HashMap<>();
        //分单条与多条配置
        for (Map.Entry<String,List<PlatformAppInfoVo>> map:appGroupMap.entrySet()){
            Map<String, PlatformPageDataVo> eachMap = new HashMap<>();
            Map<String, PlatformReservationDataVo> eachReservationMap = new HashMap<>();
            List<PlatformAppInfoVo> dataList = map.getValue();
            //处理一个平台产品id多个动能产品id
            TreeMap<String,PlatformAppInfoVo> bindEndTimeMap = new TreeMap<>();
            if (dataList.size()>0){
                dataList = dataList.stream().sorted(Comparator.comparing(PlatformAppInfoVo::getBindEndTime)).collect(Collectors.toList());
                for (PlatformAppInfoVo app:dataList){
                    bindEndTimeMap.put(app.getBindEndTime(),app);
                }
            }
            //多条配置
            if (dataList.size()>1){
                List<String> dateList = DateUtil.getDays(startTime,endTime);
                for (String dateStr:dateList){
                    DateTime date = DateTime.parse(dateStr,format);
                    for (Map.Entry<String,PlatformAppInfoVo> bindEndTime:bindEndTimeMap.entrySet()){
                        DateTime bindEndTimeDate = DateTime.parse(bindEndTime.getKey(),format).plusDays(1);
                        if (date.isBefore(bindEndTimeDate)){
                            PlatformAppInfoVo app = bindEndTime.getValue();
                            if ("page".equals(dataType)){
                                Map<String,PlatformPageDataVo> eachDateDataMap = VivoPageDataServiceImpl.vivoCommonPageDataHandle(app,dateStr,dateStr,tokenMap);
                                if (eachDateDataMap.get(dateStr+"_"+app.getTappid())!=null){
                                    eachMap.put(dateStr+"_"+app.getTappid(),eachDateDataMap.get(dateStr+"_"+app.getTappid()));
                                }
                            }
                            if ("reservation".equals(dataType)){
                                Map<String,PlatformReservationDataVo> eachReservationDataMap = VivoPageDataServiceImpl.vivoCommonReservationDataHandle(app,dateStr,dateStr,tokenMap);
                                if (eachReservationDataMap.get(dateStr+"_"+app.getTappid())!=null){
                                    eachReservationMap.put(dateStr+"_"+app.getTappid(),eachReservationDataMap.get(dateStr+"_"+app.getTappid()));
                                }
                            }
                            break;
                        }
                    }
                }
            }else {
                if ("page".equals(dataType)){
                    eachMap = VivoPageDataServiceImpl.vivoCommonPageDataHandle(dataList.get(0),startTime,endTime,tokenMap);
                }
                if ("reservation".equals(dataType)){
                    eachReservationMap = VivoPageDataServiceImpl.vivoCommonReservationDataHandle(dataList.get(0),startTime,endTime,tokenMap);;
                }
            }
            //汇总结果
            if (eachMap.size()>0){
                for (Map.Entry<String,PlatformPageDataVo> eachDataMap:eachMap.entrySet()){
                    dataMap.put(eachDataMap.getKey(),eachDataMap.getValue());
                    warnMap.put(eachDataMap.getValue().getTaccount(),NO_WARN_FLAG);
                }
            } else {
                //需要告警：warnMap
                String taccount = dataList.get(0).getTaccount();
                if (gameComMap.containsKey(taccount) && !NO_WARN_FLAG.equals(warnMap.get(taccount))){
                    warnMap.put(dataList.get(0).getTaccount(),WARN_FLAG);
                }
            }

            if (eachReservationMap.size()>0){
                for (Map.Entry<String,PlatformReservationDataVo> eachDataMap:eachReservationMap.entrySet()){
                    reservationDataMap.put(eachDataMap.getKey(),eachDataMap.getValue());
                }
            }
        }

        if (dataMap.size() > 0) {
            for (Map.Entry<String, PlatformPageDataVo> map : dataMap.entrySet()) {
                PlatformPageDataVo mapVo = map.getValue();
                list.add(mapVo);
            }
        }

        if (reservationDataMap.size() > 0) {
            for (Map.Entry<String, PlatformReservationDataVo> map : reservationDataMap.entrySet()) {
                PlatformReservationDataVo mapVo = map.getValue();
                reservationList.add(mapVo);
            }
        }
        //失败告警
        //未拉取到数据账号告警操作
        if ("page".equals(dataType) && warnMap.containsValue(WARN_FLAG)) {
            for (Map.Entry<String, String> entry : warnMap.entrySet()) {
                if (WARN_FLAG.equals(entry.getValue())) {
                    //需要告警的主体
                    String taccount = entry.getKey();
                    String alertMsg = "vivo平台详情页分发分析数据爬取失败，媒体：vivo，报错账号：" + taccount + "-" + gameComMap.get(taccount) + "，媒体报错返回：爬取数据异常，请及时关注！";
                    commonService.onceDailyAlertMsg("onceVivoPageData:"+ taccount ,alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                }
            }
        }
        if ("page".equals(dataType)){
//            List<String> appids = list.stream().map(PlatformPageDataVo::getAppid).collect(Collectors.toList());
//            sendFailMail("vivo", "page", appids, appList);
            if(list.size()>0){
                StringBuffer bf=new StringBuffer("渠道详情页转化分析数据拉取成功,");
                bf.append("平台：vivo");
                for (PlatformPageDataVo data:list){
                    String acc=data.getTaccount();
                    bf.append(",账号：");
                    bf.append(acc);
                    bf.append(",主体：");
                    bf.append(comMap.get("cname"));
                }
                HttpClientUtils.getInstance().httpGet(MSG_URL+bf.toString());
            }
            return list;
        }

        if ("reservation".equals(dataType)){
            List<String> appids = reservationList.stream().map(PlatformReservationDataVo::getAppid).collect(Collectors.toList());
            sendFailMail("vivo", "reservation", appids, appList);
            return reservationList;
        }
        return list;
    }






    public static String secondToHHmmss(String minute) {
        String result = "00:00:00";
        try {
            int seconds = Integer.parseInt(minute);
            int temp = 0;
            StringBuffer sb = new StringBuffer();
            temp = seconds / 3600;
            sb.append((temp < 10) ? "0" + temp + ":" : "" + temp + ":");

            temp = seconds % 3600 / 60;
            sb.append((temp < 10) ? "0" + temp + ":" : "" + temp + ":");

            temp = seconds % 3600 % 60;
            sb.append((temp < 10) ? "0" + temp : "" + temp);
            result = sb.toString();
        } catch (Exception e) {
            logger.error("secondToHHmmss error:", e);
        }
        return result;
    }


    /**
     * 华为小游戏渠道详情页数据拉取
     * @param startTime
     * @param endTime
     * @param account
     * @return
     */
    private List<PlatformPageDataVo> syncHuaweiQuickGameList(String startTime, String endTime, String account) {
        List<PlatformPageDataVo> storeList = new ArrayList<>();
        logger.info("syncHuaweiQuickGame starting");
        //查询oppo渠道下小游戏的appid
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setPlatform(PlatformDataConstants.PLATFORM.HUAWEI);
        //华为小游戏:华为快游戏-应用-57,华为快游戏-游戏-45
        query.setApp_category("45,57");
        query.setTaccount(account);
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(query);
        //分组
        Map<String, List<PlatformAppInfoVo>> appGroupMap = appList.stream().collect(Collectors.groupingBy(PlatformAppInfoVo::getTappid));
        //获取拉取数据的账号信息
        String sql = "select account as mapkey,tttoken,ttappid,ttparam,cname from app_channel_config where channel = 'huawei' and status=1 ";
        if (!BlankUtils.checkBlank(account)) {
            sql = sql + " and account ='" + account + "' ";
        }
        Map<String, Map<String, Object>> tokenMap = adMapper.queryListMapOfKey(sql);

        //分单条与多条配置  一个tappid可能存在对应多个appid
        for (Map.Entry<String, List<PlatformAppInfoVo>> entry : appGroupMap.entrySet()) {
            List<PlatformAppInfoVo> dataList = entry.getValue();
            //数据日期处理--存在多条数据时需要根据最大绑定时间进行拆分对应的日期
            Map<String, PlatformAppInfoVo> spiltAppInfoMap = spiltDateFromEndTime(dataList, startTime, endTime);
            //拉取oppo渠道的小游戏相关数据
            for (Map.Entry<String, PlatformAppInfoVo> appInfoEntry : spiltAppInfoMap.entrySet()) {
                PlatformAppInfoVo appInfo = appInfoEntry.getValue();
                String startDate = appInfo.getStart_state_time();
                String endDate = appInfo.getEnd_state_time();
                if (!tokenMap.containsKey(appInfo.getTaccount())) {
                    continue;
                }
                Map<String, Object> accountMap = tokenMap.get(appInfo.getTaccount());
                List<PlatformPageDataVo> fetchHuaweiData = null;
                try {
                    fetchHuaweiData = HuaweiPageDataServiceImpl.fetchHuaweiQuickGameData(appInfo, startDate, endDate, accountMap);
                    storeList.addAll(fetchHuaweiData);
                } catch (ApiException e) {
                    logger.error("华为小游戏拉取渠道详情页异常，异常原因：" + e.getMessage());
                    String alertMsg = "h5_huawei渠道详情页转化分析数据拉取失败，媒体：huawei，报错账号：" + appInfo.getTaccount() + "-" + accountMap.get("cname") + "，异常原因："+e.getMessage()+"，请及时关注！";;
                    // 飞书通知异常
                    commonService.onceDailyAlertMsg("oncePageH5Hw" + appInfo.getTaccount(),alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                } catch (Exception e) {
                    //未知异常。发送飞书通知
                    logger.error("华为小游戏拉取渠道详情页异常，异常原因：未知异常," + e);
                    //发送飞书通知
                    String alertMsg = "h5_huawei渠道详情页转化分析数据拉取失败，媒体：huawei，报错账号：" + appInfo.getTaccount() + "-" + accountMap.get("cname") + "，异常原因：token失效或未知错误，请及时关注！";;
                    commonService.onceDailyAlertMsg("oncePageH5Hw" + appInfo.getTaccount(),alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                }
            }
        }
        logger.info("syncHuaweiQuickGame end");
        return storeList;
    }


    public List huaweiPageDataNew(String startTime, String endTime,String account,String dataType) {
        List<PlatformPageDataVo> list = new ArrayList<>();
        List<PlatformReservationDataVo> reservationList = new ArrayList<>();
        //文件临时路径
        Environment environment = ApplicationContextUtils.get(Environment.class);
        String savePath = environment.getProperty("commonPath");

        PlatformAppInfoVo vo = new PlatformAppInfoVo();
        vo.setPlatform("huawei");
        vo.setTaccount(account);
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(vo);
        Map<String,List<PlatformAppInfoVo>> appGroupMap = appList.stream().collect(Collectors.groupingBy(t->t.getTappid()));

        List<PlatformHwConfigVo> hwConfig = platformDataMapper.getPlatformHwConfigList();
        Map<String, PlatformHwConfigVo> hwConfigMap = hwConfig.stream().collect(Collectors.toMap(PlatformHwConfigVo::getDn_account, Function.identity()));

        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");

        Map<String,PlatformPageDataVo> dataMap = new HashMap<>();
        Map<String,PlatformReservationDataVo> reservationDataMap = new HashMap<>();
        //分单条与多条配置
        for (Map.Entry<String,List<PlatformAppInfoVo>> map:appGroupMap.entrySet()){
            Map<String, PlatformPageDataVo> eachMap = new HashMap<>();
            Map<String,PlatformReservationDataVo> reservationEachMap = new HashMap<>();
            List<PlatformAppInfoVo> dataList = map.getValue();
            //处理一个平台产品id多个动能产品id
            TreeMap<String,PlatformAppInfoVo> bindEndTimeMap = new TreeMap<>();
            if (dataList.size()>0){
                dataList = dataList.stream().sorted(Comparator.comparing(PlatformAppInfoVo::getBindEndTime)).collect(Collectors.toList());
                for (PlatformAppInfoVo app:dataList){
                    bindEndTimeMap.put(app.getBindEndTime(),app);
                }
            }
            //多条配置
            if (dataList.size()>1){
                List<String> dateList = DateUtil.getDays(startTime,endTime);
                for (String dateStr:dateList){
                    DateTime date = DateTime.parse(dateStr,format);
                    for (Map.Entry<String,PlatformAppInfoVo> bindEndTime:bindEndTimeMap.entrySet()){
                        DateTime bindEndTimeDate = DateTime.parse(bindEndTime.getKey(),format).plusDays(1);
                        if (date.isBefore(bindEndTimeDate)){
                            PlatformAppInfoVo app = bindEndTime.getValue();
                            if ("page".equals(dataType)){
                                Map<String,PlatformPageDataVo> eachDateDataMap = huaweiCommonPageDataHandle(app,dateStr,dateStr,hwConfigMap,savePath);
                                if (eachDateDataMap.get(dateStr+"_"+app.getTappid())!=null){
                                    eachMap.put(dateStr+"_"+app.getTappid(),eachDateDataMap.get(dateStr+"_"+app.getTappid()));
                                }
                            }

                            if ("reservation".equals(dataType)){
                                Map<String,PlatformReservationDataVo> eachReservationDateDataMap = huaweiCommonReservationDataHandle(app,dateStr,dateStr,hwConfigMap,savePath);
                                if (eachReservationDateDataMap.get(dateStr+"_"+app.getTappid())!=null){
                                    reservationEachMap.put(dateStr+"_"+app.getTappid(),eachReservationDateDataMap.get(dateStr+"_"+app.getTappid()));
                                }
                            }
                            break;
                        }
                    }
                }
            }else {
                if ("page".equals(dataType)){
                    eachMap = huaweiCommonPageDataHandle(dataList.get(0),startTime,endTime,hwConfigMap,savePath);
                }

                if ("reservation".equals(dataType)){
                    reservationEachMap = huaweiCommonReservationDataHandle(dataList.get(0),startTime,endTime,hwConfigMap,savePath);
                }
            }
            //汇总结果
            if (eachMap.size()>0){
                for (Map.Entry<String,PlatformPageDataVo> eachDataMap:eachMap.entrySet()){
                    dataMap.put(eachDataMap.getKey(),eachDataMap.getValue());
                }
            }

            if (reservationEachMap.size()>0){
                for (Map.Entry<String,PlatformReservationDataVo> eachDataMap:reservationEachMap.entrySet()){
                    reservationDataMap.put(eachDataMap.getKey(),eachDataMap.getValue());
                }
            }
        }
        if ("page".equals(dataType)){
            if (dataMap.size() > 0) {
                for (Map.Entry<String, PlatformPageDataVo> map : dataMap.entrySet()) {
                    PlatformPageDataVo mapVo = map.getValue();
                    list.add(mapVo);
                }
            }
            if (list.size()>0){
                //List<String> appids = list.stream().map(PlatformPageDataVo::getAppid).collect(Collectors.toList());
                //sendFailMail("huawei", "page", appids, appList);
                StringBuffer bf=new StringBuffer("渠道详情页转化分析数据拉取成功, ");
                bf.append("平台：huawei");
                String sql = "SELECT account,cname FROM yyhz_0308.app_channel_config WHERE channel = 'huawei' ";
                if (!BlankUtils.checkBlank(account)){
                    sql += "and account='"+account+"' ";
                }
                List<Map<String, Object>> tokenList = adMapper.queryListMap(sql);
                for (Map<String, Object> token : tokenList) {
                    bf.append(",账号：");
                    bf.append(token.get("account"));
                    bf.append(",主体：");
                    bf.append(token.get("cname"));
                }
                HttpClientUtils.getInstance().httpGet(MSG_URL+bf.toString());
            }
            return list;
        }

        if ("reservation".equals(dataType)){
            if (reservationDataMap.size() > 0) {
                for (Map.Entry<String, PlatformReservationDataVo> map : reservationDataMap.entrySet()) {
                    PlatformReservationDataVo mapVo = map.getValue();
                    reservationList.add(mapVo);
                }
            }
            if (reservationList.size()>0){
                List<String> appids = reservationList.stream().map(PlatformReservationDataVo::getAppid).collect(Collectors.toList());
                sendFailMail("huawei", "reservation", appids, appList);
            }
            return reservationList;
        }
        return list;
    }

    public Map<String,PlatformPageDataVo> huaweiCommonPageDataHandle(PlatformAppInfoVo app,String startTime,String endTime,
                                                                     Map<String, PlatformHwConfigVo> hwConfigMap,String savePath){

        Map<String,PlatformPageDataVo> dataMap = new HashMap<>();
        String account=app.getTaccount();
        PlatformHwConfigVo config = hwConfigMap.get(account);
        if (config == null) {
            return dataMap;
        }
        startTime = startTime.replace("-", "");
        endTime = endTime.replace("-", "");
        try {
            String token = PlatformDataConstants.getHwApiToken(config.getHw_report_api_client_id(), config.getHw_report_api_client_secret());

            String userdataFileUrl = getHwReportUrl(HW_REPORT_API_URL_USERDATA, config.getHw_report_api_client_id(),
                    token, app.getTappid(), startTime, endTime);
            if (BlankUtils.checkBlank(userdataFileUrl)) {
                logger.info("huawei userdata url empty:" + app.getTappid());
            }

            String downloadFileUrl = getHwReportUrl(HW_REPORT_API_URL_DOWNLOAD, config.getHw_report_api_client_id(),
                    token, app.getTappid(), startTime, endTime);
            if (BlankUtils.checkBlank(downloadFileUrl)) {
                logger.info("huawei download url empty:" + app.getTappid());
            }

            String payFileUrl = getHwPayReportUrl(HW_REPORT_API_URL_PAYDATA,config.getHw_report_api_client_id(),
                    token, app.getTappid(), startTime, endTime);
            if (BlankUtils.checkBlank(payFileUrl)){
                logger.info("huawei pay url empty:" + app.getTappid());
            }

            String reservationFileUrl = getHwReportUrl(HW_REPORT_API_URL_RESERVATIONDATA,config.getHw_report_api_client_id(),
                    token,app.getTappid(),startTime,endTime);
            if (BlankUtils.checkBlank(reservationFileUrl)){
                logger.info("huawei reservation url empty:" + app.getTappid());
            }

            //用户新增活跃数据文件
            String userDataFileName = savePath + "/userdata/" + app.getTappid() + ".csv";
            boolean userDataDownload = downloadNet(userdataFileUrl, userDataFileName);
            if (userDataDownload) {
                List<PlatformPageDataVo>  userDataList = readDownloadFile(userDataFileName);
                if (userDataList.size() > 0) {
                    for (PlatformPageDataVo first:userDataList){
                        first.setAppid(app.getAppid());
                        first.setTaccount(account);
                        first.setTappid(app.getTappid());
                        first.setPlatform("huawei");
                    }
                    //下载文件
                    String downloadDataFileName = savePath + "/download/" + app.getTappid() + ".csv";
                    boolean downloadDataDownload = downloadNet(downloadFileUrl,downloadDataFileName);
                    if (downloadDataDownload){
                        List<PlatformPageDataVo> downloadDataList = readDownloadFile(downloadDataFileName);
                        //补全下载数据
                        if (downloadDataList.size()>0){
                            for (PlatformPageDataVo first:userDataList){
                                for (PlatformPageDataVo second:downloadDataList){
                                    if (first.getTdate().equals(second.getTdate())){
                                        first.setExposure(second.getExposure());
                                        first.setExposure_click_ratio(second.getExposure_click_ratio());
                                        first.setDetail_view(second.getDetail_view());
                                        first.setTotal_download(second.getTotal_download());
                                        first.setUpdate_download(second.getUpdate_download());
                                        first.setNet_download(second.getNet_download());
                                        first.setDetail_transform(second.getDetail_transform());
                                        first.setInstall_rate(second.getInstall_rate());
                                    }
                                }
                            }
                        }
                    }
                    //付费文件
                    String payDataFileName = savePath +"/pay/"+app.getTappid()+".csv";
                    boolean payDataDownload = downloadNet(payFileUrl,payDataFileName);
                    if (payDataDownload){
                        List<PlatformPageDataVo> payDataList = readDownloadFile(payDataFileName);
                        //补全下载数据
                        if (payDataList.size()>0){
                            for (PlatformPageDataVo first:userDataList){
                                for (PlatformPageDataVo third:payDataList){
                                    if (first.getTdate().equals(third.getTdate())){
                                        first.setPay_num(third.getPay_num());
                                        first.setPay_total(third.getPay_total());
                                        first.setPay_reg_num(third.getPay_reg_num());
                                        first.setPay_times(third.getPay_times());
                                        first.setPay_first_total(third.getPay_first_total());
                                    }
                                }
                            }
                        }
                    }
                }
                for (PlatformPageDataVo po:userDataList){
                    dataMap.put(po.getTdate()+"_"+po.getTappid(),po);
                }
            }
        }catch (Exception e){
            logger.error("download or userdata error",e);
        }
        return dataMap;
    }

    public Map<String,PlatformReservationDataVo> huaweiCommonReservationDataHandle(PlatformAppInfoVo app,String startTime,String endTime,
                                                             Map<String, PlatformHwConfigVo> hwConfigMap,String savePath){

        Map<String,PlatformReservationDataVo> dataMap = new HashMap<>();
        PlatformHwConfigVo config = hwConfigMap.get(app.getTaccount());
        if (config == null) {
            return dataMap;
        }
        startTime = startTime.replace("-", "");
        endTime = endTime.replace("-", "");
        try {
            String token = PlatformDataConstants.getHwApiToken(config.getHw_report_api_client_id(), config.getHw_report_api_client_secret());

            String reservationFileUrl = getHwReportUrl(HW_REPORT_API_URL_RESERVATIONDATA,config.getHw_report_api_client_id(),
                    token,app.getTappid(),startTime,endTime);
            if (BlankUtils.checkBlank(reservationFileUrl)){
                logger.info("huawei reservation url empty:" + app.getTappid());
            }

            //用户新增活跃数据文件
            String reservationFileName = savePath + "/reservation/" + app.getTappid() + ".csv";
            boolean reservationDataDownload = downloadNet(reservationFileUrl, reservationFileName);
            if (reservationDataDownload) {
                List<PlatformReservationDataVo>  reservationDataList = readDownloadReservationFile(reservationFileName);
                if (reservationDataList.size() > 0) {
                    for (PlatformReservationDataVo first:reservationDataList){
                        first.setAppid(app.getAppid());
                        first.setTappid(app.getTappid());
                        first.setTaccount(app.getTaccount());
                        first.setPlatform("huawei");
                    }
                }
                for (PlatformReservationDataVo po:reservationDataList){
                    dataMap.put(po.getTdate()+"_"+po.getTappid(),po);
                }
            }
        }catch (Exception e){
            logger.error("download or userdata error",e);
        }
        return dataMap;
    }




    public boolean downloadNet(String fileUrl, String fileName) {
        boolean succ = false;
        try {
            FileUtils.copyURLToFile(
                    new URL(fileUrl),
                    new File(fileName));
            succ = true;
        } catch (Exception e) {
            logger.error("downloadNet error:", e);
        }
        return succ;
    }

    public List<PlatformPageDataVo> readDownloadFile(String fileName) {
        List<PlatformPageDataVo> list = new ArrayList<>();
        //根据路径文件名区分下载数据与用户新增活跃数据
        File file = new File(fileName);
        List<String> dataList = new ArrayList<>();
        try {
            BufferedReader textFile = new BufferedReader(new FileReader(file));
            String lineDta = "";
            while ((lineDta = textFile.readLine()) != null) {
                dataList.add(lineDta);
            }
            textFile.close();
        } catch (Exception e) {
            logger.error("read csv error:", e);
        }
        file.delete();
        DateTimeFormatter formatYYYYMMDD = DateTimeFormat.forPattern("yyyyMMdd");
        if (dataList.size() > 0) {
            if (fileName.contains("userdata")) {
                //﻿日期,活跃设备数,新增设备数
                for (int i = 0; i < dataList.size(); i++) {
                    if (i != 0) {
                        PlatformPageDataVo dataVo = new PlatformPageDataVo();
                        String[] data = dataList.get(i).split(",");
                        String tdate = DateTime.parse(data[0], formatYYYYMMDD).toString("yyyy-MM-dd");
                        dataVo.setTdate(tdate);
                        dataVo.setDau(data[1]);
                        dataVo.setAddnum(data[2]);
                        try{
                            if(!BlankUtils.checkBlank(data[3])){
                                String keep1 = data[3];
                                keep1 = keep1.replace("%","");
                                keep1 = new BigDecimal(keep1).
                                        divide(new BigDecimal("100"))
                                        .setScale(4,RoundingMode.HALF_UP).toString();
                                dataVo.setKeep1(keep1);
                            }
                            if(!BlankUtils.checkBlank(data[4])){
                                String keep3 = data[4];
                                keep3 = keep3.replace("%","");
                                keep3 = new BigDecimal(keep3).
                                        divide(new BigDecimal("100"))
                                        .setScale(4,RoundingMode.HALF_UP).toString();
                                dataVo.setKeep3(keep3);
                            }
                            if(!BlankUtils.checkBlank(data[5])){
                                String keep7 = data[5];
                                keep7 = keep7.replace("%","");
                                keep7 = new BigDecimal(keep7).
                                        divide(new BigDecimal("100"))
                                        .setScale(4,RoundingMode.HALF_UP).toString();
                                dataVo.setKeep7(keep7);
                            }
                        }catch (Exception e){

                        }
                        list.add(dataVo);
                    }
                }
            } else if (fileName.contains("download")){
                //日期	曝光	曝光点击率	详情页查看	总下载成功	更新下载成功	新下载成功	详情页转化率	安装成功数	安装成功率	卸载数（从应用市场新安装的应用）	分享成功数
                for (int i = 0; i < dataList.size(); i++) {
                    if (i != 0) {
                        PlatformPageDataVo dataVo = new PlatformPageDataVo();
                        String[] data = dataList.get(i).split(",");
                        String tdate = DateTime.parse(data[0], formatYYYYMMDD).toString("yyyy-MM-dd");
                        dataVo.setTdate(tdate);
                        dataVo.setExposure(data[1]);
                        dataVo.setExposure_click_ratio(ratioChangeFormat(data[2]));
                        dataVo.setDetail_view(data[3]);
                        dataVo.setTotal_download(data[4]);
                        dataVo.setUpdate_download(data[5]);
                        dataVo.setNet_download(data[6]);
                        dataVo.setDetail_transform(ratioChangeFormat(data[7]));
                        dataVo.setInstall_rate(ratioChangeFormat(data[9]));
                        list.add(dataVo);
                    }
                }
            }else if (fileName.contains("pay")){
                //日期	曝光	曝光点击率	详情页查看	总下载成功	更新下载成功	新下载成功	详情页转化率	安装成功数	安装成功率	卸载数（从应用市场新安装的应用）	分享成功数
                for (int i = 0; i < dataList.size(); i++) {
                    if (i != 0) {
                        PlatformPageDataVo dataVo = new PlatformPageDataVo();
                        String[] data = dataList.get(i).split(",");
                        String tdate = DateTime.parse(data[0], formatYYYYMMDD).toString("yyyy-MM-dd");
                        dataVo.setTdate(tdate);
                        dataVo.setPay_num(data[1]);
                        dataVo.setPay_total(data[2]);
                        dataVo.setPay_times(data[3]);
                        dataVo.setPay_reg_num(data[4]);
                        dataVo.setPay_first_total(data[7]);
                        list.add(dataVo);
                    }
                }
            }
        }
        return list;
    }

    public List<PlatformReservationDataVo> readDownloadReservationFile(String fileName) {
        List<PlatformReservationDataVo> list = new ArrayList<>();
        File file = new File(fileName);
        List<String> dataList = new ArrayList<>();
        try {
            BufferedReader textFile = new BufferedReader(new FileReader(file));
            String lineDta = "";
            while ((lineDta = textFile.readLine()) != null) {
                dataList.add(lineDta);
            }
            textFile.close();
        } catch (Exception e) {
            logger.error("read csv error:", e);
        }
        file.delete();
        DateTimeFormatter formatYYYYMMDD = DateTimeFormat.forPattern("yyyyMMdd");
        if (dataList.size()>0){
            if (fileName.contains("reservation")) {
                //日期	曝光	曝光点击率	详情页查看	总下载成功	更新下载成功	新下载成功	详情页转化率	安装成功数	安装成功率	卸载数（从应用市场新安装的应用）	分享成功数
                for (int i = 0; i < dataList.size(); i++) {
                    if (i != 0) {
                        PlatformReservationDataVo dataVo = new PlatformReservationDataVo();
                        String[] data = dataList.get(i).split(",");
                        String tdate = DateTime.parse(data[0], formatYYYYMMDD).toString("yyyy-MM-dd");
                        dataVo.setTdate(tdate);
                        // 累计预约量 7
                        // 当日预约量 4
                        // 预约取消用户数 6
                        // 详情页预约转化率 5
                        // 预约热度
                        // 曝光预约转化率
                        // 曝光预约转化率品类中间值
                        // 详情页预约转化率品类中间值
                        // 预约详情pv 1
                        // 预约详情UV 2
                        // 预约详情浏览设备数 3
                        dataVo.setDetails_pv(data[1]);
                        dataVo.setDetails_uv(data[2]);
                        dataVo.setDetails_view(data[3]);
                        dataVo.setPreorder_users_today(data[4]);
                        if(!BlankUtils.checkBlank(data[5])){
                            String preorder_success_rate = data[5];
                            preorder_success_rate = preorder_success_rate.replace("%","");
                            preorder_success_rate = new BigDecimal(preorder_success_rate).
                                    divide(new BigDecimal("100"))
                                    .setScale(4,RoundingMode.HALF_UP).toString();
                            dataVo.setPreorder_success_rate(preorder_success_rate);
                        }
                        dataVo.setPreorder_users_cancel(data[6]);
                        dataVo.setPreorder_users_total(data[7]);

                        list.add(dataVo);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 获取报表的路径
     * 具体数值参考示例https://developer.huawei.com/consumer/cn/doc/development/AppGallery-connect-References/agcapi-appdownloadexport-0000001158365059
     *
     * @param url
     * @param client_id
     * @param token
     * @param appId
     * @param startTime yyyyMMdd
     * @param endTime   yyyyMMdd
     */
    public static String getHwReportUrl(String url, String client_id, String token, String appId, String startTime, String endTime) {
        String reportUrl = "";
        HttpGet get = new HttpGet(url + appId + "?" + "language=zh-CN" + "&startTime=" + startTime + "&endTime=" + endTime+"&currency=CNY&userTag=0");
        get.setHeader("Authorization", "Bearer " + token);
        get.setHeader("client_id", client_id);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            CloseableHttpResponse httpResponse = httpClient.execute(get);
            BufferedReader br =
                    new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(), Consts.UTF_8));
            String result = br.readLine();
            JSONObject object = JSON.parseObject(result);
            JSONObject retJson = object.getJSONObject("ret");
            if ("0".equals(retJson.getString("code"))) {
                reportUrl = object.getString("fileURL");
            }
        } catch (Exception e) {
            logger.error("getHwReportUrl error:", e);
        }
        return reportUrl;
    }

    /**
     * 获取报表的路径
     * 具体数值参考示例https://developer.huawei.com/consumer/cn/doc/development/AppGallery-connect-References/agcapi-appdownloadexport-0000001158365059
     *
     * @param url
     * @param client_id
     * @param token
     * @param appId
     * @param startTime yyyyMMdd
     * @param endTime   yyyyMMdd
     */
    public static String getHwPayReportUrl(String url, String client_id, String token, String appId, String startTime, String endTime) {
        String reportUrl = "";
        HttpGet get = new HttpGet(url + appId + "?" + "language=zh-CN" + "&startTime=" + startTime + "&endTime=" + endTime+"&currency=CNY&userTag=0");
        get.setHeader("Authorization", "Bearer " + token);
        get.setHeader("client_id", client_id);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            CloseableHttpResponse httpResponse = httpClient.execute(get);
            BufferedReader br =
                    new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(), Consts.UTF_8));
            String result = br.readLine();
            JSONObject object = JSON.parseObject(result);
            JSONObject retJson = object.getJSONObject("ret");
            if ("0".equals(retJson.getString("code"))) {
                reportUrl = object.getString("fileURL");
            }
        } catch (Exception e) {
            logger.error("getHwPayReportUrl error:", e);
        }
        return reportUrl;
    }

    public String ratioChangeFormat(String source){
        String target = "0";
        if (!BlankUtils.checkBlank(source)){
            source = source.replace("%","");
            if (isNumeric(source)){
                target = new BigDecimal(source).divide(new BigDecimal("100")).setScale(4,RoundingMode.CEILING).toString();
            }
        }
        return target;
    }

    public static boolean isNumeric(String str){
        Pattern pattern = Pattern.compile("-?[0-9]+.?[0-9]*");
        return pattern.matcher(str).matches();
    }

    private static String removeCookieWithInvilad(String cookie){
        if (!BlankUtils.checkBlank(cookie)){
            if (cookie.contains("devname")){
                String[] cookieStrs = cookie.split(";");
                StringBuffer stringBuffer = new StringBuffer();
                for (String each:cookieStrs){
                    if (!each.contains("devname")){
                        stringBuffer.append(each).append(";");
                    }
                }
                cookie = stringBuffer.toString();
            }
        }
        return cookie;
    }

    @Override
    public List<PlatformMsgInfoVo> selectPlatformMsgInfoList(Map<String, String> paramMap) {
        return platformDataMapper.selectPlatformMsgInfoList(paramMap);
    }

    /**
     * 渠道违规记录拉取
     * @param platform 平台
     * @param account 账户
     */
    @Override
    public boolean syncPlatformMsgList(String platform, String account) {
        List<PlatformMsgInfoVo> list = new ArrayList<>();
        try {
            if ("oppo".equals(platform)) {
                //list = getMsgListOppo(account);
            } else if ("vivo".equals(platform)) {
                list = getMsgListVivo(account);
            } else if ("xiaomi".equals(platform)) {
                //停掉小米的信息获取 ********-sunwf
                //list = getMsgListXiaomi(account);
            } else if ("huawei".equals(platform)) {
            }

            if (list != null && list.size() > 0) {
                platformDataMapper.savePlatformMsgList(list);
                logger.info("syncPlatformMsgList 平台:" + platform + " 渠道违规记录拉取完成");
                return true;
            } else {
                logger.info("syncPlatformMsgList 平台:" + platform + " 渠道违规记录未拉取到数据");
            }
        } catch (Exception e) {
            logger.error("syncPlatformMsgList 平台:"+ platform +" error:", e);
        }
        return false;
    }

    /* xiaomi-获取站内信息提示 */
    public List<PlatformMsgInfoVo> getMsgListXiaomi(String account) {
        List<PlatformMsgInfoVo> list = new ArrayList<>();

        String sql = "select account,tttoken,ttappid,ttparam,cname company from app_channel_config where channel='xiaomi' and status=1 and modify_time is not null";
        if(!BlankUtils.checkBlank(account)){
            sql += " and account='"+account+"' ";
        }
        List<Map<String, String>> accountList = adMapper.queryListMapOne(sql);

        for (Map<String, String> act : accountList) {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Cookie", act.get("ttappid"));

            String ttparam = act.get("ttparam");
            String maccount = act.get("account");
            String mcompany = act.get("company");

            int totalPage = 1;
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {

                String url = "https://dev.mi.com/memberapi/getMsgList?userId="+ttparam+"&msgType=1&noticeType=0&pageNum="+pageNum+"&pageSize=20";
                logger.debug("getMsgListXiaomi url=="+url);
                try {
                    String httpGet = HttpClientUtils.getInstance().httpGet(url,headMap,true);
                    logger.debug("getMsgListXiaomi httpGet=="+httpGet);

                    if(StringUtil.is_nullString(httpGet)){
                        //发送飞书邮件至孙文风
                        StringBuilder message = new StringBuilder();
                        message.append("渠道站内信爬取失败,媒体：xiaomi,")
                                .append("报错账户：").append(maccount+"-"+mcompany).append(",")
                                .append("媒体响应结果为空");
                        sendFailMsg(message.toString());
                        logger.warn("getMsgListXiaomi fail,api result is null,"+",account:"+maccount+",company:"+mcompany);
                        continue;
                    }
                    if(!"0".equals(JSONObject.parseObject(httpGet).getString("code"))){
                        String platformMsg=JSONObject.parseObject(httpGet).getString("error");
                        StringBuilder message = new StringBuilder();
                        message.append("渠道站内信爬取失败,媒体：xiaomi,")
                                 .append("报错账户：").append(maccount+"-"+mcompany).append(",")
                                 .append("媒体报错返回："+platformMsg);
                        sendFailMsg(message.toString());
                        logger.warn("getMsgListXiaomi fail,api result:"+httpGet+",account:"+maccount+",company:"+mcompany+" url=="+url);
                        continue;
                    }
                    JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                    totalPage = ((int) Math.ceil(data.getDoubleValue("total")/20d));
                    if(totalPage > 10 ){
                        totalPage = 10;
                    }
                    logger.debug("getMsgListXiaomi totalPage=="+totalPage);

                    JSONArray msgList = data.getJSONArray("list");
                    if(null== msgList || msgList.isEmpty()){
                        logger.warn("getMsgListXiaomi fail,list is null"+maccount+mcompany+" url=="+url);
                        continue;
                    }
                    for (int i = 0; i < msgList.size(); i++) {
                        JSONObject item = msgList.getJSONObject(i);
                        DateTime datetime = new DateTime(item.getLongValue("createTime")*1000L);
                        String time = datetime.toString("yyyy-MM-dd HH:mm:ss");
                        String day = datetime.toString("yyyy-MM-dd");

                        if(item.getString("title").contains("违规")
                            || item.getString("title").contains("封禁")
                                || item.getString("title").contains("作弊")
                                || item.getString("title").contains("单机承诺函")
                                || item.getString("content").contains("解封申请已通过")){
                            PlatformMsgInfoVo info = new PlatformMsgInfoVo();
                            info.setPlatform("xiaomi");
                            info.setAccount(maccount);
                            info.setCompany(mcompany);
                            info.setTdate(day);
                            info.setCreatetime(time);
                            info.setContentId(item.getString("contentId"));
                            info.setTitle(item.getString("title"));
                            info.setContent(item.getString("content"));

                            /* 从违规消息中匹配解析出对应的应用名称和ID
                                // (.*?)意思取中间内容，?是特殊字符，需要使用\\转义
                             */
                            String name = "";
                            Matcher matcher = Pattern.compile("应用名称：(.*?)，APPID").matcher(item.getString("content"));
                            while (matcher.find()) {
                                name = matcher.group(1);
                            }
                            if(BlankUtils.checkBlank(name)){
                                // 未匹配到则再次尝试
                                Matcher matcherB = Pattern.compile("应用名称：(.*?)、APPID").matcher(item.getString("content"));
                                while (matcherB.find()) {
                                    name = matcherB.group(1);
                                }
                            }

                            String name2 = "";
                            Matcher matcher2 = Pattern.compile("APPID：(.*?)）").matcher(item.getString("content"));
                            while (matcher2.find()) {
                                name2 = matcher2.group(1);
                            }

                            /* 属于广告位违规，特殊处理违规信息 */
                            if(BlankUtils.checkBlank(name) && item.getString("title").contains("违规封禁通知")){
                                boolean flag = (item.getString("content").contains("您的代码位") || item.getString("content").contains("广告位"));
                                if(flag){
                                    // 跳过广告位违规的事件告警采集 -林培珊.20241125
                                    continue;
                                }
//                                Matcher matcher3 = Pattern.compile("广告位（(.*?)）涉嫌违规").matcher(item.getString("content"));
//                                while (matcher3.find()) {
//                                    name = matcher3.group(1);
//                                }
                            }
                            /* 属于单机承诺函，特殊处理规则 */
                            if(item.getString("title").contains("单机承诺函")){
                                // 有富文本，需要去除html标签
                                String replaceContent = item.getString("content").replaceAll("<[^>]*>", "");
                                info.setContent(replaceContent);

                                Matcher matcher4 = Pattern.compile("应用名称：(.*?)，包名").matcher(replaceContent);
                                while (matcher4.find()) {
                                    name = matcher4.group(1);
                                }
                                Matcher matcher5 = Pattern.compile("包名：(.*?)）尚未").matcher(replaceContent);
                                while (matcher5.find()) {
                                    name2 = matcher5.group(1);
                                }
                            }

                            info.setTappname(name);
                            info.setTappid(name2);
                            
                            //新增一张表统计产品的累计违规次数  先查下当前消息是否有抓取到  已抓取到的消息不进行累计违规次数
                            Map<String,String> qmap=new HashMap<String, String>();
                            qmap.put("platform", "xiaomi");
                            qmap.put("account", maccount);
                            qmap.put("contentId", item.getString("contentId"));
                            int counts=platformDataMapper.getMsgByMsgId(qmap);
                            if(counts==0){
                                Map<String,Object> pmap=new HashMap<String, Object>();
                                 pmap.put("pappid", name2);
                                 platformDataMapper.saveMsgCumulCounts(pmap);
                            }
                            list.add(info);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("getMsgListXiaomi error,account:"+act.get("account")+",msg:{}",e);

                    StringBuilder message = new StringBuilder();
                    message.append("报错任务：").append("getMsgListXiaomi xiaomi-获取站内信息").append("\n")
                            .append("报错账户：").append(act.get("account")+"-"+act.get("company")).append("\n")
                            .append("报错返回："+e.getMessage()).append("\n");
                    commonService.sendFsMsg(message.toString());
                }
            }
        }
        return list;
    }

    /* vivo-获取站内信息提示 */
    public List<PlatformMsgInfoVo> getMsgListVivo(String account) {

        List<PlatformMsgInfoVo> list = new ArrayList<>();

        String sql = "select account,tttoken,ttappid,ttparam,cname company from app_channel_config where channel='vivo' and status=1 and modify_time is not null";
        if(!BlankUtils.checkBlank(account)){
            sql += " and account='"+account+"' ";
        }
        List<Map<String, String>> accountList = adMapper.queryListMapOne(sql);

        for (Map<String, String> act : accountList) {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Cookie", act.get("tttoken"));
            String maccount=act.get("account");
            String company=act.get("company");
            int totalPage = 1;
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {

                long millis = DateTime.now().getMillis();
                String url = "https://adnet.vivo.com.cn/api/notification/query?pageIndex="+pageNum+"&pageSize=80&order=desc&orderBy=createDate&timestamp="+millis;
                logger.debug("getMsgListVivo {},url=="+url, maccount+"-"+company);
                try {
                    String httpGet = HttpClientUtils.getInstance().httpGet(url,headMap,true);
                    logger.debug("getMsgListVivo {},httpGet=="+httpGet, maccount+"-"+company);
                    if(StringUtil.is_nullString(httpGet)){
                        StringBuilder message = new StringBuilder();
                        message.append("渠道站内信爬取失败,媒体：VIVO,")
                        .append("报错账户：").append(maccount+"-"+company).append(",")
                        .append("媒体响应结果为空");
                        //发送飞书邮件至孙文风
                        sendFailMsg(message.toString());
                        logger.info("getMsgListVivo fail,api result is null,"+",account:"+maccount+",company:"+company);
                        continue;
                    }
                    if( !"1".equals(JSONObject.parseObject(httpGet).getString("code"))){
                        String platformMsg=JSONObject.parseObject(httpGet).getJSONObject("errorCodeMsg").getString("errorMsg");
                        StringBuilder message = new StringBuilder();
                        message.append("渠道站内信爬取失败,媒体：VIVO,")
                        .append("报错账户：").append(maccount+"-"+company).append(",")
                        .append("媒体报错返回："+platformMsg);
                        sendFailMsg(message.toString());
                        logger.info("getMsgListVivo fail,api result:"+httpGet+",account:"+maccount+",company:"+company+",url=="+url);
                        continue;
                    }
                    JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                    totalPage = ((int) Math.ceil(data.getDoubleValue("totalCount")/80d));

                    logger.debug("getMsgListVivo {},totalPage=="+totalPage, maccount+"-"+company);

                    JSONArray msgList = data.getJSONArray("dataList");
                    if(null == msgList || msgList.isEmpty()){
                        logger.info("getMsgListVivo fail,items is null"+maccount+company+" url=="+url);
                        continue;
                    }
                    for (int i = 0; i < msgList.size(); i++) {
                        JSONObject item = msgList.getJSONObject(i);

                        DateTime datetime = new DateTime(item.getLongValue("createDate"));
                        String time = datetime.toString("yyyy-MM-dd HH:mm:ss");
                        String day = datetime.toString("yyyy-MM-dd");

                        if(item.getString("title").contains("违规")){
                            PlatformMsgInfoVo info = new PlatformMsgInfoVo();
                            info.setPlatform("vivo");
                            info.setAccount(maccount);
                            info.setCompany(company);
                            info.setTdate(day);
                            info.setCreatetime(time);
                            info.setContentId(item.getString("id"));
                            info.setTitle(item.getString("title"));
                            info.setContent(item.getString("content"));

                            /* 从违规消息中匹配解析出对应的应用名称和ID */
                            // (.*?)意思取中间内容，?是特殊字符，需要使用\\转义
                            Matcher matcher = Pattern.compile("您的媒体：(.*?)\\(").matcher(item.getString("content"));
                            String name = "";
                            while (matcher.find()) {
                                name = matcher.group(1);
                            }
                            Matcher matcher2 = Pattern.compile("\\((.*?)\\),").matcher(item.getString("content"));
                            String name2 = "";
                            while (matcher2.find()) {
                                name2 = matcher2.group(1);
                            }
                            info.setTappname(name);
                            info.setTappid(name2);
                            //新增一张表统计产品的累计违规次数  先查下当前消息是否有抓取到  已抓取到的消息不进行累计违规次数
                            Map<String,String> qmap=new HashMap<String, String>();
                            qmap.put("platform", "vivo");
                            qmap.put("contentId", item.getString("id"));
                            int counts=platformDataMapper.getMsgByMsgId(qmap);
                            if(counts==0){
                                Map<String,Object> pmap=new HashMap<String, Object>();
                                 pmap.put("pappid", name2);
                                 platformDataMapper.saveMsgCumulCounts(pmap);
                            }
                            list.add(info);
                        }

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("getMsgListVivo error,account:"+maccount+",msg:{}",e);

                    StringBuilder message = new StringBuilder();
                    message.append("报错任务：").append("getMsgListVivo vivo-获取站内信息").append("\n")
                            .append("报错账户：").append(maccount+"-"+company).append("\n")
                            .append("报错返回："+e.getMessage()).append("\n");
                    commonService.sendFsMsg(message.toString());
                }
            }
        }
        return list;
    }

    /* oppo-获取站内信息提示 */
    public List<PlatformMsgInfoVo> getMsgListOppo(String account) {

        List<PlatformMsgInfoVo> list = new ArrayList<>();

        String sql = "select account,tttoken,ttappid,ttparam,token,cname company from app_channel_config where channel='oppo' and status=1 and modify_time is not null and IFNULL(ttappid,'') != ''";
        if(!BlankUtils.checkBlank(account)){
            sql += " and account='"+account+"' ";
        }
        List<Map<String, String>> accountList = adMapper.queryListMapOne(sql);
        String url = "https://u.oppomobile.com/union/msg/Q/list";
        for (Map<String, String> act : accountList) {
            String maccount=act.get("account");
            String company=act.get("company");

            String ttappid=act.get("ttappid");
            String token= act.get("token");
            if(StringUtil.is_nullString(ttappid)){
                StringBuilder message = new StringBuilder();
                message.append("渠道站内信爬取失败,媒体：OPPO,")
                        .append("报错账户：").append(maccount+"-"+company).append(",")
                        .append("未获取到账号cookie");
                //发送飞书邮件至孙文风
                sendFailMsg(message.toString());

                logger.info("getMsgListOppo fail,cookie is null,"+",account:"+maccount+",company:"+company);
                continue;
            }
            Map<String, String> headMap = new HashMap<>();
            if(ttappid.split("oadstk=") != null && ttappid.split("oadstk=").length == 2) {
                String oadstk = ttappid.split("oadstk=")[1];
                // 如果oadstk的字符内容中包含;，则获取第1个;前的内容
                if(oadstk.contains(";")) {
                    oadstk = oadstk.split(";")[0];
                }
                headMap.put("Oadstk", oadstk);
            }
            headMap.put("Cookie",ttappid);
            headMap.put("Token",token);
            headMap.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36");
            headMap.put("Content-Type", "application/x-www-form-urlencoded");
            logger.info("getMsgListOppo headMap:"+JSON.toJSONString(headMap));


            int totalPage = 1;
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                logger.debug("getMsgListOppo "+maccount+company+" url=="+url);
                try {
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("page", pageNum+"");
                    paramMap.put("rows", "20");
                    paramMap.put("sortMode", "1");
                    paramMap.put("searchingWord", "");
                    paramMap.put("envType ", "5");

                    String httpGet = HttpClientUtils.getInstance().httpPost(url,paramMap,headMap);
                    if(StringUtil.is_nullString(httpGet)){
                        StringBuilder message = new StringBuilder();
                        message.append("渠道站内信爬取失败,媒体：OPPO,")
                                .append("报错账户：").append(maccount+"-"+company).append(",")
                                .append("媒体响应结果为空");
                        //发送飞书邮件至孙文风
                        sendFailMsg(message.toString());
                        logger.info("getMsgListOppo fail,api result is null,"+",account:"+maccount+",company:"+company);
                        continue;
                    }
                    logger.debug("getMsgListOppo "+maccount+company+" httpGet=="+httpGet);
                    if(!"0".equals(JSONObject.parseObject(httpGet).getString("code"))){
                        String platformMsg=JSONObject.parseObject(httpGet).getString("msg");
                        StringBuilder message = new StringBuilder();
                        message.append("渠道站内信爬取失败,媒体：OPPO,")
                                .append("报错账户：").append(maccount+"-"+company).append(",")
                                .append("媒体报错返回："+platformMsg);
                        //发送飞书邮件至孙文风
                        sendFailMsg(message.toString());
                        logger.info("getMsgListOppo fail,api result:"+httpGet+",account:"+maccount+",company:"+company+" url=="+url);
                        continue;
                    }
                    JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                    totalPage = ((int) Math.ceil(data.getDoubleValue("totalCount")/20d));
                    logger.debug("getMsgListOppo "+maccount+company+" totalPage=="+totalPage);

                    JSONArray msgList = data.getJSONArray("items");
                    if(msgList== null|| msgList.isEmpty()){
                        logger.info("getMsgListOppo fail,items is null"+maccount+company+" url=="+url);
                        continue;
                    }
                    for (int i = 0; i < msgList.size(); i++) {
                        JSONObject item = msgList.getJSONObject(i);
                        DateTime datetime = new DateTime(item.getLongValue("insertTime")*1000L);
                        String time = datetime.toString("yyyy-MM-dd HH:mm:ss");
                        String day = datetime.toString("yyyy-MM-dd");

//                        System.out.println("***********************************************");
//                        System.out.println(time+"\t"+item.getString("messageId")+"\t"+item.getString("title")+"\t"+item.getString("content"));

                        if(item.getString("title").contains("违规")){
                            PlatformMsgInfoVo info = new PlatformMsgInfoVo();
                            info.setPlatform("oppo");
                            info.setAccount(maccount);
                            info.setCompany(company);
                            info.setTdate(day);
                            info.setCreatetime(time);
                            info.setContentId(item.getString("messageId"));
                            info.setTitle(item.getString("title"));
                            info.setContent(item.getString("content"));

                            /* 从违规消息中匹配解析出对应的应用名称和ID */
                            // (.*?)意思取中间内容，?是特殊字符，需要使用\\转义
                            Matcher matcher = Pattern.compile("《(.*?)》").matcher(item.getString("content"));
                            String name = "";
                            while (matcher.find()) {
                                name = matcher.group(1);
                            }
                            Matcher matcher2 = Pattern.compile("\\((.*?)\\),").matcher(item.getString("content"));
                            String name2 = "";
                            while (matcher2.find()) {
                                name2 = matcher2.group(1);
                            }
                            info.setTappname(name);
                            info.setTappid(name2);
                            //新增一张表统计产品的累计违规次数  先查下当前消息是否有抓取到  已抓取到的消息不进行累计违规次数
                            Map<String,String> qmap=new HashMap<String, String>();
                            qmap.put("platform", "oppo");
                            qmap.put("contentId", item.getString("messageId"));
                            int counts=platformDataMapper.getMsgByMsgId(qmap);
                            if(counts==0){
                                Map<String,Object> pmap=new HashMap<String, Object>();
                                 pmap.put("pappid", name2);
                                 platformDataMapper.saveMsgCumulCounts(pmap);
                            }
                            list.add(info);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("getMsgListOppo error,account:"+maccount+",msg:{}",e);
                }
            }
        }
        return list;
    }
    public static void  sendFailMsg(String msg) {
        Map<String,String> sendMap = new HashMap<>();
        sendMap.put("msg",msg);
         sendMap.put("uname","sunwf");
         sendMap.put("robot","robot3");
         sendPost("https://edc.vigame.cn:6115/fs/sendMsg",sendMap);
    }
    /**
     * 发送POST请求
     * @param url
     * @return
     */
    private static String sendPost(String url,Map<String,String> paramMap){
        CloseableHttpClient httpClient = HttpClients.createDefault();// 创建http实例
        CloseableHttpResponse response = null;
        try {
            HttpPost httpPost =new HttpPost();
            httpPost.setURI(new URI(url));
            List<NameValuePair> parms = new ArrayList<>();
            for (Map.Entry<String,String> param:paramMap.entrySet()){
                parms.add(new BasicNameValuePair(param.getKey(), param.getValue()));
            }
            httpPost.setEntity(new UrlEncodedFormEntity(parms,"utf-8"));
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity(); //请求类型
            String content = EntityUtils.toString(entity, "utf-8");
            return content;
        }catch (Exception e) {
            logger.error("sendPost error1:",e);
        }finally {
            try {
                httpClient.close();
                response.close();
            }catch (Exception e) {
                logger.error("sendPost error1:",e);
            }
        }
        return "";
    }
    @Override
    public List<Map<String, Object>> selectPlatformBusinessInfo(Map<String, String> paramMap) {
        return platformDataMapper.selectPlatformBusinessInfo(paramMap);
    }

    @Override
    public List<Map<String, Object>> getAppPartnerNameList(Map<String, String> paramMap) {
        return platformDataMapper.getAppPartnerNameList(paramMap);
    }
    public List<Map<String, Object>> selectAppPartnerList(Map<String, String> paramMap) {
        return platformDataMapper.selectAppPartnerList(paramMap);
    }

    @Override
    public int insertAppPartnerList(Map<String, String> paramMap) {
        return platformDataMapper.insertAppPartnerList(paramMap);
    }

    @Override
    public int updateAppPartnerList(Map<String, String> paramMap) {
        return platformDataMapper.updateAppPartnerList(paramMap);
    }
    @Override
    public int deleteAppPartnerList(List<String> appidTags) {
        //删除产品自定义分组权限控制数据
        int deleteResult = platformDataMapper.deleteAppPartnerList(appidTags);
        if (deleteResult > 0) {
            //删除渠道产品id关联配置数据的自定义分组
            platformDataMapper.updateAppidTag(appidTags);
        }
        return deleteResult;
    }

    @Override
    public void updatePlatformAppInfoBindEndTime(PlatformAppInfoVo exist) {
        platformDataMapper.updatePlatformAppInfoBindEndTime(exist);
    }

    @Override
    public List<Map<String, Object>> selectVivoQuickGameList(Map<String, String> paramMap) {
        List<Map<String, Object>> maps = platformDataMapper.selectVivoQuickGameList(paramMap);
        for (Map<String,Object> map : maps) {

            if (map != null && map.size() > 0) {

                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    if (entry.getKey().contains("rate")) {
                        entry.setValue(entry.getValue().toString() + '%');
                    }
                }

            }
        }
        return maps;
    }

    /**
     * 拉取vivo小游戏数据报表
     * @param account 账户
     * @param sdate 起始日期
     * @param edate 结束日期
     */
//    @Async("aaaScheduler")
    @Override
    public void syncVivoQuickGameList(String account,String sdate,String edate) {
        //需要限定拉取数据的账号是游戏主体
        String sql = "select account,tttoken,ttappid,ttparam,cname company from app_channel_config where channel='vivo' and modify_time is not null and company_type = '游戏' ";
//                "and account in ('<EMAIL>','<EMAIL>','<EMAIL>')";
        if (!BlankUtils.checkBlank(account)) {
            sql += " and account in ("+ account +") ";
        }
        List<Map<String, String>> accountList = adMapper.queryListMapOne(sql);

        String adChannelSql =
                "SELECT packagename, appid, b.app_name FROM `adv_platform_app_info` a\n" +
                "left join app_info b on a.appid = b.id\n" +
                "where b.app_category = 46";
        List<Map<String, Object>> maps = adMapper.queryListMap(adChannelSql);
        Map<Object, Map<String, Object>> packageMap = maps.stream().collect(Collectors.toMap(map -> map.get("packagename"), map -> map));

        for (Map<String, String> act : accountList) {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Cookie", act.get("tttoken"));

            int totalPage = 1;
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                /* 拉取小游戏列表 */
                long millis = DateTime.now().getMillis();
                String url = "https://dev.vivo.com.cn/webapi/quickGame/list?timestamp="+millis+"&currentPage=1&currentPageNum="+pageNum;
                logger.debug("syncVivoQuickGameList url=="+url);

                String httpGet = HttpClientUtils.getInstance().httpGet(url, headMap,true);
                logger.debug("syncVivoQuickGameList httpGet=="+httpGet);

                if (BlankUtils.isJSONObject(httpGet) && "0".equals(JSONObject.parseObject(httpGet).getString("code"))) {
                    JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                    totalPage = ((int) Math.ceil(data.getDoubleValue("recordCount")/10d));
                    logger.debug("syncVivoQuickGameList totalPage=="+totalPage);

                    /* 获取小游戏广告收入集合 */
                    Map<String, JSONObject> adDataMap = getVivoAdData(headMap, sdate, edate);

                    /* 遍历小游戏列表，拉取每个产品的新增活跃留存付费数据 */
                    JSONArray gameList = data.getJSONArray("data");
                    for (int i = 0; i < gameList.size(); i++) {
                        JSONObject game = gameList.getJSONObject(i);
                        String tappid = game.getString("id");
                        logger.debug(String.format("gameList: %s,%s,%s,%s", act.get("account"), tappid, game.getString("rpkName"), game.getString("rpkPackage")));

                        List<JSONObject> sinkList = new ArrayList<>();
                        // 获取该产品日期区间的数据
                        int days = Days.daysBetween(DateTime.parse(sdate), DateTime.parse(edate)).getDays();
                        for (int p = 0; p <= days; p++) {
                            String date = DateTime.parse(sdate).plusDays(p).toString("yyyy-MM-dd");
                            String date2 = DateTime.parse(sdate).plusDays(p).toString("yyyyMMdd");
                            // 获取新增活跃支付数据
                            JSONObject info = processGameSpendData(act, headMap, adDataMap, game, tappid, date, date2);
                            processGameBaseAndAvgData(headMap, tappid, date,info);
                            Object tpackage = info.get("tpackage");
                            if (tpackage != null) {
                                if (packageMap.containsKey(tpackage.toString())) {
                                    info.put("appid", packageMap.get(tpackage.toString()).get("appid"));
                                    info.put("app_name", packageMap.get(tpackage.toString()).get("app_name"));
                                } else {
                                    info.put("appid", null);
                                    info.put("app_name", null);
                                }
                            }
                            sinkList.add(info);
                        }
                        // 执行入库操作
                        if(sinkList != null && !sinkList.isEmpty()) {
                            platformDataMapper.saveVivoQuickGameList(sinkList);
                            List<JSONObject> collect = sinkList.stream().filter(map -> {
                                Double userDuration = map.getDouble("user_duration");
                                if (userDuration != null) {
                                    double value = userDuration * 60;
                                    long milli = (long) (value * 1000);

                                    // SimpleDateFormat用于格式化时间
                                    SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");
                                    dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));

                                    Date date = new Date(milli);

                                    // 输出格式化的时间
                                    String formattedTime = dateFormat.format(date);
                                    map.put("daily_duration", formattedTime);
                                    map.put("actnum", map.getIntValue("day_active_user"));
                                    String appid = map.getString("appid");
                                    if (BlankUtils.isBlank(appid)) {
                                        return false;
                                    }
                                    return true;
                                }
                                return false;
                            }).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(collect)) {
                                Map<String, Object> paramMap = new HashMap<String, Object>();
                                paramMap.put("sql1", "insert into umeng_channel_total(tdate,appid,appname,appkey,cname,cid,daily_duration,actnum) values ");
                                paramMap.put("sql2", " (#{li.tdate},#{li.appid},#{li.app_name},#{li.appid},'h5_vivo',#{li.appid},#{li.daily_duration},#{li.actnum}) ");
                                paramMap.put("sql3", " ON DUPLICATE KEY UPDATE daily_duration=VALUES(daily_duration),actnum=VALUES(actnum)");
                                paramMap.put("list", collect);
                                adMapper.batchExecSql(paramMap);
                            }
                        }

                        // 执行留存数据更新
                        for (int k = 0; k <= days; k++) {
                            String date = DateTime.parse(sdate).plusDays(k).toString("yyyy-MM-dd");
                            updateVivoAppKeep(headMap, tappid, date);
                        }

                    }
                }else{
                    logger.warn("syncVivoQuickGameList,账户 "+act.get("account")+"-"+act.get("company")+", 拉取vivo小游戏数据报错："+httpGet);

                    StringBuilder message = new StringBuilder();
                    message.append("报错任务：").append("syncVivoQuickGameList 拉取vivo小游戏数据").append("\n")
                            .append("报错账户：").append(act.get("account")+"-"+act.get("company")).append("\n")
                            .append("报错返回："+httpGet).append("\n");
                    commonService.sendFsMsg(message.toString());
                    //数据爬取异常报警，且一个主体一天只能报警一次
                    String alertMsg = "vivo小游戏数据爬取失败，媒体：vivo，报错账号：" + act.get("account") + "-" + act.get("company") + "，异常原因："+httpGet+"，请及时关注！";
                    commonService.onceDailyAlertMsg("onceVivoQuickGame:"+act.get("account"),alertMsg,"oc_d23d54068596ed6b735cafd1a51b4d76","sunwf");
                }
            }

        }

    }

    /**
     * 拉取oppo小游戏数据
     *
     * @param startTime 开始时间 格式： yyyy-MM-dd  2024-09-19
     * @param endTime   结束时间 格式： yyyy-MM-dd  2024-09-20
     * @param account   账号 格式： xxxx
     */
    public List<PlatformPageDataVo> syncOppoQuickGameList(String startTime, String endTime,String account) {
        List<PlatformPageDataVo> storeList = new ArrayList<>();
        logger.info("syncOppoQuickGame starting");
        //查询oppo渠道下小游戏的appid
        PlatformAppInfoVo query = new PlatformAppInfoVo();
        query.setPlatform(PlatformDataConstants.PLATFORM.OPPO);
        query.setChannel("'h5_oppo'");
        query.setTaccount(account);
        List<PlatformAppInfoVo> appList = platformDataMapper.getPlatformAppInfoList(query);
        //分组
        Map<String, List<PlatformAppInfoVo>> appGroupMap = appList.stream().collect(Collectors.groupingBy(PlatformAppInfoVo::getTappid));
        //获取拉取数据的账号信息
        String sql = "select account as mapkey,tttoken,cname from app_channel_config where channel = 'oppo' and status=1 ";
        if (!BlankUtils.checkBlank(account)) {
            sql = sql + " and account ='" + account + "' ";
        }
        Map<String, Map<String, Object>> tokenMap = adMapper.queryListMapOfKey(sql);
        //分单条与多条配置  一个tappid可能存在对应多个appid
        for (Map.Entry<String, List<PlatformAppInfoVo>> entry : appGroupMap.entrySet()) {
            List<PlatformAppInfoVo> dataList = entry.getValue();
            //数据日期处理--存在多条数据时需要根据最大绑定时间进行拆分对应的日期
            Map<String, PlatformAppInfoVo> spiltAppInfoMap = spiltDateFromEndTime(dataList, startTime, endTime);
            //拉取oppo渠道的小游戏相关数据
            for (Map.Entry<String, PlatformAppInfoVo> appInfoEntry : spiltAppInfoMap.entrySet()) {
                PlatformAppInfoVo appInfo = appInfoEntry.getValue();
                String platformStartTime = appInfo.getStart_state_time();
                String platformEndTime = appInfo.getEnd_state_time();
                Map<String, PlatformPageDataVo> fetchOppoData = OppoPageDataServiceImpl.fetchOppoQuickGameData(appInfo, platformStartTime, platformEndTime, tokenMap.get(appInfo.getTaccount()));
                if (MapUtils.isEmpty(fetchOppoData)) continue;
                storeList.addAll(fetchOppoData.values());
            }
        }
        // 新增数据至 data_ym.umeng_user_channel_total 表中，后续查询 在线时长 需要
        if (!CollectionUtils.isEmpty(storeList)) {
            //拉取到oppo小游戏数据，添加至 data_ym.umeng_user_channel_total 表中
            List<UmengUserChannelTotal> channels = storeList.stream()
                .map(platformPageDataVo -> {
                    UmengUserChannelTotal channelTotal = new UmengUserChannelTotal();
                    channelTotal.setAppid(Integer.parseInt(platformPageDataVo.getAppid()));
                    channelTotal.setTdate(DateUtil.strToDate(platformPageDataVo.getTdate(),"yyyy-MM-dd"));
                    channelTotal.setInstallChannel("h5_oppo");
                    channelTotal.setActNum(Integer.parseInt(platformPageDataVo.getDau()));
                    channelTotal.setAddNum(Integer.parseInt(platformPageDataVo.getAddnum()));
                    channelTotal.setAppKey(platformPageDataVo.getAppid());
                    //使用时长
                    channelTotal.setDuration(platformPageDataVo.getAvg_duration());
                    //平均单次使用时长
                    channelTotal.setDailyPerDuration(platformPageDataVo.getAvg_duration());
                    //启动次数赋值默认值
                    channelTotal.setStartNum(Integer.parseInt(platformPageDataVo.getStartup_cnt()));
                    return channelTotal;
            }).collect(Collectors.toList());
            // 新增数据至adb库，测试无法新增
            try {
                adbUmengMapper.replaceIntoUserChannelTotal(channels);
            } catch (Exception e) {
                logger.info("新增adb报错：", e);
            }
            adMapper.replaceIntoUserChannelTotal(channels);
            logger.debug("拉取到oppo小游戏数据，添加至 data_ym.umeng_user_channel_total表完成");
        }
        logger.info("syncOppoQuickGame end");
        return storeList;
    }


    /**
     * 当一个tappid对应多个appid时，需要根据最后绑定时间进行对查询的日期限定拆分，只存在一条时不需要进行时间切割操作
     * 示例，假设dataList存在两条数据，data1的bindEndTime: 2024-09-10 ;data2的bindEndTime: 2099-09-09
     * (1)传入 startTime=2024-08-10，endTime=2024-09-20
     *  此时data1的查询开始结束时间需要为 2024-08-10--2024-09-10，data2的查询起始时间：2024-09-11--2024-09-20
     * (2)传入 startTime=2024-09-20，endTime=2024-09-25
     *  此时data1的不进行查询，data2的查询起始时间：2024-09-20--2024-09-25
     * (3)传入 startTime=2024-08-10，endTime=2024-08-25
     *  此时data1的查询开始结束时间需要为 2024-08-10--2024-08-25，data2的不进行查询
     *
     * @param dataList 需要处理的数据
     * @param startTime 开始时间 格式：yyyy-MM-dd 2024-09-01
     * @param endTime   结束时间 格式：yyyy-MM-dd 2024-09-01
     * @return 处理结果 结果map格式：key：appid，value：PlatformAppInfoVo
     */
    private Map<String, PlatformAppInfoVo> spiltDateFromEndTime(List<PlatformAppInfoVo> dataList, String startTime, String endTime) {
        Map<String, PlatformAppInfoVo> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(dataList) || StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return resultMap;
        }
        if (dataList.size() == 1) {
            //只存在一条或无数据
            PlatformAppInfoVo appInfoVo = dataList.get(0);
            appInfoVo.setStart_state_time(startTime);
            appInfoVo.setEnd_state_time(endTime);
            resultMap.put(appInfoVo.getAppid(), appInfoVo);
        } else {
            DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
            DateTime startDate = DateTime.parse(startTime, format);
            DateTime endDate = DateTime.parse(endTime, format);
            //对 BindEndTime 进行排序
            List<PlatformAppInfoVo> collect = dataList.stream().sorted(Comparator.comparing(PlatformAppInfoVo::getBindEndTime)).collect(Collectors.toList());
            DateTime startTimeTemp = startDate;
            for (PlatformAppInfoVo appInfoVo : collect) {
                DateTime bindEndDate = DateTime.parse(appInfoVo.getBindEndTime(), format);
                //最后绑定时间在查询的开始时间之前，不进行查询操作
                if (bindEndDate.compareTo(startTimeTemp) < 0) continue;
                //最后绑定时间在查询的开始时间之后
                //最后绑定时间
                if (bindEndDate.compareTo(endDate) <= 0) {
                    appInfoVo.setStart_state_time(startTimeTemp.toString(format));
                    appInfoVo.setEnd_state_time(bindEndDate.toString(format));
                    resultMap.put(appInfoVo.getAppid(), appInfoVo);
                    startTimeTemp = bindEndDate.plusDays(1);
                } else {
                    appInfoVo.setStart_state_time(startTimeTemp.toString(format));
                    appInfoVo.setEnd_state_time(endTime);
                    resultMap.put(appInfoVo.getAppid(), appInfoVo);
                    break;
                }
            }
        }
        return resultMap;
    }


    private void processGameBaseAndAvgData(Map<String, String> headMap, String tappid, String date, JSONObject info) {
        // 平均启动次数
        JSONObject active = getVivoGraphDataByModuleId(headMap, tappid, date, "83");
        info.put("start_up_number", active.getString("start_up_number"));
        info.put("boot_uv_avg", active.getString("boot_uv_avg"));
        // 人均时长
        JSONObject duration = getVivoGraphDataByModuleId(headMap, tappid, date, "84");
        info.put("user_duration", duration.getString("user_duration"));
        info.put("durat_avg", duration.getString("durat_avg"));
        // 曝光转化率
        JSONObject ctr = getVivoGraphDataByModuleId(headMap, tappid, date, "85");
        info.put("exposure_rate", ctr.getString("exposure_rate"));
        info.put("ctr_avg", ctr.getString("ctr_avg"));

        // 游戏性能指标
        JSONObject gameCapacity = getVivoTableDataByModuleId(headMap, tappid, date, "94");
        info.put("collapse_rate", gameCapacity.getString("collapse_rate"));
        info.put("black_screen_rate", gameCapacity.getString("black_screen_rate"));
        info.put("network_request_success_rate", gameCapacity.getString("network_request_success_rate"));
        info.put("download_network_request_success_rate", gameCapacity.getString("download_network_request_success_rate"));
        info.put("average_max_ram", gameCapacity.getString("average_max_ram"));
        info.put("card_frame_rate", gameCapacity.getString("card_frame_rate"));
        info.put("stuck_rate", gameCapacity.getString("stuck_rate"));

        // 转化率
        JSONObject gameConversion = getVivoTableDataByModuleId(headMap, tappid, date, "91");
        info.put("download_success_rate", gameConversion.getString("download_success_rate"));
        info.put("start_up_success_rate", gameConversion.getString("start_up_success_rate"));

    }


    private JSONObject getVivoGraphDataByModuleId(Map<String, String> headMap, String tappid, String date, String moduleId) {
        /* 拉取平均启动时间 */
        JSONObject result = new JSONObject();
        try {
            long millis = DateTime.now().getMillis();

            String url = "https://dev.vivo.com.cn/webapi/data-service/graph/v2?moduleId=" + moduleId +
                    "&type=5&tabCode=qg_user_action&menuType=1" +
                    "&dataId=" + tappid +
                    "&startDate=" + date +
                    "&endDate=" + date +
                    "&timestamp="+millis;
            logger.debug("getVivoActAddData url=="+url);

            String httpGet = HttpClientUtils.getInstance().httpGetThrow(url, headMap,true);
            logger.debug("getVivoActAddData httpGet=="+httpGet);
            if (BlankUtils.isJSONObject(httpGet) && "0".equals(JSONObject.parseObject(httpGet).getString("code"))) {
                JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                JSONArray jsonArray = data.getJSONArray("dataList");
                result = jsonArray.getJSONObject(0);

            }

            logger.debug("getVivoActAddData result=="+result);
        } catch (Exception e) {
            logger.error("vivo小游戏图表失败, tappid:{}, moduleId:{} ,", tappid, moduleId, e);
        }
        return result;
    }

    private JSONObject getVivoTableDataByModuleId(Map<String, String> headMap, String tappid, String date, String moduleId) {
        /* 拉取平均启动时间 */
        JSONObject result = new JSONObject();
        try {
            long millis = DateTime.now().getMillis();

            String url = "https://dev.vivo.com.cn/webapi/data-service/table/v2?moduleId=" + moduleId +
                    "&menuType=3&type=5" +
                    "&dataId=" + tappid +
                    "&startDate=" + date +
                    "&endDate=" + date +
                    "&currentPageNum=1&numPerPage=10&dataFormatType=0" +
                    "&timestamp=" + millis;
            logger.debug("getVivoActAddData url=="+url);

            String httpGet = HttpClientUtils.getInstance().httpGetThrow(url, headMap,true);
            logger.debug("getVivoActAddData httpGet=="+httpGet);
            if (BlankUtils.isJSONObject(httpGet) && "0".equals(JSONObject.parseObject(httpGet).getString("code"))) {
                JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                JSONArray jsonArray = data.getJSONObject("pageData").getJSONArray("data");
                result = jsonArray.getJSONObject(0);
            }

            logger.debug("getVivoActAddData result=="+result);
        } catch (Exception e) {
            logger.error("vivo小游戏表格失败, tappid:{}, moduleId:{} ,", tappid, moduleId, e);
        }
        return result;
    }


    /**
     * 获取新增活跃支付数据
     * @param act
     * @param headMap
     * @param adDataMap
     * @param game
     * @param tappid
     * @param date
     * @param date2
     * @return
     */
    private JSONObject processGameSpendData(Map<String, String> act, Map<String, String> headMap, Map<String, JSONObject> adDataMap, JSONObject game, String tappid, String date, String date2) {
        JSONObject info = getVivoActAddData(headMap, tappid, date);

        info.put("tdate", date);
        info.put("account", act.get("account"));
        info.put("company", act.get("company"));
        info.put("tappid", tappid);
        info.put("tappname", game.getString("rpkName"));
        info.put("tpackage", game.getString("rpkPackage"));

        // 通过集合匹配，赋值广告相关数据
        JSONObject adData = adDataMap.get(date2 + game.getString("rpkName"));
        if(adData != null && adData.size() > 0){
            info.put("view", adData.getIntValue("view"));
            info.put("click", adData.getIntValue("click"));
            info.put("click_ratio", adData.getString("clickRatio"));
            info.put("cpc", adData.getString("cpc"));
            info.put("ecpm", adData.getString("ecpm"));
            info.put("ad_income", adData.getDoubleValue("income"));
        }
        info.put("revenue", (info.getDoubleValue("pay_income")+info.getDoubleValue("ad_income")));

        if(info.getIntValue("day_active_user") != 0) {
            String pay_arpu = new BigDecimal(info.getDoubleValue("pay_income")).divide(new BigDecimal(info.getIntValue("day_active_user")), 2, RoundingMode.HALF_UP).toString();
            info.put("pay_arpu", pay_arpu);
            String ad_arpu = new BigDecimal(info.getDoubleValue("ad_income")).divide(new BigDecimal(info.getIntValue("day_active_user")), 2, RoundingMode.HALF_UP).toString();
            info.put("ad_arpu", ad_arpu);
            String act_arpu = new BigDecimal(info.getDoubleValue("revenue")).divide(new BigDecimal(info.getIntValue("day_active_user")), 2, RoundingMode.HALF_UP).toString();
            info.put("act_arpu", act_arpu);
        }else{
            info.put("pay_arpu", "0");
            info.put("ad_arpu", "0");
            info.put("act_arpu", "0");
        }
        if(info.getIntValue("pay_user") != 0) {
            String pay_arppu = new BigDecimal(info.getDoubleValue("pay_income")).divide(new BigDecimal(info.getIntValue("pay_user")), 2, RoundingMode.HALF_UP).toString();
            info.put("pay_arppu", pay_arppu);
        }else{
            info.put("pay_arppu", "0");
        }
        return info;
    }


    /**
     * 同步运营数据-广告数据
     * @param headMap 请求头参数
     * @param sdate 起始日期
     * @param edate 结束日期
     * @return
     */
    public Map<String,JSONObject> getVivoAdData(Map<String, String> headMap,String sdate,String edate) {

        /* 拉取展示点击ecpm数据返回 */
        Map<String,JSONObject> result = new HashMap<>();
        try {
            int totalPage = 1;
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                long millis = DateTime.now().getMillis();
                String url = "https://adnet.vivo.com.cn/api/report/getReportTableData?order=&orderBy=&startDate="+ sdate +"&endDate="+ edate +"&dimensions=mediaId&platformType=" +
                        "&metrics=view&flowType=1&pageIndex="+pageNum+"&pageSize=30&timestamp=1701860469743"+millis;
                logger.debug("getVivoAdData url==" + url);

                String httpGet = HttpClientUtils.getInstance().httpGet(url, headMap,true);
                logger.debug("getVivoAdData httpGet==" + httpGet);
                if (BlankUtils.isJSONObject(httpGet) && "1".equals(JSONObject.parseObject(httpGet).getString("code"))) {
                    JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                    totalPage = ((int) Math.ceil(data.getDoubleValue("totalCount") / 30d));

                    logger.debug("getVivoAdData totalPage==" + totalPage);
                    /* 遍历小游戏的广告收益列表 */
                    JSONArray dataList = data.getJSONArray("dataList");
                    for (int i = 0; i < dataList.size(); i++) {
                        JSONObject info = dataList.getJSONObject(i);
                        result.put(info.getString("date")+info.getString("mediaName"), info);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 同步运营数据-更新留存数据
     * @param headMap 请求头参数
     * @param tappid 产品ID
     * @param date 日期
     * @return
     */
    public void updateVivoAppKeep(Map<String, String> headMap,String tappid,String date) {
        /* 拉取留存数据执行修改 */
        try {
            String end = DateTime.parse(date).minusDays(1).toString("yyyy-MM-dd");
            String start = DateTime.parse(date).minusDays(6).toString("yyyy-MM-dd");
            long millis = DateTime.now().getMillis();
            String link = "https://dev.vivo.com.cn/webapi/data-service/table/v2?moduleId=82&type=5&menuType=1&menuCode=&moduleCode=&currentPageNum=1&numPerPage=10&dataFormatType=0&dataId="+tappid+"&startDate="+start+"&endDate="+end+"&tabCode=qg_add_active_retention&timestamp="+millis;
            logger.debug("updateVivoAppKeep link=="+link);

            String httpGet = HttpClientUtils.getInstance().httpGet(link, headMap,true);
            if (BlankUtils.isJSONObject(httpGet) && "0".equals(JSONObject.parseObject(httpGet).getString("code"))) {
                JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                List<JSONObject> dataList = data.getJSONObject("pageData").getJSONArray("data").toJavaList(JSONObject.class);
                dataList.forEach(act -> {
                    act.put("tappid",tappid);
                    act.put("tdate",act.getString("effect_date"));
                });
                /* 更新留存信息到数据库中 */
                logger.debug("updateVivoAppKeep dataList=="+JSON.toJSONString(dataList));
                if(dataList != null && dataList.size() > 0){
                    Map<String, Object> paramMap2 = new HashMap<String, Object>();
                    paramMap2.put("sql1", "update vivo_quick_game_info set next_day_left_rate=#{li.next_day_left_rate},act_rtn_2_avg=#{li.act_rtn_2_avg},three_day_left_rate=#{li.three_day_left_rate},act_rtn_3_avg=#{li.act_rtn_3_avg},seven_day_left_rate=#{li.seven_day_left_rate},act_rtn_7_avg=#{li.act_rtn_7_avg} "+
                            "where tdate=#{li.tdate} and tappid=#{li.tappid} ");
                    paramMap2.put("list", dataList);
                    adMapper.batchExecSqlTwo(paramMap2);
                }

            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 同步运营数据-新增活跃支付
     * @param headMap 请求头参数
     * @param tappid 产品ID
     * @param date 日期
     * @return
     */
    public JSONObject getVivoActAddData(Map<String, String> headMap,String tappid,String date) {

        /* 拉取新增活跃数据返回 */
        JSONObject result = new JSONObject();
        try {
            long millis = DateTime.now().getMillis();
            String start = DateTime.parse(date).minusDays(6).toString("yyyy-MM-dd");
            Map<String,String> cardMap = new HashMap<String,String>(){{
                put("24","new_user");
                put("25","day_active_user");
                put("49","new_uv_avg");
                put("50","act_uv_avg");
                put("26","pay_user");
                put("27","pay_income");
            }};
            for (String card : cardMap.keySet()) {
                String url = "https://dev.vivo.com.cn/webapi/data-service/index-card?cardId="+card+"&type=5&dataId="+tappid+"&startDate="+start+"&endDate="+date+
                        "&menuCode=&tabCode=qg_add_active_retention&timestamp="+millis;
                logger.debug("getVivoActAddData url=="+url);

                String httpGet = HttpClientUtils.getInstance().httpGet(url, headMap,true);
                logger.debug("getVivoActAddData httpGet=="+httpGet);
                if (BlankUtils.isJSONObject(httpGet) && "0".equals(JSONObject.parseObject(httpGet).getString("code"))) {
                    JSONObject data = JSONObject.parseObject(httpGet).getJSONObject("data");
                    result.put(cardMap.get(card), (data.getString("kpiValue").replace("-","0")));
                }
            }
            logger.debug("getVivoActAddData result=="+result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
    public static void main(String[] args) {
        Map<String, String> headMap = new HashMap<>();
         headMap.put("Cookie", "oadstk=ef8b041c3e9d53f43693334198592f6e;oadsk=c79df0fe571d45d995f0a8dfa706b4b7; adstk=ef8b041c3e9d53f43693334198592f6e");
         headMap.put("Token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblNjaGVtZSI6IkEiLCJpZGMiOiJzaG91bWluZyIsImV4cCI6MTcwNTM4ODM0MSwidG9rZW5UeXBlIjoiQUNDRVNTX1RPS0VOIiwidGlkIjoiaE9FcmNYUzdycCtEcXQ2NUcrTVJ5aExJa0Q3WjgrclpwanhKWkRjTEpWbE9ZOG0reUcrNXZmNjdiblVDSjNhNHJodG9zSXlDbkszbklpYW5VNlZwdjJOeFdnb2R2U2hVUVZJcldGY2FxaHgzSE9XYkg1aStHOWQxU0duM21scVR3MWVsL0FORU40RE83ZWxMM05Fd1FpU2ZuY3JGM2lKVXkxOU94N0ljYkwxQmJMc21UanlWYVAzOHZXQmNVc1dHNXk5ZTNycm8xTUwyWUdDNlFFV05sQT09IiwicmVmcmVzaFRva2VuIjoiQlk5YkhjUVdUSmJVM3dINVFWQXRrYSJ9.So8s-saqyaChls7DwrKBCla5s_-_QZGD9MtJ78OGvz0");
         headMap.put("Oadstk", "ef8b041c3e9d53f43693334198592f6e");
         headMap.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36");
         headMap.put("Content-Type", "application/x-www-form-urlencoded");

         Map<String, String> paramMap = new HashMap<>();
         paramMap.put("page", "1");
         paramMap.put("rows", "20");
         paramMap.put("sortMode", "1");
         paramMap.put("searchingWord", "");

         String httpGet = HttpClientUtils.getInstance().httpPost("https://u.oppomobile.com/union/msg/Q/list",paramMap,headMap);
         System.out.println(httpGet);

    }
}
