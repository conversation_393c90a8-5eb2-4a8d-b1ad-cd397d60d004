package com.wbgame.service.impl.platform.vivo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.PlatformDataMapper;
import com.wbgame.mapper.master.WbConfigMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.adv2.PlatformAppInfoVo;
import com.wbgame.pojo.adv2.PlatformPageDataVo;
import com.wbgame.pojo.adv2.PlatformRegAndDauDataVo;
import com.wbgame.pojo.adv2.platform.PlatformReservationDataVo;
import com.wbgame.service.impl.platform.PlatformDataConstants;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.HttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname VivoPageDataServiceImpl
 * @Description vivo平台详情页数据拉取
 * @Date 2023/4/23 14:47
 */
public class VivoPageDataServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(VivoPageDataServiceImpl.class);

    @Autowired
    PlatformDataMapper platformDataMapper;


    public static Map<String,PlatformPageDataVo> vivoCommonPageDataHandle(PlatformAppInfoVo app,
             String startTime,String endTime,Map<String,String> tokenMap){
        Map<String,PlatformPageDataVo> map = new HashMap<>();
        List<PlatformPageDataVo> list = new ArrayList<>();
        //分四部分数据
        //第一部分 下载量 更新下载量
        //第二部分 安装成功率
        //第三部分 详情页下载转化率
        //第四部分 付费
        //第六部分 启动次数
        //第七部分 总览(首次下载 更新下载量)
        //第八部分 下载成功率
        //第七部分 新增
        //第八部分 活跃
        List<PlatformPageDataVo> firstPart = new ArrayList<>();
        List<PlatformPageDataVo> secondPart = new ArrayList<>();
        List<PlatformPageDataVo> thirdPart = new ArrayList<>();
        List<PlatformPageDataVo> fourthPart = new ArrayList<>();
        List<PlatformPageDataVo> fifthPart = new ArrayList<>();

        List<PlatformPageDataVo> sixthPart = new ArrayList<>();
        List<PlatformPageDataVo> seventhPart = new ArrayList<>();
        List<PlatformPageDataVo> eighthPart = new ArrayList<>();
        Map<String,String> ninePartMap = new HashMap<>();
        String account=app.getTaccount();
        try {
            Map<String, String> headMap = new HashMap<>();
           
            headMap.put("cookie", tokenMap.get(account));
            //第一部分 新增活跃 首次下载 increase_download->净下载量 net_download  总更新 update_download->更新下载量 update_download
            String firstNow = (System.currentTimeMillis() - 3000) + "";
            String firstUrl = "https://dev.vivo.com.cn/webapi/data-service/graph/v2?moduleId=24&menuType=1&type=2" +
                    "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + firstNow;
            try {
                String result = HttpRequest.get(firstUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                            JSONArray dataArray = data.getJSONArray("dataList");
                            if (dataArray != null && dataArray.size() > 0) {
                                for (Object obj : dataArray) {
                                    JSONObject dataJson = (JSONObject) obj;
                                    PlatformPageDataVo dataVo = new PlatformPageDataVo();
                                    dataVo.setPlatform("vivo");
                                    dataVo.setTaccount(account);
                                    dataVo.setTappid(app.getTappid());
                                    dataVo.setShopId(app.getShopId());
                                    dataVo.setAppid(app.getAppid());
                                    dataVo.setTdate(dataJson.getString("effect_date"));
                                    dataVo.setUpdate_download(dataJson.getString("update_download"));
                                    dataVo.setNet_download(dataJson.getString("increase_download"));
                                    firstPart.add(dataVo);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("vivoPageData first part parse error", e);
            }
            //拿不到新增活跃直接返回
            if (firstPart.size()==0){
                return map;
            }

            //第二部分 安装成功率 /下载成功-安装转化率 install_success_conversion_rate->install_rate
            //下载成功转化率 download_success_conversion_rate->download_rate
            String secondNow = (System.currentTimeMillis() - 3000) + "";
            String secondUrl = "https://dev.vivo.com.cn/webapi/data-service/graph/v2?moduleId=28&menuType=1&type=2" +
                    "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + secondNow;
            try {
                String result = HttpRequest.get(secondUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                            JSONArray dataArray = data.getJSONArray("dataList");
                            if (dataArray != null && dataArray.size() > 0) {
                                for (Object obj : dataArray) {
                                    JSONObject dataJson = (JSONObject) obj;
                                    PlatformPageDataVo dataVo = new PlatformPageDataVo();
                                    dataVo.setAppid(app.getAppid());
                                    dataVo.setTaccount(account);
                                    dataVo.setTdate(dataJson.getString("effect_date"));
                                    String install_rate = "";
                                    if (!BlankUtils.checkBlank(dataJson.getString("install_success_conversion_rate"))) {
                                        install_rate = dataJson.getString("install_success_conversion_rate")
                                                .replace("-", "").replace("%","");
                                    }
                                    dataVo.setInstall_rate(install_rate);
                                    String download_rate = "";
                                    if (!BlankUtils.checkBlank(dataJson.getString("download_success_conversion_rate"))) {
                                        download_rate = dataJson.getString("download_success_conversion_rate")
                                                .replace("-", "").replace("%","");
                                    }
                                    dataVo.setDownload_rate(download_rate);
                                    secondPart.add(dataVo);
                                }
                            }
                        }
                    }
                }
            }catch (Exception e){
                logger.error("vivoPageData second part error", e);
            }

            //第三部分 详情页转化率
            try {
                String thirdNow = (System.currentTimeMillis() - 3000) + "";
                String thirdUrl = "https://dev.vivo.com.cn/webapi/data-service/index-card?cardId=12&type=2&dataId=" + app.getTappid()
                        + "&startDate=" + startTime + "&endDate=" + endTime + "&timestamp=" + thirdNow;

                String result = HttpRequest.get(thirdUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                            JSONArray dataArray = data.getJSONArray("dataList");
                            if (dataArray != null && dataArray.size() > 0) {
                                for (Object obj : dataArray) {
                                    JSONObject dataJson = (JSONObject) obj;
                                    PlatformPageDataVo dataVo = new PlatformPageDataVo();
                                    dataVo.setAppid(app.getAppid());
                                    dataVo.setTaccount(account);
                                    dataVo.setTdate(dataJson.getString("effect_date"));
                                    dataVo.setDetail_transform(dataJson.getString("detail_page_dld_rate"));
                                    thirdPart.add(dataVo);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("vivoPageData third part error", e);
            }

            //付费数据
            String fourthNow = (System.currentTimeMillis() - 3000) + "";
            String fourthUrl = "https://dev.vivo.com.cn/webapi/data-service/table/v2?moduleId=31&menuType=1&type=2&dataFormatType=0&currentPageNum=1&numPerPage=10" +
                    "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + fourthNow;
            try {
                String result = HttpRequest.get(fourthUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                        	JSONObject pageData = data.getJSONObject("pageData");
                        	if (pageData != null) {
                        		JSONArray dataArray = pageData.getJSONArray("data");
	                            if (dataArray != null && dataArray.size() > 0) {
	                                for (Object obj : dataArray) {
	                                    JSONObject dataJson = (JSONObject) obj;
	                                    PlatformPageDataVo dataVo = new PlatformPageDataVo();
	                                    dataVo.setAppid(app.getAppid());
	                                    dataVo.setTaccount(account);
	                                    dataVo.setTdate(dataJson.getString("effect_date"));
	                                    if (dataJson.getString("pay_success_rate")!=null) {
	                                        String pay_success_ratio = "";
	                                        pay_success_ratio = dataJson.getString("pay_success_rate")
	                                                .replace("%", "")
	                                                .replace("-", "");
	                                        if (!BlankUtils.checkBlank(pay_success_ratio)) {
	                                            BigDecimal bigDecimal = new BigDecimal(pay_success_ratio)
	                                                    .multiply(new BigDecimal("100"))
	                                                    .divide(new BigDecimal("10000")).setScale(4, RoundingMode.HALF_UP);
	                                            pay_success_ratio = bigDecimal.toString();
	                                        }
	                                        dataVo.setPay_succ_ratio(pay_success_ratio);
	                                    }
	
	                                    if (dataJson.getString("pay_user")!=null){
	                                        String pay_num = dataJson.getString("pay_user").replace("-","");
	                                        dataVo.setPay_num(pay_num);
	                                    }
	                                    if (dataJson.getString("consume_total_money")!=null){
	                                        String pay_total = dataJson.getString("consume_total_money").replace("-","");
	                                        dataVo.setPay_total(pay_total);
	                                    }
	
	                                    if (dataJson.getString("new_pay_money")!=null){
	                                        String pay_reg_total= dataJson.getString("new_pay_money").replace("-","");
	                                        dataVo.setPay_reg_total(pay_reg_total);
	                                    }
	                                    if (dataJson.getString("new_pay_user_num")!=null){
	                                        String pay_reg_num= dataJson.getString("new_pay_user_num").replace("-","");
	                                        dataVo.setPay_reg_num(pay_reg_num);
	                                    }
	                                    if (dataJson.getString("first_pay_money")!=null){
	                                        String first_pay_money = dataJson.getString("first_pay_money").replace("-","");
	                                        dataVo.setPay_first_total(first_pay_money);;
	                                    }
	                                    if (dataJson.getString("first_pay_user_num")!=null){
	                                        String first_pay_user_num = dataJson.getString("first_pay_user_num").replace("-","");
	                                        dataVo.setFirst_pay_user_num(first_pay_user_num);
	                                    }
	                                    fourthPart.add(dataVo);
	                                }
	                            }
                        	}
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("vivoPageData fourth part parse error", e);
            }

            //留存数据
            String fifthNow = (System.currentTimeMillis() - 3000) + "";
            String fifthUrl = "https://dev.vivo.com.cn/webapi/data-service/table/v2?moduleId=147&menuType=1&type=2&currentPageNum=1&numPerPage=10&dataFormatType=1" +
                    "&menuCode=behavior_retention_analysis&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + fifthNow;
            try {
                String result = HttpRequest.get(fifthUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                        	JSONObject pageData = data.getJSONObject("pageData");
                        	if (pageData != null) {
                        		JSONArray dataArray = pageData.getJSONArray("data");
                                if (dataArray != null && dataArray.size() > 0) {
                                    for (Object obj : dataArray) {
                                        JSONObject dataJson = (JSONObject) obj;
                                        PlatformPageDataVo dataVo = new PlatformPageDataVo();
                                        dataVo.setAppid(app.getAppid());
                                        dataVo.setTaccount(account);
                                        dataVo.setTdate(dataJson.getString("effect_date"));
                                        String keep1 = "";
                                        if (!BlankUtils.checkBlank(dataJson.getString("day1_new_user_rate"))) {
                                            keep1 = dataJson.getString("day1_new_user_rate")
                                                    .replace("%", "")
                                                    .replace("-", "");
                                            keep1 = new BigDecimal(keep1)
                                                    .divide(new BigDecimal("100"))
                                                    .setScale(4,RoundingMode.HALF_UP)
                                                    .toString();
                                        }
                                        String keep3 = "";
                                        if (!BlankUtils.checkBlank(dataJson.getString("day3_new_user_rate"))) {
                                            keep3 = dataJson.getString("day3_new_user_rate")
                                                    .replace("%", "")
                                                    .replace("-", "");
                                            keep3 = new BigDecimal(keep3)
                                                    .divide(new BigDecimal("100"))
                                                    .setScale(4,RoundingMode.HALF_UP)
                                                    .toString();
                                        }
                                        String keep7 = "";
                                        if (!BlankUtils.checkBlank(dataJson.getString("day7_new_user_rate"))) {
                                            keep7 = dataJson.getString("day7_new_user_rate")
                                                    .replace("%", "")
                                                    .replace("-", "");
                                            keep7 = new BigDecimal(keep7)
                                                    .divide(new BigDecimal("100"))
                                                    .setScale(4,RoundingMode.HALF_UP)
                                                    .toString();
                                        }
                                        String keep30 = "";
                                        if (!BlankUtils.checkBlank(dataJson.getString("day30_new_user_rate"))) {
                                        	keep30 = dataJson.getString("day30_new_user_rate")
                                                    .replace("%", "")
                                                    .replace("-", "");
                                        	keep30 = new BigDecimal(keep30)
                                                    .divide(new BigDecimal("100"))
                                                    .setScale(4,RoundingMode.HALF_UP)
                                                    .toString();
                                        }
                                        dataVo.setKeep1(keep1);
                                        dataVo.setKeep3(keep3);
                                        dataVo.setKeep7(keep7);
                                        dataVo.setKeep30(keep30);
                                        fifthPart.add(dataVo);
                                    }
                                }
                        	}
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("vivoPageData fifth part parse error", e);
            }

            //启动次数数据
            String sixNow = (System.currentTimeMillis() - 3000) + "";
            String sixthUrl = "https://dev.vivo.com.cn/webapi/data-service/graph/v2?moduleId=144&menuType=1&type=2" +
                    "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + sixNow;
            try {
                String result = HttpRequest.get(sixthUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                            JSONArray dataArray = data.getJSONArray("dataList");
                            if (dataArray != null && dataArray.size() > 0) {
                                for (Object obj : dataArray) {
                                    JSONObject dataJson = (JSONObject) obj;
                                    PlatformPageDataVo dataVo = new PlatformPageDataVo();
                                    dataVo.setAppid(app.getAppid());
                                    dataVo.setTaccount(account);
                                    dataVo.setTdate(dataJson.getString("effect_date"));
                                    dataVo.setStartup_cnt(dataJson.getString("startup_cnt"));
                                    sixthPart.add(dataVo);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("vivoPageData sixth part parse error", e);
            }

            //新增数据
            String sevenNow = (System.currentTimeMillis() - 3000) + "";
            String sevenUrl = "https://dev.vivo.com.cn/webapi/data-service/table/v2?moduleId=141&menuType=1&type=2" +
                    "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + sevenNow+"&currentPageNum=1&numPerPage=10&dataFormatType=0";
            try {
                String result = HttpRequest.get(sevenUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                            JSONObject pageData = data.getJSONObject("pageData");
                            if (pageData!=null){
                                JSONArray dataArray = pageData.getJSONArray("data");
                                if (dataArray != null && dataArray.size() > 0) {
                                    for (Object obj : dataArray) {
                                        JSONObject dataJson = (JSONObject) obj;
                                        PlatformPageDataVo dataVo = new PlatformPageDataVo();
                                        dataVo.setAppid(app.getAppid());
                                        dataVo.setTaccount(account);
                                        dataVo.setTdate(dataJson.getString("effect_date"));
                                        dataVo.setAddnum(dataJson.getString("new_user_uv"));
                                        seventhPart.add(dataVo);
                                    }
                                }
                            }

                        }
                    }
                }
            } catch (Exception e) {
                logger.error("vivoPageData seventh part parse error", e);
            }


            //活跃数据
            String eightNow = (System.currentTimeMillis() - 3000) + "";
            String eightUrl = "https://dev.vivo.com.cn/webapi/data-service/table/v2?moduleId=143&menuType=1&type=2" +
                    "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + eightNow+"&currentPageNum=1&numPerPage=10&dataFormatType=0";;
            try {
                String result = HttpRequest.get(eightUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                            JSONObject pageData = data.getJSONObject("pageData");
                            if (pageData!=null){
                                JSONArray dataArray = pageData.getJSONArray("data");
                                if (dataArray != null && dataArray.size() > 0) {
                                    for (Object obj : dataArray) {
                                        JSONObject dataJson = (JSONObject) obj;
                                        PlatformPageDataVo dataVo = new PlatformPageDataVo();
                                        dataVo.setAppid(app.getAppid());
                                        dataVo.setTaccount(account);
                                        dataVo.setTdate(dataJson.getString("effect_date"));
                                        dataVo.setDau(dataJson.getString("active_user_uv"));
                                        eighthPart.add(dataVo);
                                    }
                                }
                            }

                        }
                    }
                }
            } catch (Exception e) {
                logger.error("vivoPageData eight part parse error", e);
            }

            // 新增用户人均使用时长--分页查询数据
            //vivo 的这个api 每页大小限定只能为10条
            int numPerPage = 10;
            int recordCount = 10;
            for (int i = 0; i < (recordCount + numPerPage - 1) / numPerPage; i++) {
                String nineNow = (System.currentTimeMillis() - 3000) + "";
                String nineUrl = "https://dev.vivo.com.cn/webapi/data-service/table/v2?moduleId=371&menuType=1&type=2" +
                        "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                        "&currentPageNum="+(i + 1)+"&numPerPage="+numPerPage+"&dataFormatType=0&timestamp=" + nineNow;
                try {
                    String result = HttpRequest.get(nineUrl, headMap);
                    JSONObject ret = JSONObject.parseObject(result);
                    if (ret != null) {
                        if ("0".equals(ret.getString("code"))) {
                            JSONObject data = ret.getJSONObject("data");
                            if (data != null && data.containsKey("pageData")) {
                                JSONObject pageData = data.getJSONObject("pageData");
                                //赋值数据总数
                                recordCount = pageData.getInteger("recordCount");
                                JSONArray dataArray = pageData.getJSONArray("data");
                                if (dataArray != null && dataArray.size() > 0) {
                                    for (Object obj : dataArray) {
                                        JSONObject dataJson = (JSONObject) obj;
                                        String newUserAvgDuration = dataJson.getString("new_user_avg_duration");
                                        if (!BlankUtils.checkBlank(newUserAvgDuration)) {
                                            //分钟转成秒：四舍五入
                                            BigDecimal seconds = new BigDecimal(newUserAvgDuration).multiply(new BigDecimal(60)).setScale(0,RoundingMode.HALF_UP);
                                            ninePartMap.put(app.getAppid() +"_" + dataJson.getString("effect_date"),seconds.toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("vivoPageData nine part parse error", e);
                }
            }

            //合并数据
            if (firstPart.size() > 0) {
                if (secondPart.size() > 0) {
                    for (PlatformPageDataVo first : firstPart) {
                        for (PlatformPageDataVo second : secondPart) {
                            if (first.getAppid().equals(second.getAppid()) && first.getTdate().equals(second.getTdate())) {
                                first.setInstall_rate(second.getInstall_rate());
                            }
                        }
                    }
                }
                if (thirdPart.size() > 0) {
                    for (PlatformPageDataVo first : firstPart) {
                        for (PlatformPageDataVo third : thirdPart) {
                            if (first.getAppid().equals(third.getAppid()) && first.getTdate().equals(third.getTdate())) {
                                first.setDetail_transform(third.getDetail_transform());
                            }
                        }
                    }
                }
                if(fourthPart.size()>0){
                    for (PlatformPageDataVo first : firstPart) {
                        for (PlatformPageDataVo fourth : fourthPart) {
                            if (first.getAppid().equals(fourth.getAppid()) && first.getTdate().equals(fourth.getTdate())) {
                                first.setPay_succ_ratio(fourth.getPay_succ_ratio());
                                first.setPay_num(fourth.getPay_num());
                                first.setPay_total(fourth.getPay_total());
                                first.setPay_reg_num(fourth.getPay_reg_num());
                                first.setPay_reg_total(fourth.getPay_reg_total());
                                first.setPay_first_total(fourth.getPay_first_total());
                                first.setFirst_pay_user_num(fourth.getFirst_pay_user_num());
                            }
                        }
                    }
                }

                if(fifthPart.size()>0){
                    for (PlatformPageDataVo first : firstPart) {
                        for (PlatformPageDataVo fifth : fifthPart) {
                            if (first.getAppid().equals(fifth.getAppid()) && first.getTdate().equals(fifth.getTdate())) {
                                first.setKeep1(fifth.getKeep1());
                                first.setKeep3(fifth.getKeep3());
                                first.setKeep7(fifth.getKeep7());
                                first.setKeep30(fifth.getKeep30());
                            }
                        }
                    }
                }

                if(sixthPart.size()>0){
                    for (PlatformPageDataVo first : firstPart) {
                        for (PlatformPageDataVo sixth : sixthPart) {
                            if (first.getAppid().equals(sixth.getAppid()) && first.getTdate().equals(sixth.getTdate())) {
                                first.setStartup_cnt(sixth.getStartup_cnt());
                            }
                        }
                    }
                }

                if(seventhPart.size()>0){
                    for (PlatformPageDataVo first : firstPart) {
                        for (PlatformPageDataVo seventh : seventhPart) {
                            if (first.getAppid().equals(seventh.getAppid()) && first.getTdate().equals(seventh.getTdate())) {
                                first.setAddnum(seventh.getAddnum());
                            }
                        }
                    }
                }


                if(eighthPart.size()>0){
                    for (PlatformPageDataVo first : firstPart) {
                        for (PlatformPageDataVo eighth : eighthPart) {
                            if (first.getAppid().equals(eighth.getAppid()) && first.getTdate().equals(eighth.getTdate())) {
                                first.setDau(eighth.getDau());
                            }
                        }
                    }
                }
                //赋值新增用户人均使用时长（分钟）
                if(ninePartMap.size() > 0){
                    for (PlatformPageDataVo first : firstPart) {
                        String key = first.getAppid() + "_" + first.getTdate();
                        first.setNew_user_avg_duration(ninePartMap.get(key));
                    }
                }

            }
            list = firstPart;

            //发送拉取失败邮件
            if (list != null && list.size() > 0) {
                //拼接新增活跃
                try {
                    for (PlatformPageDataVo each : list) {
                        //设置人均次数
                        String avg_time = "";
                        try {
                            if (!BlankUtils.checkBlank(each.getStartup_cnt())
                                    &&!BlankUtils.checkBlank(each.getDau())&&!"0".equals(each.getDau())){
                                avg_time = new BigDecimal(each.getStartup_cnt())
                                        .divide(new BigDecimal(each.getDau()),2, RoundingMode.HALF_UP).toString();
                            }
                        } catch (Exception e) {
                            logger.error("avg_time error:", e);
                        }
                        each.setAvg_time(avg_time);
                    }
                } catch (Exception e) {
                    logger.error("vivo platform page user addnum/dau data error:", e);
                }
            }
        } catch (Exception e) {
            logger.error("vivoPageData error:", e);
        }
        if (list.size()>0){
            for (PlatformPageDataVo each : list){
                map.put(each.getTdate()+"_"+each.getTappid(),each);
            }
        }
        return map;
    }


    public static Map<String, PlatformReservationDataVo> vivoCommonReservationDataHandle(PlatformAppInfoVo app,
                                                                                  String startTime, String endTime, Map<String,String> tokenMap){
        Map<String,PlatformReservationDataVo> map = new HashMap<>();
        List<PlatformReservationDataVo> list = new ArrayList<>();
        //分两部分数据

        List<PlatformReservationDataVo> firstPart = new ArrayList<>();
        List<PlatformReservationDataVo> secondPart = new ArrayList<>();

        try {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("cookie", tokenMap.get(app.getTaccount()));
            //第一部分
            // 累积预约人数 reserve_total_user_num->累计预约量 preorder_users_total
            // 日新增预约人数 reserve_day_user_num ->当日预约量 preorder_users_today
            // 取消预约数 cancel_reservation_num ->预约取消用户数 preorder_users_cancel
            String firstNow = (System.currentTimeMillis() - 3000) + "";
            String firstUrl = "https://dev.vivo.com.cn/webapi/data-service/graph/v2?moduleId=29&menuType=1&type=2" +
                    "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + firstNow;
            try {
                String result = HttpRequest.get(firstUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                            JSONArray dataArray = data.getJSONArray("dataList");
                            if (dataArray != null && dataArray.size() > 0) {
                                for (Object obj : dataArray) {
                                    JSONObject dataJson = (JSONObject) obj;
                                    PlatformReservationDataVo dataVo = new PlatformReservationDataVo();
                                    dataVo.setPlatform("vivo");
                                    dataVo.setTaccount(app.getTaccount());
                                    dataVo.setTappid(app.getTappid());
                                    dataVo.setAppid(app.getAppid());
                                    dataVo.setTdate(dataJson.getString("effect_date"));
                                    if (dataJson.getString("reserve_total_user_num")!=null){
                                        String preorder_users_total = dataJson.getString("reserve_total_user_num").replace("-","");
                                        dataVo.setPreorder_users_total(preorder_users_total);
                                    }
                                    if (dataJson.getString("reserve_day_user_num")!=null){
                                        String preorder_users_today = dataJson.getString("reserve_day_user_num").replace("-","");
                                        dataVo.setPreorder_users_today(preorder_users_today);
                                    }
                                    if (dataJson.getString("cancel_reservation_num")!=null){
                                        String preorder_users_cancel = dataJson.getString("cancel_reservation_num").replace("-","");
                                        dataVo.setPreorder_users_cancel(preorder_users_cancel);
                                    }
                                    firstPart.add(dataVo);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("vivoReservationData first part parse error", e);
            }
            //拿不到新增活跃直接返回
            if (firstPart.size()==0){
                return map;
            }

            //第二部分 预约详情页转化率 reserve_detail_page_rate -> 详情页预约转化率 preorder_success_rate
            String secondNow = (System.currentTimeMillis() - 3000) + "";
            String secondUrl = "https://dev.vivo.com.cn/webapi/data-service/graph/v2?moduleId=30&menuType=1&type=2" +
                    "&dataId=" + app.getTappid() + "&startDate=" + startTime + "&endDate=" + endTime +
                    "&timestamp=" + secondNow;
            try {
                String result = HttpRequest.get(secondUrl, headMap);
                JSONObject ret = JSONObject.parseObject(result);
                if (ret != null) {
                    if ("0".equals(ret.getString("code"))) {
                        JSONObject data = ret.getJSONObject("data");
                        if (data != null) {
                            JSONArray dataArray = data.getJSONArray("dataList");
                            if (dataArray != null && dataArray.size() > 0) {
                                for (Object obj : dataArray) {
                                    JSONObject dataJson = (JSONObject) obj;
                                    PlatformReservationDataVo dataVo = new PlatformReservationDataVo();
                                    dataVo.setAppid(app.getAppid());
                                    dataVo.setTdate(dataJson.getString("effect_date"));
                                    if (dataJson.getString("reserve_detail_page_rate")!=null) {
                                        String preorder_success_rate = "";
                                        preorder_success_rate = dataJson.getString("reserve_detail_page_rate")
                                                .replace("%", "")
                                                .replace("-", "");
                                        if (!BlankUtils.checkBlank(preorder_success_rate)) {
                                            BigDecimal bigDecimal = new BigDecimal(preorder_success_rate).setScale(4, RoundingMode.HALF_UP);
                                            preorder_success_rate = bigDecimal.toString();
                                        }
                                        dataVo.setPreorder_success_rate(preorder_success_rate);
                                    }
                                    secondPart.add(dataVo);
                                }
                            }
                        }
                    }
                }
            }catch (Exception e){
                logger.error("vivoReservationData second part error", e);
            }

            //合并数据
            if (firstPart.size() > 0) {
                if (secondPart.size() > 0) {
                    for (PlatformReservationDataVo first : firstPart) {
                        for (PlatformReservationDataVo second : secondPart) {
                            if (first.getAppid().equals(second.getAppid()) && first.getTdate().equals(second.getTdate())) {
                                first.setPreorder_success_rate(second.getPreorder_success_rate());
                            }
                        }
                    }
                }
            }
            list = firstPart;
        } catch (Exception e) {
            logger.error("vivoPageData error:", e);
        }
        if (list.size()>0){
            for (PlatformReservationDataVo each : list){
                map.put(each.getTdate()+"_"+each.getTappid(),each);
            }
        }
        return map;
    }

    public static JSONObject fetchVivoPlatformAppInfoData(String taccount,String token){
        logger.info("vivoPlatformAppInfoData start");
        JSONObject ret = new JSONObject();
        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("cookie",token);
        List<PlatformAppInfoVo> dataList = new ArrayList<>();

        //state 1 成功 2 token失效 3 未知异常
        String state = "";

        //1 应用 4 游戏
        String[] appTypes = {"1","4"};
        for (String appType:appTypes){
            Integer currentPageNum = 1;
            while (true){
                try {
                    //请求参数
                    Map<String,String> paramMap = new HashMap<>();
                    String timestamp = System.currentTimeMillis()+"";
                    String url = String.format(PlatformDataConstants.PLATFORM.VIVO_APPINFO_URL,appType,currentPageNum,timestamp);
                    String result = HttpRequest.get(url,headerMap);
                    if (BlankUtils.checkBlank(result)){
                        break;
                    }
                    JSONObject resultJson = null;
                    try {
                        resultJson = JSONObject.parseObject(result);
                    }catch (Exception e){
                        state = PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_TOKEN_EXPARED;
                        logger.error("parse vivo appinfo data error",e);
                        break;
                    }
                    if (resultJson==null){
                        break;
                    }
                    //此处判断token失效
                    if (!"0".equals(resultJson.getString("code"))){
                        state = PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_TOKEN_EXPARED;
                        break;
                    }

                    JSONObject dataJson = resultJson.getJSONObject("data");
                    if (dataJson==null){
                        break;
                    }
                    JSONArray dataArray = dataJson.getJSONArray("data");
                    if (dataArray.size()==0){
                        break;
                    }
                    for (Object obj : dataArray) {
                        JSONObject each = (JSONObject) obj;
                        PlatformAppInfoVo vo = new PlatformAppInfoVo();
                        vo.setPlatform(PlatformDataConstants.PLATFORM.VIVO);
                        vo.setTappid(each.getString("id"));
                        vo.setVersion(each.getString("versionName"));
                        String vivoState = "";
                        String status = each.getString("status");
                        String auditOpinion= each.getString("auditOpinion");
                        String qualityStatus = each.getString("qualityStatus");
                        String saleStatus = each.getString("saleStatus");
                        if ("2".equals(auditOpinion)) {
                            vivoState = "13";
                        } else if ("-3".equals(status)) {
                            vivoState = "0";
                        } else if (!"0".equals(status) && !"2".equals(status) || !"0".equals(qualityStatus) && !"1".equals(qualityStatus)) {
                            if (!"0".equals(status) && !"2".equals(status) || "0".equals(qualityStatus) || "1".equals(qualityStatus)) {
                                if ("3".equals(status) && !BlankUtils.checkBlank(saleStatus) && Integer.parseInt(saleStatus) <= 0) {
                                    vivoState = "7";
                                } else if ("3".equals(status) && "1".equals(saleStatus)) {
                                    vivoState = "1";
                                } else if ("-4".equals(status)) {
                                    vivoState = "6";
                                } else if (!"3".equals(status) || !"2".equals(saleStatus) && !"3".equals(saleStatus)) {

                                } else {
                                    vivoState = "2";
                                }
                            } else {
                                vivoState = "5";
                            }
                        } else {
                            vivoState = "14";
                        }
                        vo.setState(vivoState);
                        vo.setTaccount(taccount);
                        vo.setSyncState(PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_SUCCESS);
                        dataList.add(vo);
                    }
                    Integer pageCount = dataJson.getInteger("pageCount");
                    if (currentPageNum.compareTo(pageCount)>0){
                        break;
                    }
                    currentPageNum ++;
                }catch (Exception e){
                    logger.error("oppoPlatformAppInfoData error:",e);
                    state = PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_UNKOWN_ERROR;
                    break;
                }
            }
        }

        if (dataList.size()>0){
            state = PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_SUCCESS;
        }
        ret.put("taccount",taccount);
        ret.put("data",dataList);
        ret.put("state",state);
        //设置token
        logger.info("oppoPlatformAppInfoData end");
        return ret;
    }


    public static JSONObject fetchVivoPlatformGameInfoData(String taccount,String token){
        logger.info("vivoPlatformGameInfoData start");
        JSONObject ret = new JSONObject();
        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("cookie",token);
        List<PlatformAppInfoVo> dataList = new ArrayList<>();
        //1 应用 4 游戏
        String[] appTypes = {"1","4"};
        for (String appType:appTypes){

        }
        //state 1 成功 2 token失效 3 未知异常
        String state = "";
        Integer currentPageNum = 1;
        while (true){
            try {
                //请求参数
                Map<String,String> paramMap = new HashMap<>();
                String timestamp = System.currentTimeMillis()+"";
                paramMap.put("appType","4");
                paramMap.put("appStatus","0");
                paramMap.put("currentPageNum",currentPageNum+"");
                paramMap.put("timestamp",timestamp);
                String result = HttpRequest.httpPost(PlatformDataConstants.PLATFORM.VIVO_APPINFO_URL,paramMap,headerMap);
                if (BlankUtils.checkBlank(result)){
                    break;
                }
                JSONObject resultJson = null;
                try {
                    resultJson = JSONObject.parseObject(result);
                }catch (Exception e){
                    state = PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_TOKEN_EXPARED;
                    logger.error("parse vivo appinfo data error",e);
                    break;
                }
                if (resultJson==null){
                    break;
                }
                //此处判断token失效
                if (!"0".equals(resultJson.getString("errno"))){
                    state = PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_TOKEN_EXPARED;
                    break;
                }
                JSONArray dataArray = resultJson.getJSONArray("data");
                if (dataArray.size()==0){
                    break;
                }
                for (Object obj : dataArray) {
                    JSONObject each = (JSONObject) obj;
                    PlatformAppInfoVo vo = new PlatformAppInfoVo();
                    vo.setPlatform(PlatformDataConstants.PLATFORM.VIVO);
                    vo.setTappid(each.getString("id"));
                    vo.setVersion(each.getString("versionName"));
                    String vivoState = "";
                    //已撤回 auditOpinion 2
                    //未提交 status -3
                    //
//                    put("0","未提交");
//                    put("1","已上线");
//                    put("2","已下线");
//                    put("3","未发布");
//                    put("4","自动化审核中");
//                    put("5","审核中");
//                    put("6","审核驳回");
//                    put("7","定时发布");
//                    put("8","资质审核");
//                    put("9","资质驳回");
//                    put("10","资质通过");
//                    put("11","已冻结");
//                    put("12","报备成功");
//                    put("13","撤销上线");
                    //    VIVO	未提交	未提交
//    VIVO	测试中	测试中
//    VIVO	审核中	审核中
//    VIVO	待发布	定时发布
//    VIVO	已发布	已上线
//    VIVO	审核不通过	审核驳回
//    VIVO	已下架	已下线
//    VIVO	已撤回	撤销上线
                    if ("-3".equals(each.getString("status"))){
                        vivoState = "0";
                    }
                    if ("2".equals(each.getString("auditOpinion"))){
                        vivoState = "13";
                    }
//                    if (){
//
//                    }
                    vo.setState(vivoState);
                    vo.setTaccount(taccount);
                    vo.setSyncState(PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_SUCCESS);
                    dataList.add(vo);
                }
                Integer pageCount = resultJson.getInteger("pageCount");
                if (currentPageNum.compareTo(pageCount)>0){
                    break;
                }
                currentPageNum ++;
            }catch (Exception e){
                logger.error("vivoPlatformGameInfoData error:",e);
                state = PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_UNKOWN_ERROR;
                break;
            }
        }
        if (dataList.size()>0){
            state = PlatformDataConstants.APPINFO.SYNC_APPINFO_STATE_SUCCESS;
        }
        ret.put("taccount",taccount);
        ret.put("data",dataList);
        ret.put("state",state);
        //设置token
        logger.info("vivoPlatformGameInfoData end");
        return ret;
    }

}
