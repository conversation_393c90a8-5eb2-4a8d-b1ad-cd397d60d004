package com.wbgame.service.impl;

import com.wbgame.mapper.master.MainSysDepartmentV3Mapper;
import com.wbgame.mapper.master.PremMapper;
import com.wbgame.mapper.master.WbSysV3Mapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.finance.MainSysDepartmentV3;
import com.wbgame.service.PermissionApprovalService;
import com.wbgame.utils.BlankUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 权限申请审批服务实现
 */
@Service
public class PermissionApprovalServiceImpl implements PermissionApprovalService {

    @Autowired
    private WbSysV3Mapper wbSysV3Mapper;

    @Autowired
    private MainSysDepartmentV3Mapper departmentMapper;

    @Autowired
    private PremMapper premMapper;

    @Override
    public PermissionApprovalResponseVO submitPermissionApplication(PermissionApprovalResponseVO request) {
        // 1. 查询申请人信息
        CurrUserVo applicant = wbSysV3Mapper.selectUserWithDepartmentByLoginName(request.getApplicantUsername());
        if (applicant == null) {
            throw new RuntimeException("申请人不存在：" + request.getApplicantUsername());
        }

        // 2. 构建响应对象
        PermissionApprovalResponseVO response = new PermissionApprovalResponseVO();
        response.setApplicantUsername(applicant.getLogin_name());
        response.setApplicantNickName(applicant.getNick_name());
        response.setApplicantDepartment(applicant.getDepartment());
        response.setApplicationType(request.getApplicationType());
        response.setApplicationMark(request.getApplicationMark());
        response.setApplicationDetail(request.getApplicationDetail());

        // 3. 查找审批人
        List<ApproverInfoVO> approvers = findApprovers(applicant, request.getApplicationMark(), request.getApplicationType());
        response.setApprovers(approvers);

        // 4. 生成审批流程说明
        String approvalProcess = generateApprovalProcess(approvers);
        response.setApprovalProcess(approvalProcess);

        return response;
    }

    @Override
    public Integer createPermissionApplicationRecord(PermissionApprovalResponseVO request, PermissionApprovalResponseVO approvers) {
        WbPremConfigVo premConfig = new WbPremConfigVo();
        premConfig.setPrem_type(request.getApplicationType().toString());
        premConfig.setPrem_mark(request.getApplicationMark());
        premConfig.setMenu_page_dis(request.getApplicationDetail());
        premConfig.setPrem_con("0"); // 0代表审批中
        premConfig.setPrem_user(request.getApplicantUsername());
        
        // 获取申请人部门ID作为审核部门
        CurrUserVo applicant = wbSysV3Mapper.selectUserWithDepartmentByLoginName(request.getApplicantUsername());
        if (applicant != null) {
            premConfig.setPrem_group(applicant.getDepartment());
        }
        
        premConfig.setCuser(request.getApplicantUsername());
        
        premMapper.addPrem(premConfig);
        return premConfig.getPrem_id();
    }

    /**
     * 查找审批人
     */
    private List<ApproverInfoVO> findApprovers(CurrUserVo applicant, String permissionMark, Integer applicationType) {
        if (StringUtils.isEmpty(applicant.getDepartment())) {
            throw new RuntimeException("申请人部门信息不完整");
        }
        List<ApproverInfoVO> approvers = new ArrayList<>();

        Integer currentDepartmentId = Integer.valueOf(applicant.getDepartment());
        
        // 查找一级审批人（直接上级部门）
        ApproverInfoVO firstLevelApprover = findApproverInDepartment(currentDepartmentId, permissionMark, applicationType, 1);
        if (firstLevelApprover != null) {

            // 如果一级审批人没有权限，查找二级审批人
            if (!firstLevelApprover.getHasPermission()) {
                ApproverInfoVO secondLevelApprover = findSecondLevelApprover(currentDepartmentId, permissionMark, applicationType);
                if (secondLevelApprover != null) {
                    approvers.add(secondLevelApprover);
                }
            }else{
                approvers.add(firstLevelApprover);
            }
        }else {
            System.out.println(applicant.getLogin_name()+" 未找到上级部门");
        }
        
        if (approvers.isEmpty()) {
            // 未找到上级部门或二级审批人也没有权限，固定返回admin
            ApproverInfoVO approver = new ApproverInfoVO();
            approver.setLogin_name("admin");
            approver.setNick_name("admin");
            approver.setDepartment("1");
            approver.setDepartmentName("管理部");
            approver.setApprovalLevel(2);
            approver.setHasPermission(true);
            approver.setPermissionDetail("具有相关权限");
            approvers.add(approver);
        }
        
        return approvers;
    }

    /**
     * 在指定部门查找审批人
     */
    private ApproverInfoVO findApproverInDepartment(Integer departmentId, String permissionMark, Integer applicationType, int approvalLevel) {
        // 查找上级部门
        MainSysDepartmentV3 parentDept = departmentMapper.selectParentDepartment(departmentId);
        if (parentDept == null) {
            return null;
        }
        
        // 查找上级部门中的用户
        List<ApproverInfoVO> users = wbSysV3Mapper.selectUsersByDepartmentId(parentDept.getId());
        if (CollectionUtils.isEmpty(users)) {
            return null;
        }
        
        // 查找有权限的用户（优先选择第一个有权限的用户）
        for (ApproverInfoVO user : users) {
            // 判断是否有审批管理AMprem的页面权限
            System.out.print(String.format("username=%s", user.getLogin_name()));
            if(StringUtils.isEmpty(user.getPage_list()) || !Arrays.asList(user.getPage_list().split(",")).contains("AMprem")) {
                System.out.println("\t跳过.");
                continue;
            }else{
                System.out.println("\t有AMprem权限.");
            }

            ApproverInfoVO approver = new ApproverInfoVO();
            approver.setLogin_name(user.getLogin_name());
            approver.setNick_name(user.getNick_name());
            approver.setDepartment(user.getDepartment());
            approver.setFsuser_id(user.getFsuser_id()); // 设置飞书用户ID
            approver.setDepartmentName(parentDept.getDepartmentName());
            approver.setApprovalLevel(approvalLevel);

            /*
            // 检查用户权限，不判断是否有permissionMark页面或应用，只考虑AMprem权限
            boolean hasPermission = checkUserPermission(user, permissionMark, applicationType);
             */
            boolean hasPermission = true;
            approver.setHasPermission(hasPermission);
            approver.setPermissionDetail(hasPermission ? "具有相关权限" : "无相关权限");

            if (hasPermission) {
                return approver; // 返回第一个有权限的用户
            }
        }
        
        // 如果没有找到有权限的用户，返回第一个用户（作为候选审批人）
        ApproverInfoVO firstUser = users.get(0);
        ApproverInfoVO approver = new ApproverInfoVO();
        approver.setLogin_name(firstUser.getLogin_name());
        approver.setNick_name(firstUser.getNick_name());
        approver.setDepartment(firstUser.getDepartment());
        approver.setFsuser_id(firstUser.getFsuser_id()); // 设置飞书用户ID
        approver.setDepartmentName(parentDept.getDepartmentName());
        approver.setApprovalLevel(approvalLevel);
        approver.setHasPermission(false);
        approver.setPermissionDetail("无相关权限，需要上级审批");

        return approver;
    }

    /**
     * 查找二级审批人
     */
    private ApproverInfoVO findSecondLevelApprover(Integer departmentId, String permissionMark, Integer applicationType) {
        // 先找到一级上级部门
        MainSysDepartmentV3 firstParentDept = departmentMapper.selectParentDepartment(departmentId);
        if (firstParentDept == null) {
            return null;
        }
        
        // 再找到二级上级部门
        ApproverInfoVO secondLevelApprover = findApproverInDepartment(firstParentDept.getId(), permissionMark, applicationType, 2);
        if(secondLevelApprover != null && secondLevelApprover.getHasPermission()){
            return secondLevelApprover;
        }else {
            return null;
        }
    }

    /**
     * 检查用户对应的标识权限
     */
    private boolean checkUserPermission(ApproverInfoVO user, String permissionMark, Integer applicationType) {
        if (user == null || StringUtils.isEmpty(permissionMark)) {
            return false;
        }

        if (applicationType == 1) {
            // 界面权限：通过org_id匹配后检查page_list中拥有审核管理AMprem的权限
            if (StringUtils.isEmpty(user.getOrg_id())) {
                return false;
            }
            return user.getPage_list().contains(permissionMark);
        } else if (applicationType == 2) {
            // 应用权限：通过role_id匹配后检查appid和app_category
            if (StringUtils.isEmpty(user.getRole_id())) {
                return false;
            }
            if(permissionMark.length() == 5){
                return (StringUtils.isEmpty(user.getApp_group()) || Arrays.asList(user.getApp_group().split(",")).contains(permissionMark));
            }else{
                return (StringUtils.isEmpty(user.getApp_category()) || Arrays.asList(user.getApp_category().split(",")).contains(permissionMark));
            }
        }

        return false;
    }

    /**
     * 生成审批流程说明
     */
    private String generateApprovalProcess(List<ApproverInfoVO> approvers) {
        if (CollectionUtils.isEmpty(approvers)) {
            return "无审批流程";
        }
        
        StringBuilder process = new StringBuilder();
        for (int i = 0; i < approvers.size(); i++) {
            ApproverInfoVO approver = approvers.get(i);
            if (i > 0) {
                process.append(" -> ");
            }
            process.append("第").append(approver.getApprovalLevel()).append("级审批：")
                   .append(approver.getNick_name()).append("(").append(approver.getLogin_name()).append(")");
            if (!approver.getHasPermission()) {
                process.append("[需上级审批]");
            }
        }
        
        return process.toString();
    }
}
