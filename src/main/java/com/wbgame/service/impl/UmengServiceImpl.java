package com.wbgame.service.impl;

import com.alibaba.fastjson.JSON;import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ocean.rawsdk.ApiExecutor;
import com.alibaba.ocean.rawsdk.client.exception.OceanException;
import com.umeng.uapp.param.UmengUappEventCreateParam;
import com.umeng.uapp.param.UmengUappEventCreateResult;
import com.wbgame.mapper.adb.DnwxBiAdtMapper;
import com.wbgame.mapper.adb.DnwxBiMapper;
import com.wbgame.mapper.adb.UmengMonitorMapper;
import com.wbgame.mapper.haiwai2adb.Haiwai2DnwxBiUsMapper;
import com.wbgame.mapper.haiwaiad.HaiwaiCfgMapper;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.CommonMapper;
import com.wbgame.mapper.master.ViolationRecordMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.Ad4399Run;
import com.wbgame.pojo.UmengAdIncomeVo;
import com.wbgame.pojo.XiaomiViolationRecordVo;
import com.wbgame.pojo.advert.ChannelAdDataVo;
import com.wbgame.service.CommonService;
import com.wbgame.service.UmengService;
import com.wbgame.service.jettison.report.SpendReportService;
import com.wbgame.task.UsertagTask;
import com.wbgame.utils.*;
import com.wbgame.utils.jettison.JsonUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wbgame.controller.advert.query.transport.UmengAdvIncomeController.UMENG_CHANNEL_MEDIA_MAP;


@Service("umengService")
public class UmengServiceImpl implements UmengService {

	private static Logger logger = LoggerFactory.getLogger(UmengServiceImpl.class);

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	@Autowired
	private AdMapper adMapper;
	@Autowired
	private CommonService commonService;

	@Autowired
	private TfxtMapper tfxtMapper;

	@Autowired
	private CommonMapper commonMapper;
	@Autowired
	private UmengMonitorMapper umengMonitorMapper;

    @Autowired
    private DnwxBiMapper dnwxBiMapper;

	@Autowired
	SpendReportService spendReportService;

	@Autowired
	DnwxBiAdtMapper dnwxBiAdtMapper;
	@Autowired
	HaiwaiCfgMapper haiwaiCfgMapper;
	@Autowired
	Haiwai2DnwxBiUsMapper haiwai2DnwxBiUsMapper;

	@Autowired
	private ViolationRecordMapper violationRecordMapper;

	public final static Map<String, String> AD_SHOW_TYPE_MAP = new HashMap<String, String>() {{
		put("splash_splash", "splash");
		put("natSplash_splash", "nativesplash");
		put("yuans_splash","nativesplash");
		put("plaque_plaque", "plaque");
		put("msg_plaque", "nativenewplaque");
		put("yuans_plaque", "nativenewplaque");
		put("plaqueVideo_plaque", "plaquevideo");
		put("banner_banner", "banner");
		put("msg_banner", "nativenewbanner");
		put("yuans_banner","nativenewbanner");
		put("video_video", "video");
		put("msg_msg", "nativemsg");
		put("yuans_msg", "nativemsg");
		put("icon_icon", "suspendicon");
		put("boxBanner_banner", "suspendicon");
		put("boxBanner_plaque", "suspendicon");
		put("boxPlaque_plaque", "suspendicon");
		put("yuans_box", "suspendicon");
//		put("msg_icon","suspendicon");
		put("natPlaque_plaque", "nativenewplaque");
		put("natBanner_banner", "nativenewbanner");
		//20240911新增：匹配数据 plaque_msg
		put("plaque_msg", "plaque");

	}};

	@Override
	public String syncChannelAppData(String day, String channel) {

		List<UmengAdIncomeVo> dataList = new ArrayList<UmengAdIncomeVo>();
		Map<String, UmengAdIncomeVo> valMap = new HashMap<String, UmengAdIncomeVo>();
		JSONObject ret = new JSONObject();
		//获取日期格式为 yyyy-MM-dd 的日期
		String hyphenDate = DateUtils.changeDateStringToHyphen(day);
		// 通过appkey来聚集多条广告类型内容到一个应用中
//		String query = "select ad_code mapkey,appkey,adtype,status from umeng_adcode_list where channel = '"+channel+"' ";
//		Map<String, Map<String, Object>> codeMap = adMapper.queryListMapOfKey(query);

		String query = "SELECT sdk_code mapkey, CONCAT(sdk_adtype,'_',open_type) as type_match, 'OPEN' as `status`, b.umeng_key appkey,a.appid  \n" +
				"FROM dnwx_cfg.dn_extend_adsid_manage a\n" +
				"left join app_info b\n" +
				"on a.appid = b.id where a.cha_id = '"+channel+"' ";
		List<Map<String, Object>> codeMapList = adMapper.queryListMap(query);
		if(CollectionUtils.isEmpty(codeMapList)){
			ret.put("ret",0);
			ret.put("msg","channel:"+channel+","+day+"未获取到数据，请切换日期重试！");
			return ret.toJSONString();
		}

		Map<String, Map<String, Object>> codeMap = codeMapList.stream()
				.filter(map -> AD_SHOW_TYPE_MAP.containsKey(map.getOrDefault("type_match", "")))
				.peek(map -> map.put("adtype", AD_SHOW_TYPE_MAP.get(map.get("type_match"))))
				.collect(Collectors.toMap(map -> (String) map.get("mapkey"), map -> map, (k, v) -> v));

		JSONArray array = new JSONArray();
		if("oppo".equals(channel) ||"oppoml".equals(channel)||"oppo2".equals(channel)||"oppomj".equals(channel) || "h5_oppo".equals(channel) || "uc".equals(channel)){
			// 通过oppo api拉取数据
			//userID :api_key
			array = getCommonDataFromCashTotal(day,"oppo", channel, codeMap.keySet());
			if(array == null || array.isEmpty()){
				ret.put("ret",0);
				ret.put("msg","channel:"+channel+","+day+"未获取到数据，请切换日期重试！");
				return ret.toJSONString();
			}
//			List<Map<String, Object>> maps = adMapper.queryListMap("SELECT placementid, posName FROM dnwx_cfg.`adv_adcode_oppo_info`");
//			Map<Object, Object> collect = maps.stream().collect(Collectors.toMap(map -> map.get("placementid"), map -> map.get("posName")));
//			array.stream().map(o -> (JSONObject) o).forEach(o -> o.put("posName", collect.get(o.getString("posId"))));
		}

		if("vivo".equals(channel)||"vivoml".equals(channel)||"vivo2".equals(channel)||"vivoml2".equals(channel)){
			// 通过vivo api拉取数据
			array = getCommonDataFromCashTotal(day,"vivo", channel, codeMap.keySet());
			if(array == null || array.isEmpty()){
				ret.put("ret",0);
				ret.put("msg","channel:"+channel+","+day+"未获取到数据，请切换日期重试！");
				return ret.toJSONString();
			}
		}

		// h5_vivo + honor 渠道，从变现明细表中获取到数据
		if("h5_vivo".equals(channel)){
			// vivo 小游戏，通过自有表获取
			array = getApiDataNewFromCash(day, channel);
			if(array == null || array.isEmpty()){
				ret.put("ret",0);
				ret.put("msg","channel:"+channel+","+day+"未获取到数据，请切换日期重试！");
				return ret.toJSONString();
			}
		}

		if ("honor".equals(channel)) {
			// 通过荣耀 拉取数据
			String tdate = day.substring(0, 4)+"-"+day.substring(4, 6)+"-"+day.substring(6, 8);
			//改成使用api方式处理
			array = getCommonDataFromCashTotal(day,"honor", channel, codeMap.keySet());
			if(array == null || array.isEmpty()){
				ret.put("ret",0);
				ret.put("msg","channel:"+channel+","+tdate+"未获取到数据，请切换日期重试！");
				return ret.toJSONString();
			}
		}

		if("xiaomi".equals(channel)||"xmyy".equals(channel)||"xiaomimj".equals(channel)||"xiaomiml".equals(channel)||"xiaomiwt".equals(channel)||"xiaomiml2".equals(channel)){
			// 通过小米 拉取数据
			String tdate = day.substring(0, 4)+"-"+day.substring(4, 6)+"-"+day.substring(6, 8);
			//改成使用api方式处理
			array = getCommonDataFromCashTotal(day,"Mi", channel, codeMap.keySet());
			if(array == null || array.isEmpty()){
				ret.put("ret",0);
				ret.put("msg","channel:"+channel+","+tdate+"未获取到数据，请切换日期重试！");
				return ret.toJSONString();
			}
		}

		if("ad4399".equals(channel)){
			// 通过4399 拉取数据
			String tdate = day.substring(0, 4)+"-"+day.substring(4, 6)+"-"+day.substring(6, 8);
			array = get4399ApiData(tdate);
			if(array == null || array.isEmpty()){
				ret.put("ret",0);
				ret.put("msg","channel:"+channel+","+tdate+"未获取到数据，请切换日期重试！");
				return ret.toJSONString();
			}
		}

		if("huawei".equals(channel)||"huaweimj".equals(channel)||"huawei2".equals(channel) || "h5_huawei".equals(channel)){
			// 通过huawei api拉取数据
			array = getCommonDataFromCashTotal(day,"Huawei", channel, codeMap.keySet());
			if(array == null || array.isEmpty()){
				ret.put("ret",0);
				ret.put("msg","channel:"+channel+","+day+"未获取到数据，请切换日期重试！");
				return ret.toJSONString();
			}
		}
		// 特殊处理，渠道为google_huawei等海外渠道时 新增活跃 从 新增活跃汇总查询-大数据 获取 -20250325
		boolean isHaiwai = false;
		if ("google_huawei".equals(channel) || "google_xiaomi".equals(channel)) {
			isHaiwai = true;
		}
		if (isHaiwai) {
			array = getCommonDataFromCashTotalOfCountry(day,channel, codeMap.keySet());
			if(array == null || array.isEmpty()){
				ret.put("ret",0);
				ret.put("msg","channel:"+channel+","+day+"未获取到数据，请切换日期重试！");
				return ret.toJSONString();
			}
		}
		logger.debug("array:{}", JSON.toJSONString(array));

		for (int i = 0; i < array.size(); i++) {
			JSONObject object = array.getJSONObject(i);

			String[] vals = {object.getString("posId"), object.getString("view"), object.getString("income"), object.getString("click"), object.getString("request_count"), object.getString("fill_count")};


			// 得到广告位ID的map对象
			Map<String, Object> code = codeMap.get(vals[0]);
			if(code == null){
				//oppo2先特殊处理
				if ("oppo2".equals(channel)||"oppoml".equals(channel)||"oppo".equals(channel)||"oppomj".equals(channel)
						||"google_huawei".equals(channel) || "h5_vivo".equals(channel) || "honor".equals(channel)){
					logger.warn("**************************未找到广告位ID："+vals[0]+" ，跳过");
					continue;
				}else {
					return "{\"ret\":0, \"msg\":\"广告位ID "+vals[0]+" 没有找到，请仔细核对！\"}";
				}
			}
			// code处于停用状态，不录入
			if("close".equals(code.get("status").toString()))
				continue;

			String appkey = (code.get("appkey") != null?code.get("appkey").toString():"");
			String adtype = code.get("adtype").toString();
			UmengAdIncomeVo ua = valMap.get(appkey);

			if(isHaiwai){
				/** 处理 appkey 为空字符被合并为一条的情况，appkey为空字符串，使用appid作为替代 */
				appkey = (code.get("appkey") == null || code.get("appkey").toString().isEmpty() ? code.get("appid")+"" : code.get("appkey").toString());
				ua = valMap.get(object.getString("posId")+object.getString("country"));
			}

			if(ua == null || ua.getAppkey() == null){
				// 初始化应用数据
				ua = new UmengAdIncomeVo();
				ua.setTdate(day);
				ua.setChannel(channel);
				ua.setAppkey(appkey);
				ua.setAdcode(vals[0]);
				if(isHaiwai){
					ua.setAppid(code.get("appid")+"");
					ua.setCountry(object.getString("country"));
				}

				ua.setBanner_show("0");
				ua.setPlaque_show("0");
				ua.setSplash_show("0");
				ua.setVideo_show("0");
				ua.setNative_banner_show("0");
				ua.setNative_plaque_show("0");
				ua.setNative_splash_show("0");
				ua.setPlaque_video_show("0");
				ua.setNative_msg_show("0");

				//2021-08-26 添加
				ua.setSystem_splash_show("0");
				ua.setNative_new_banner_show("0");
				ua.setNative_new_plaque_show("0");
				ua.setSuspend_icon_show("0");

				ua.setBanner_income("0");
				ua.setPlaque_income("0");
				ua.setSplash_income("0");
				ua.setVideo_income("0");
				ua.setNative_banner_income("0");
				ua.setNative_plaque_income("0");
				ua.setNative_splash_income("0");
				ua.setPlaque_video_income("0");
				ua.setNative_msg_income("0");

				//2021-08-26 添加
				ua.setSystem_splash_income("0");
				ua.setNative_new_banner_income("0");
				ua.setNative_new_plaque_income("0");
				ua.setSuspend_icon_income("0");

				ua.setBanner_ecpm("0");
				ua.setPlaque_ecpm("0");
				ua.setSplash_ecpm("0");
				ua.setVideo_ecpm("0");
				ua.setNative_banner_ecpm("0");
				ua.setNative_plaque_ecpm("0");
				ua.setNative_splash_ecpm("0");
				ua.setPlaque_video_ecpm("0");
				ua.setNative_msg_ecpm("0");
				//2021-08-26 添加
				ua.setSystem_splash_ecpm("0");
				ua.setNative_new_banner_ecpm("0");
				ua.setNative_new_plaque_ecpm("0");
				ua.setSuspend_icon_ecpm("0");


				ua.setBanner_click("0");
				ua.setPlaque_click("0");
				ua.setSplash_click("0");
				ua.setVideo_click("0");
				ua.setNative_banner_click("0");
				ua.setNative_plaque_click("0");
				ua.setNative_splash_click("0");
				ua.setPlaque_video_click("0");
				ua.setNative_msg_click("0");
				//2021-08-26 添加
				ua.setSystem_splash_click("0");
				ua.setNative_new_banner_click("0");
				ua.setNative_new_plaque_click("0");
				ua.setSuspend_icon_click("0");

				//2024-10-08 添加 请求量和填充量
				ua.setBanner_request("0");
				ua.setPlaque_request("0");
				ua.setSplash_request("0");
				ua.setVideo_request("0");
				ua.setNative_banner_request("0");
				ua.setNative_plaque_request("0");
				ua.setNative_splash_request("0");
				ua.setPlaque_video_request("0");
				ua.setNative_msg_request("0");
				ua.setSystem_splash_request("0");
				ua.setNative_new_plaque_request("0");
				ua.setNative_new_banner_request("0");
				ua.setSuspend_icon_request("0");

				ua.setBanner_fill("0");
				ua.setPlaque_fill("0");
				ua.setSplash_fill("0");
				ua.setVideo_fill("0");
				ua.setNative_banner_fill("0");
				ua.setNative_plaque_fill("0");
				ua.setNative_splash_fill("0");
				ua.setPlaque_video_fill("0");
				ua.setNative_msg_fill("0");
				ua.setSystem_splash_fill("0");
				ua.setNative_new_plaque_fill("0");
				ua.setNative_new_banner_fill("0");
				ua.setSuspend_icon_fill("0");
			}

			// ***这里相同的一个应用数据，show和income数值需要叠加，并且ecpm需要自己计算***
			BigDecimal k = new BigDecimal(1000);
				if("banner".equals(adtype)){
					ua.setBanner_show(new BigDecimal(ua.getBanner_show())
						.add(new BigDecimal(vals[1])).toString());
					ua.setBanner_income(new BigDecimal(ua.getBanner_income())
						.add(new BigDecimal(vals[2])).toString());
					ua.setBanner_click(new BigDecimal(ua.getBanner_click())
						.add(new BigDecimal(vals[3])).toString());
					ua.setBanner_request(new BigDecimal(ua.getBanner_request()).add(new BigDecimal(vals[4])).toString());
					ua.setBanner_fill(new BigDecimal(ua.getBanner_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getBanner_show()) != 0
						&& Double.valueOf(ua.getBanner_income()) != 0){

					ua.setBanner_ecpm(
						new BigDecimal(ua.getBanner_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getBanner_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}
			}else if("plaque".equals(adtype)){
				ua.setPlaque_show(new BigDecimal(ua.getPlaque_show())
					.add(new BigDecimal(vals[1])).toString());
				ua.setPlaque_income(new BigDecimal(ua.getPlaque_income())
					.add(new BigDecimal(vals[2])).toString());
				ua.setPlaque_click(new BigDecimal(ua.getPlaque_click())
					.add(new BigDecimal(vals[3])).toString());
				ua.setPlaque_request(new BigDecimal(ua.getPlaque_request()).add(new BigDecimal(vals[4])).toString());
				ua.setPlaque_fill(new BigDecimal(ua.getPlaque_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getPlaque_show()) != 0
						&& Double.valueOf(ua.getPlaque_income()) != 0){

					ua.setPlaque_ecpm(
						new BigDecimal(ua.getPlaque_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getPlaque_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}

			}else if("splash".equals(adtype)){
				ua.setSplash_show(new BigDecimal(ua.getSplash_show())
					.add(new BigDecimal(vals[1])).toString());
				ua.setSplash_income(new BigDecimal(ua.getSplash_income())
					.add(new BigDecimal(vals[2])).toString());
				ua.setSplash_click(new BigDecimal(ua.getSplash_click())
					.add(new BigDecimal(vals[3])).toString());
				ua.setSplash_request(new BigDecimal(ua.getSplash_request()).add(new BigDecimal(vals[4])).toString());
				ua.setSplash_fill(new BigDecimal(ua.getSplash_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getSplash_show()) != 0
						&& Double.valueOf(ua.getSplash_income()) != 0){

					ua.setSplash_ecpm(
						new BigDecimal(ua.getSplash_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getSplash_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}
			}else if("video".equals(adtype)){
				ua.setVideo_show(new BigDecimal(ua.getVideo_show())
					.add(new BigDecimal(vals[1])).toString());
				ua.setVideo_income(new BigDecimal(ua.getVideo_income())
					.add(new BigDecimal(vals[2])).toString());
				ua.setVideo_click(new BigDecimal(ua.getVideo_click())
					.add(new BigDecimal(vals[3])).toString());
				ua.setVideo_request(new BigDecimal(ua.getVideo_request()).add(new BigDecimal(vals[4])).toString());
				ua.setVideo_fill(new BigDecimal(ua.getVideo_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getVideo_show()) != 0
						&& Double.valueOf(ua.getVideo_income()) != 0){

					ua.setVideo_ecpm(
						new BigDecimal(ua.getVideo_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getVideo_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}
			}else if("nativebanner".equals(adtype)){
				ua.setNative_banner_show(new BigDecimal(ua.getNative_banner_show())
					.add(new BigDecimal(vals[1])).toString());
				ua.setNative_banner_income(new BigDecimal(ua.getNative_banner_income())
					.add(new BigDecimal(vals[2])).toString());
				ua.setNative_banner_click(new BigDecimal(ua.getNative_banner_click())
					.add(new BigDecimal(vals[3])).toString());
				ua.setNative_banner_request(new BigDecimal(ua.getNative_banner_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_banner_fill(new BigDecimal(ua.getNative_banner_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getNative_banner_show()) != 0
						&& Double.valueOf(ua.getNative_banner_income()) != 0){

					ua.setNative_banner_ecpm(
						new BigDecimal(ua.getNative_banner_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getNative_banner_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}
			}else if("nativeplaque".equals(adtype)){
				ua.setNative_plaque_show(new BigDecimal(ua.getNative_plaque_show())
					.add(new BigDecimal(vals[1])).toString());
				ua.setNative_plaque_income(new BigDecimal(ua.getNative_plaque_income())
					.add(new BigDecimal(vals[2])).toString());
				ua.setNative_plaque_click(new BigDecimal(ua.getNative_plaque_click())
					.add(new BigDecimal(vals[3])).toString());
				ua.setNative_plaque_request(new BigDecimal(ua.getNative_plaque_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_plaque_fill(new BigDecimal(ua.getNative_plaque_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getNative_plaque_show()) != 0
						&& Double.valueOf(ua.getNative_plaque_income()) != 0){

					ua.setNative_plaque_ecpm(
						new BigDecimal(ua.getNative_plaque_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getNative_plaque_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}
			}else if("nativesplash".equals(adtype)){
				ua.setNative_splash_show(new BigDecimal(ua.getNative_splash_show())
					.add(new BigDecimal(vals[1])).toString());
				ua.setNative_splash_income(new BigDecimal(ua.getNative_splash_income())
					.add(new BigDecimal(vals[2])).toString());
				ua.setNative_splash_click(new BigDecimal(ua.getNative_splash_click())
					.add(new BigDecimal(vals[3])).toString());
				ua.setNative_splash_request(new BigDecimal(ua.getNative_splash_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_splash_fill(new BigDecimal(ua.getNative_splash_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getNative_splash_show()) != 0
						&& Double.valueOf(ua.getNative_splash_income()) != 0){

					ua.setNative_splash_ecpm(
						new BigDecimal(ua.getNative_splash_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getNative_splash_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}
			}else if("plaquevideo".equals(adtype)){
				ua.setPlaque_video_show(new BigDecimal(ua.getPlaque_video_show())
						.add(new BigDecimal(vals[1])).toString());
				ua.setPlaque_video_income(new BigDecimal(ua.getPlaque_video_income())
					.add(new BigDecimal(vals[2])).toString());
				ua.setPlaque_video_click(new BigDecimal(ua.getPlaque_video_click())
					.add(new BigDecimal(vals[3])).toString());
				ua.setPlaque_video_request(new BigDecimal(ua.getPlaque_video_request()).add(new BigDecimal(vals[4])).toString());
				ua.setPlaque_video_fill(new BigDecimal(ua.getPlaque_video_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getPlaque_video_show()) != 0
						&& Double.valueOf(ua.getPlaque_video_income()) != 0){

					ua.setPlaque_video_ecpm(
						new BigDecimal(ua.getPlaque_video_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getPlaque_video_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}

			}else if("nativemsg".equals(adtype)){
				ua.setNative_msg_show(new BigDecimal(ua.getNative_msg_show())
						.add(new BigDecimal(vals[1])).toString());
				ua.setNative_msg_income(new BigDecimal(ua.getNative_msg_income())
					.add(new BigDecimal(vals[2])).toString());
				ua.setNative_msg_click(new BigDecimal(ua.getNative_msg_click())
					.add(new BigDecimal(vals[3])).toString());
				ua.setNative_msg_request(new BigDecimal(ua.getNative_msg_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_msg_fill(new BigDecimal(ua.getNative_msg_fill()).add(new BigDecimal(vals[5])).toString());
				if(Integer.valueOf(ua.getNative_msg_show()) != 0
						&& Double.valueOf(ua.getNative_msg_income()) != 0){

					ua.setNative_msg_ecpm(
						new BigDecimal(ua.getNative_msg_income())
						.multiply(k)
						.divide(new BigDecimal(ua.getNative_msg_show()), 2, RoundingMode.FLOOR)
						.toString()
					);
				}
			}
			//2021-08-26 添加
			else if("systemsplash".equals(adtype)){
					ua.setSystem_splash_show(new BigDecimal(ua.getSystem_splash_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setSystem_splash_income(new BigDecimal(ua.getSystem_splash_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setSystem_splash_click(new BigDecimal(ua.getSystem_splash_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setSystem_splash_request(new BigDecimal(ua.getSystem_splash_request()).add(new BigDecimal(vals[4])).toString());
					ua.setSystem_splash_fill(new BigDecimal(ua.getSystem_splash_fill()).add(new BigDecimal(vals[5])).toString());
					if(Integer.valueOf(ua.getSystem_splash_show()) != 0
							&& Double.valueOf(ua.getSystem_splash_income()) != 0){

						ua.setSystem_splash_ecpm(
								new BigDecimal(ua.getSystem_splash_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getSystem_splash_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
			}else if("nativenewplaque".equals(adtype)){
					ua.setNative_new_plaque_show(new BigDecimal(ua.getNative_new_plaque_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setNative_new_plaque_income(new BigDecimal(ua.getNative_new_plaque_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setNative_new_plaque_click(new BigDecimal(ua.getNative_new_plaque_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setNative_new_plaque_request(new BigDecimal(ua.getNative_new_plaque_request()).add(new BigDecimal(vals[4])).toString());
					ua.setNative_new_plaque_fill(new BigDecimal(ua.getNative_new_plaque_fill()).add(new BigDecimal(vals[5])).toString());
					if(Integer.valueOf(ua.getNative_new_plaque_show()) != 0
							&& Double.valueOf(ua.getNative_new_plaque_income()) != 0){

						ua.setNative_new_plaque_ecpm(
								new BigDecimal(ua.getNative_new_plaque_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getNative_new_plaque_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
			}else if("nativenewbanner".equals(adtype)){
					ua.setNative_new_banner_show(new BigDecimal(ua.getNative_new_banner_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setNative_new_banner_income(new BigDecimal(ua.getNative_new_banner_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setNative_new_banner_click(new BigDecimal(ua.getNative_new_banner_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setNative_new_banner_request(new BigDecimal(ua.getNative_new_banner_request()).add(new BigDecimal(vals[4])).toString());
					ua.setNative_new_banner_fill(new BigDecimal(ua.getNative_new_banner_fill()).add(new BigDecimal(vals[5])).toString());
					if(Integer.valueOf(ua.getNative_new_banner_show()) != 0
							&& Double.valueOf(ua.getNative_new_banner_income()) != 0){

						ua.setNative_new_banner_ecpm(
								new BigDecimal(ua.getNative_new_banner_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getNative_new_banner_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
			} else if("suspendicon".equals(adtype)){
					ua.setSuspend_icon_show(new BigDecimal(ua.getSuspend_icon_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setSuspend_icon_income(new BigDecimal(ua.getSuspend_icon_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setSuspend_icon_click(new BigDecimal(ua.getSuspend_icon_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setSuspend_icon_request(new BigDecimal(ua.getSuspend_icon_request()).add(new BigDecimal(vals[4])).toString());
					ua.setSuspend_icon_fill(new BigDecimal(ua.getSuspend_icon_fill()).add(new BigDecimal(vals[5])).toString());
					if(Integer.valueOf(ua.getSuspend_icon_show()) != 0
							&& Double.valueOf(ua.getSuspend_icon_income()) != 0){

						ua.setSuspend_icon_ecpm(
								new BigDecimal(ua.getSuspend_icon_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getSuspend_icon_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
			}

			if (isHaiwai){
				valMap.put(object.getString("posId")+object.getString("country"), ua);
			}else{
				valMap.put(appkey, ua);
			}
		}
		logger.debug("valMap:{},result:{}",valMap.size(), JSON.toJSONString(valMap));

		// 特殊处理 google_huawei等海外渠道，将相同 appkey 的数据合并
		if (isHaiwai) {
				
			Map<String, UmengAdIncomeVo> newValMap = new HashMap<>();
			for (Entry<String, UmengAdIncomeVo> entry : valMap.entrySet()) {
				UmengAdIncomeVo value = entry.getValue();
				String appkey = value.getAppkey();
				String country = value.getCountry();
				String compositeKey = appkey + "_" + country; // 使用 appkey+country 作为复合主键
				
				if (newValMap.containsKey(compositeKey)) {
					// 已存在相同 appkey+country 的数据，需要合并
					UmengAdIncomeVo existingVo = newValMap.get(compositeKey);
					

					// 合并展示数据
					existingVo.setBanner_show(addBigDecimal(existingVo.getBanner_show(), value.getBanner_show()));
					existingVo.setPlaque_show(addBigDecimal(existingVo.getPlaque_show(), value.getPlaque_show()));
					existingVo.setSplash_show(addBigDecimal(existingVo.getSplash_show(), value.getSplash_show()));
					existingVo.setVideo_show(addBigDecimal(existingVo.getVideo_show(), value.getVideo_show()));
					existingVo.setNative_banner_show(addBigDecimal(existingVo.getNative_banner_show(), value.getNative_banner_show()));
					existingVo.setNative_plaque_show(addBigDecimal(existingVo.getNative_plaque_show(), value.getNative_plaque_show()));
					existingVo.setNative_splash_show(addBigDecimal(existingVo.getNative_splash_show(), value.getNative_splash_show()));
					existingVo.setPlaque_video_show(addBigDecimal(existingVo.getPlaque_video_show(), value.getPlaque_video_show()));
					existingVo.setNative_msg_show(addBigDecimal(existingVo.getNative_msg_show(), value.getNative_msg_show()));
					existingVo.setSystem_splash_show(addBigDecimal(existingVo.getSystem_splash_show(), value.getSystem_splash_show()));
					existingVo.setNative_new_banner_show(addBigDecimal(existingVo.getNative_new_banner_show(), value.getNative_new_banner_show()));
					existingVo.setNative_new_plaque_show(addBigDecimal(existingVo.getNative_new_plaque_show(), value.getNative_new_plaque_show()));
					existingVo.setSuspend_icon_show(addBigDecimal(existingVo.getSuspend_icon_show(), value.getSuspend_icon_show()));
					
					// 合并收入数据
					existingVo.setBanner_income(addBigDecimal(existingVo.getBanner_income(), value.getBanner_income()));
					existingVo.setPlaque_income(addBigDecimal(existingVo.getPlaque_income(), value.getPlaque_income()));
					existingVo.setSplash_income(addBigDecimal(existingVo.getSplash_income(), value.getSplash_income()));
					existingVo.setVideo_income(addBigDecimal(existingVo.getVideo_income(), value.getVideo_income()));
					existingVo.setNative_banner_income(addBigDecimal(existingVo.getNative_banner_income(), value.getNative_banner_income()));
					existingVo.setNative_plaque_income(addBigDecimal(existingVo.getNative_plaque_income(), value.getNative_plaque_income()));
					existingVo.setNative_splash_income(addBigDecimal(existingVo.getNative_splash_income(), value.getNative_splash_income()));
					existingVo.setPlaque_video_income(addBigDecimal(existingVo.getPlaque_video_income(), value.getPlaque_video_income()));
					existingVo.setNative_msg_income(addBigDecimal(existingVo.getNative_msg_income(), value.getNative_msg_income()));
					existingVo.setSystem_splash_income(addBigDecimal(existingVo.getSystem_splash_income(), value.getSystem_splash_income()));
					existingVo.setNative_new_banner_income(addBigDecimal(existingVo.getNative_new_banner_income(), value.getNative_new_banner_income()));
					existingVo.setNative_new_plaque_income(addBigDecimal(existingVo.getNative_new_plaque_income(), value.getNative_new_plaque_income()));
					existingVo.setSuspend_icon_income(addBigDecimal(existingVo.getSuspend_icon_income(), value.getSuspend_icon_income()));
					
					// 合并点击数据
					existingVo.setBanner_click(addBigDecimal(existingVo.getBanner_click(), value.getBanner_click()));
					existingVo.setPlaque_click(addBigDecimal(existingVo.getPlaque_click(), value.getPlaque_click()));
					existingVo.setSplash_click(addBigDecimal(existingVo.getSplash_click(), value.getSplash_click()));
					existingVo.setVideo_click(addBigDecimal(existingVo.getVideo_click(), value.getVideo_click()));
					existingVo.setNative_banner_click(addBigDecimal(existingVo.getNative_banner_click(), value.getNative_banner_click()));
					existingVo.setNative_plaque_click(addBigDecimal(existingVo.getNative_plaque_click(), value.getNative_plaque_click()));
					existingVo.setNative_splash_click(addBigDecimal(existingVo.getNative_splash_click(), value.getNative_splash_click()));
					existingVo.setPlaque_video_click(addBigDecimal(existingVo.getPlaque_video_click(), value.getPlaque_video_click()));
					existingVo.setNative_msg_click(addBigDecimal(existingVo.getNative_msg_click(), value.getNative_msg_click()));
					existingVo.setSystem_splash_click(addBigDecimal(existingVo.getSystem_splash_click(), value.getSystem_splash_click()));
					existingVo.setNative_new_banner_click(addBigDecimal(existingVo.getNative_new_banner_click(), value.getNative_new_banner_click()));
					existingVo.setNative_new_plaque_click(addBigDecimal(existingVo.getNative_new_plaque_click(), value.getNative_new_plaque_click()));
					existingVo.setSuspend_icon_click(addBigDecimal(existingVo.getSuspend_icon_click(), value.getSuspend_icon_click()));
					
					// 合并请求和填充数据
					existingVo.setBanner_request(addBigDecimal(existingVo.getBanner_request(), value.getBanner_request()));
					existingVo.setPlaque_request(addBigDecimal(existingVo.getPlaque_request(), value.getPlaque_request()));
					existingVo.setSplash_request(addBigDecimal(existingVo.getSplash_request(), value.getSplash_request()));
					existingVo.setVideo_request(addBigDecimal(existingVo.getVideo_request(), value.getVideo_request()));
					existingVo.setNative_banner_request(addBigDecimal(existingVo.getNative_banner_request(), value.getNative_banner_request()));
					existingVo.setNative_plaque_request(addBigDecimal(existingVo.getNative_plaque_request(), value.getNative_plaque_request()));
					existingVo.setNative_splash_request(addBigDecimal(existingVo.getNative_splash_request(), value.getNative_splash_request()));
					existingVo.setPlaque_video_request(addBigDecimal(existingVo.getPlaque_video_request(), value.getPlaque_video_request()));
					existingVo.setNative_msg_request(addBigDecimal(existingVo.getNative_msg_request(), value.getNative_msg_request()));
					existingVo.setSystem_splash_request(addBigDecimal(existingVo.getSystem_splash_request(), value.getSystem_splash_request()));
					existingVo.setNative_new_plaque_request(addBigDecimal(existingVo.getNative_new_plaque_request(), value.getNative_new_plaque_request()));
					existingVo.setNative_new_banner_request(addBigDecimal(existingVo.getNative_new_banner_request(), value.getNative_new_banner_request()));
					existingVo.setSuspend_icon_request(addBigDecimal(existingVo.getSuspend_icon_request(), value.getSuspend_icon_request()));
					
					existingVo.setBanner_fill(addBigDecimal(existingVo.getBanner_fill(), value.getBanner_fill()));
					existingVo.setPlaque_fill(addBigDecimal(existingVo.getPlaque_fill(), value.getPlaque_fill()));
					existingVo.setSplash_fill(addBigDecimal(existingVo.getSplash_fill(), value.getSplash_fill()));
					existingVo.setVideo_fill(addBigDecimal(existingVo.getVideo_fill(), value.getVideo_fill()));
					existingVo.setNative_banner_fill(addBigDecimal(existingVo.getNative_banner_fill(), value.getNative_banner_fill()));
					existingVo.setNative_plaque_fill(addBigDecimal(existingVo.getNative_plaque_fill(), value.getNative_plaque_fill()));
					existingVo.setNative_splash_fill(addBigDecimal(existingVo.getNative_splash_fill(), value.getNative_splash_fill()));
					existingVo.setPlaque_video_fill(addBigDecimal(existingVo.getPlaque_video_fill(), value.getPlaque_video_fill()));
					existingVo.setNative_msg_fill(addBigDecimal(existingVo.getNative_msg_fill(), value.getNative_msg_fill()));
					existingVo.setSystem_splash_fill(addBigDecimal(existingVo.getSystem_splash_fill(), value.getSystem_splash_fill()));
					existingVo.setNative_new_plaque_fill(addBigDecimal(existingVo.getNative_new_plaque_fill(), value.getNative_new_plaque_fill()));
					existingVo.setNative_new_banner_fill(addBigDecimal(existingVo.getNative_new_banner_fill(), value.getNative_new_banner_fill()));
					existingVo.setSuspend_icon_fill(addBigDecimal(existingVo.getSuspend_icon_fill(), value.getSuspend_icon_fill()));
					
				} else {
					// 不存在相同 appkey+country 的数据，直接添加
					newValMap.put(compositeKey, value);
				}
			}
			// 用合并后的 Map 替换原来的 Map
			valMap = newValMap;
			logger.debug("合并后的 valMap:{},result:{}", valMap.size(), JSON.toJSONString(valMap));
		}



		// 友盟key对应的活跃用户数据
		String sql = "select add_num addnum,tdate,app_key appkey,act_num actnum from umeng_user_channel_total"
				+ " where tdate = '"+day+"' and install_channel = '"+channel+"'";
		//特殊处理 渠道为huawei时 新增活跃以 huawei+huaweiml
		if ("huawei".equals(channel)){
			sql = "select IFNULL(sum(add_num),0) addnum,tdate,app_key appkey,IFNULL(sum(act_num),0) actnum from umeng_user_channel_total "
					+ " where tdate = '"+day+"' and install_channel in('huawei','huaweiml') GROUP BY tdate,app_key ";
		}
		if ("huawei2".equals(channel)){
			sql = "select IFNULL(sum(add_num),0) addnum,tdate,app_key appkey,IFNULL(sum(act_num),0) actnum from umeng_user_channel_total "
					+ " where tdate = '"+day+"' and install_channel in('huawei2','huaweiml2') GROUP BY tdate,app_key ";
		}
		//特殊处理 渠道为oppomj时 新增活跃以oppomj+oppomj2
		if ("oppomj".equals(channel)){
			sql = "select IFNULL(sum(add_num),0) addnum,tdate,app_key appkey,IFNULL(sum(act_num),0) actnum from umeng_user_channel_total "
					+ " where tdate = '"+day+"' and install_channel in('oppomj','oppomj2') GROUP BY tdate,app_key ";
		}
		//特殊处理 渠道为h5_vivo时 新增活跃从vivo小游戏表中获取
		if ("h5_vivo".equals(channel)){
			sql = "SELECT tdate, channel, new_user addnum, day_active_user actnum,t2.appid,t2.appid appkey  FROM `vivo_quick_game_info` t1\n" +
					"LEFT JOIN adv_platform_app_info t2 on t1.tpackage = t2.packagename\n" +
					"where t1.tdate = '"+day+"' and t2.appid is not null";
		}
		//特殊处理：渠道为h5_oppo,uc,h5_huawei时 新增活跃 从 渠道详情页转化分析 获取
		if ("h5_oppo".equals(channel) || "uc".equals(channel) || "h5_huawei".equals(channel)){
			// 调试时注意 day 时间格式是否正确合理
			sql = "SELECT a.tdate,a.appid,a.appid AS appkey,b.channel,IFNULL(SUM(CAST(dau AS UNSIGNED)), 0) AS actnum,IFNULL(SUM(CAST(addnum AS UNSIGNED)), 0) AS addnum FROM adv_platform_pagedata_info a LEFT JOIN adv_platform_app_info b ON a.appid = b.appid AND a.platform = b.platform and a.tappid = b.tappid and a.tdate <= b.bindEndTime WHERE a.tdate = '"+hyphenDate+"' AND b.channel = '"+channel+"' GROUP BY a.tdate,a.appid,b.channel";
		}
		List<Map<String, Object>> appMap = adMapper.queryListMap(sql);

		//获取产品名称
		String appQuery = "select concat(umeng_key,'') as mapkey,app_name,id as appid from yyhz_0308.app_info ";
		//特殊处理 渠道为h5_vivo时 新增活跃从vivo小游戏表中获取
		if ("h5_vivo".equals(channel)){
			appQuery = "select convert(id, char) as mapkey,app_name,id as appid from yyhz_0308.app_info where app_category = 46";
		}
		if ("h5_oppo".equals(channel) || "uc".equals(channel)){
			appQuery = "select convert(id, char) as mapkey,app_name,id as appid from yyhz_0308.app_info where app_category = 51";
		}
		//华为小游戏
		if ("h5_huawei".equals(channel)) {
			appQuery = "select convert(id, char) as mapkey,app_name,id as appid from yyhz_0308.app_info where app_category = 45";
		}
		Map<String, Map<String, Object>> appNameMap =  adMapper.queryListMapOfKey(appQuery);
		// 特殊处理，渠道为google_huawei时 新增活跃 从 新增活跃汇总查询-大数据 获取 -20250325
		if (isHaiwai){
			appNameMap.clear();
			String appQuery1 = "select convert(umeng_key, char) as mapkey,app_name,id as appid from yyhz_0308.app_info where app_category in (15,16,20,52)";
			Map<String, Map<String, Object>> collectMap1 = adMapper.queryListMapOfKey(appQuery1);
			String appQuery2 = "select convert(id, char) as mapkey,app_name,id as appid from yyhz_0308.app_info where app_category in (15,16,20,52)";
			Map<String, Map<String, Object>> collectMap2 = adMapper.queryListMapOfKey(appQuery2);
			appNameMap.putAll(collectMap1);
			appNameMap.putAll(collectMap2);
		}

		// 获取展示，点击 覆盖用户数
		List<Map<String, Object>> cilckActUserList = dnwxBiAdtMapper.selectClickActuser(hyphenDate);
		Map<String, Map<String, Object>> cilckActUserMap = cilckActUserList.stream().collect(Collectors.toMap(data -> ""+data.get("appid") + data.get("download_channel"), Function.identity(),(k1,k2)->k1));

		List<Map<String, Object>> showActUserList = dnwxBiAdtMapper.selectAdshowActuser(hyphenDate);
		Map<String, List<Map<String, Object>>> showActUserMap = showActUserList.stream().collect(Collectors.groupingBy(data -> ""+data.get("appid") + data.get("download_channel")));

		for (Entry<String, UmengAdIncomeVo> next : valMap.entrySet()) {
			UmengAdIncomeVo value = next.getValue();
			// 展示收入内容的友盟key与数据库友盟key匹配，获取活跃用户数
			if (isHaiwai){
				value.setAppid(appNameMap.get(value.getAppid()) != null ? appNameMap.get(value.getAppid()).get("appid").toString() : "");
				value.setAppname(appNameMap.get(value.getAppid()) != null ? appNameMap.get(value.getAppid()).get("app_name").toString() : value.getAppkey());
				value.setActnum(1);
				value.setAddnum(1);
			}else {
				for (Map<String, Object> map : appMap) {
					if (value.getAppkey().equals(map.get("appkey").toString())) {
						//2022-06-09替换成新的数据源表 产品名称 app_info 新增活跃 umeng_user_channel_total
						value.setAppid(appNameMap.get(map.get("appkey").toString()).get("appid") != null ? appNameMap.get(map.get("appkey").toString()).get("appid").toString() : "");
						value.setAppname(appNameMap.get(value.getAppkey()) != null ? appNameMap.get(value.getAppkey()).get("app_name").toString() : value.getAppkey());
						value.setActnum(Integer.parseInt(map.get("actnum") == null ? "0" : map.get("actnum").toString()));
						value.setAddnum(Integer.parseInt(map.get("addnum") == null ? "0" : map.get("addnum").toString()));
					}
				}
				//如果是 uc 需要特殊处理，需要赋值产品和名称，新增活跃是没有值的
				if ("uc".equals(channel) || "h5_huawei".equals(channel)) {
					value.setAppid(value.getAppkey());
					value.setAppname(appNameMap.get(value.getAppkey()) != null ? appNameMap.get(value.getAppkey()).get("app_name").toString() : value.getAppkey());
				}
			}

			// 获得活跃数后计算其它数据
			BigDecimal actnum = new BigDecimal(value.getActnum());
			value.setTotal_income(
				new BigDecimal(value.getBanner_income())
				.add(new BigDecimal(value.getPlaque_income()))
				.add(new BigDecimal(value.getSplash_income()))
				.add(new BigDecimal(value.getVideo_income()))
				.add(new BigDecimal(value.getNative_banner_income()))
				.add(new BigDecimal(value.getNative_plaque_income()))
				.add(new BigDecimal(value.getNative_splash_income()))
				.add(new BigDecimal(value.getPlaque_video_income()))
				.add(new BigDecimal(value.getNative_msg_income()))
				//2021-08-26 添加
				.add(new BigDecimal(value.getSystem_splash_income()))
				.add(new BigDecimal(value.getNative_new_plaque_income()))
				.add(new BigDecimal(value.getNative_new_banner_income()))
				.add(new BigDecimal(value.getSuspend_icon_income()))
				//海外数据总收入需要保留三位小数
				.setScale(isHaiwai ? 3 : 2, RoundingMode.HALF_UP).toString()
			);
			if(value.getActnum() != 0){
				value.setDau_arpu(
					new BigDecimal(value.getTotal_income())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);

				//新增占比
				value.setAvgnum(new BigDecimal(value.getAddnum()).multiply(new BigDecimal(100))
						.divide(actnum, 2, RoundingMode.HALF_UP)
						+"%");

				// 各类型 人均pv
				value.setBanner_pv(
					new BigDecimal(value.getBanner_show())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				value.setPlaque_pv(
					new BigDecimal(value.getPlaque_show())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				value.setSplash_pv(
					new BigDecimal(value.getSplash_show())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				value.setVideo_pv(
					new BigDecimal(value.getVideo_show())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				value.setMsg_pv(
					new BigDecimal(value.getNative_msg_show())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				value.setPlaque_video_pv(
					new BigDecimal(value.getPlaque_video_show())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);

				//2021-08-26 添加
				value.setSystem_splash_pv(
						new BigDecimal(value.getSystem_splash_show())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				//人均pv-原生新样式插屏 = (原生新样式插屏)/活跃 2021-09-08
				value.setNative_new_plaque_pv(
						new BigDecimal(value.getNative_new_plaque_show())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				//人均pv-原生新样式banner = （原生新样式banner）/活跃 2021-09-08
				value.setNative_new_banner_pv(
						new BigDecimal(value.getNative_new_banner_show())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				value.setSuspend_icon_pv(
						new BigDecimal(value.getSuspend_icon_show())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				//原生插屏人均pv
				value.setNative_plaque_pv(
						new BigDecimal(value.getNative_plaque_show())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				//原生banner人均pv
				value.setNative_banner_pv(
						new BigDecimal(value.getNative_banner_show())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				// 每用户平均收入 arpu
				//所有banner收入（banner入+原生banner收入+原生新样式banner收入）/活跃 banner人均arpu 2021-09-02修改
				value.setBanner_arpu(
					new BigDecimal(value.getBanner_income())
					.add(new BigDecimal(value.getNative_banner_income()))
					.add(new BigDecimal(value.getNative_new_banner_income()))
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				//所有插屏收入（插屏+原生插屏+原生新样式插屏）/活跃  插屏人均arpu 2021-09-02修改
				value.setPlaque_arpu(
					new BigDecimal(value.getPlaque_income())
					.add(new BigDecimal(value.getNative_plaque_income()))
					.add(new BigDecimal(value.getNative_new_plaque_income()))
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);

				//所有开屏收入(开屏收入+原生开屏收入+系统开屏收入)/活跃 开屏人均arpu 2021-09-02修改
				value.setSplash_arpu(
					new BigDecimal(value.getSplash_income())
					.add(new BigDecimal(value.getNative_splash_income()))
					.add(new BigDecimal(value.getSystem_splash_income()))
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				value.setVideo_arpu(
					new BigDecimal(value.getVideo_income())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				value.setMsg_arpu(
					new BigDecimal(value.getNative_msg_income())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);
				value.setPlaque_video_arpu(
					new BigDecimal(value.getPlaque_video_income())
					.divide(actnum, 2, RoundingMode.HALF_UP)
					.toString()
				);

				//2021-08-26 添加
				value.setSystem_splash_arpu(
						new BigDecimal(value.getSystem_splash_income())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				value.setNative_new_plaque_arpu(
						new BigDecimal(value.getNative_new_plaque_income())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				value.setNative_new_banner_arpu(
						new BigDecimal(value.getNative_new_banner_income())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
				value.setSuspend_icon_arpu(
						new BigDecimal(value.getSuspend_icon_income())
								.divide(actnum, 2, RoundingMode.HALF_UP)
								.toString()
				);
			}
			//渗透率相关字段
			// 获取对应的展示，点击的覆盖用户数
			calculateCilckActUserNum(value,cilckActUserMap);
			calculateShowActUserNum(value,showActUserMap);
			dataList.add(value);
		}
		logger.debug("dataList:{},result:{}",dataList.size(), JSON.toJSONString(dataList));


		if(dataList != null && dataList.size() > 0){
			try {
				String advPlatformSql = "SELECT concat(appid, '_', channel) mapkey, tappname FROM `adv_platform_app_info`\n" +
						"where channel = '"+channel+"' and bindEndTime >= '"+day+"'\n" +
						"ORDER BY bindEndTime desc";
				Map<String, Map<String, Object>> advPlatformMap = adMapper.queryListMapOfKey(advPlatformSql);
				dataList.forEach(data -> {
							String cha = data.getChannel();
							data.setMedia(UMENG_CHANNEL_MEDIA_MAP.get(cha));
							Map<String, Object> map = advPlatformMap.get(data.getAppid() + "_" + cha);
							if (map != null) {
								data.setGameName(map.get("tappname") == null ? null : map.get("tappname").toString());
							} else {
								data.setGameName(null);
							}
						});

				// 2024-05-08 sdk功能标识查询改为，状态 已上线，且日期不超过查询日期，的最大prjId
				String tempModuleSql = "SELECT singleid mapkey, tempName,largeVer FROM dnwx_client.`wbgui_tempmodule`";
				String formconfigSql = "SELECT  left(pjId, 5) appid, pjId, channelTag,  tmp_mod_id, state, OnlineDate  FROM dnwx_client.`wbgui_formconfig`\n" +
                        "where state = '已上线' \n" +
                        "and channelTag = '"+channel+"'\n" +
                        "and STR_TO_DATE(OnlineDate, '%Y/%m/%d %H:%i:%S') < STR_TO_DATE('"+day+" 23:59:59', '%Y%m%d %H:%i:%S')\n" +
                        "and (tmp_mod_id is not null and tmp_mod_id != '')";
				List<Map<String, String>> maps = adMapper.queryListMapOne(formconfigSql);
				Map<String, Map<String, Object>> tempModule = adMapper.queryListMapOfKey(tempModuleSql);
				ArrayList<Map<String, String>> flatMap = new ArrayList<>();
				maps.stream()
                        .filter(map -> BlankUtils.isNotBlank(map.get("tmp_mod_id")))
                        .forEach(map -> {
							String tmpModId = map.get("tmp_mod_id");
							if (tmpModId.contains(";")) {
								String[] split = tmpModId.split(";");
                                StringJoiner sj = new StringJoiner(" | ");
                                StringJoiner largeVer = new StringJoiner(" | ");
                                for (String s : split) {
                                    Map<String, Object> map1 = tempModule.get(s);
                                    if (!CollectionUtils.isEmpty(map1)) {
                                        sj.add(BlankUtils.getString(map1.get("tempName")));
										largeVer.add(BlankUtils.getString(map1.get("largeVer")));
                                    }
                                }
                                map.put("temp_name", sj.toString());
                                map.put("tmp_mod_id", tmpModId.replace(";", " | "));
								map.put("large_ver",largeVer.toString());
                            } else {
                                Map<String, Object> map1 = tempModule.get(tmpModId);
                                if (!CollectionUtils.isEmpty(map1)) {
                                    map.put("temp_name", BlankUtils.getString(map1.get("tempName")));
									map.put("large_ver", BlankUtils.getString(map1.get("largeVer")));
                                }
							}
						});
				Map<String, Map<String, String>> sdkMap = maps.stream().collect(Collectors.toMap(
						map -> map.get("appid") + "_" + map.get("channelTag"), map -> map,
						(x, y) -> {
							if (x.get("pjId").compareTo(y.get("pjId")) > 0) {
								return x;
							} else {
								return y;
							}
						}
				));
				//获取小米违规记录数据
				List<XiaomiViolationRecordVo> recordVoList = violationRecordMapper.queryAdsenseTypes(hyphenDate);
				Map<String,String> recordVoMap = new HashMap<>();
				Map<String, List<XiaomiViolationRecordVo>> collect = recordVoList.stream().collect(Collectors.groupingBy(data -> data.getAppid() + "_" + data.getChannel()));
				for (Entry<String, List<XiaomiViolationRecordVo>> entry : collect.entrySet()) {
					String adsenseType = entry.getValue().stream().map(XiaomiViolationRecordVo::getAdsense_type).distinct().collect(Collectors.joining("|"));
					recordVoMap.put(entry.getKey(),adsenseType);
				}
				// 获取 产品中活跃最大的项目id所带的临时模块
				Map<String, Map<String, String>> maxNumTempId = getMaxNumProjectTempId(hyphenDate,channel);
				//获取最大覆盖量的模块版本
				Map<String, String> maxNumLagerVer = getMaxNumLagerVer(hyphenDate,channel);
				for (UmengAdIncomeVo umengAdIncomeVo : dataList) {
						//匹配功能标识
					String key = umengAdIncomeVo.getAppid() +"_"+ umengAdIncomeVo.getChannel();
					if (sdkMap.containsKey(key)) {
						Map<String, String> map = sdkMap.get(key);
                        umengAdIncomeVo.setTemp_id(map.get("tmp_mod_id"));
                        umengAdIncomeVo.setTemp_name(map.get("temp_name"));
						umengAdIncomeVo.setLarge_ver(map.get("large_ver"));
                    } else {
						//当不存在功能标识时，获取最大的覆盖量的的大版本
						umengAdIncomeVo.setLarge_ver(maxNumLagerVer.get(key));
					}
					//封装 产品中活跃最大的项目id所带的临时模块
					if (maxNumTempId.containsKey(key)) {
						Map<String, String> tempMap = maxNumTempId.get(key);
						umengAdIncomeVo.setActive_temp_id(tempMap.get("temp_id"));
						umengAdIncomeVo.setActive_temp_name(tempMap.get("temp_name"));
					}
					//封装广告位类型违规
					if (recordVoMap.containsKey(key)) {
						umengAdIncomeVo.setAd_violation_type(recordVoMap.get(key));
					}
                }

				try {
					// 增加adb表的数据入库
					umengMonitorMapper.insertUmengAdIncomeList(dataList);
				}catch (Exception e){
					logger.info("新增adb报错：", e);
				}
				if (isHaiwai) {
					try {
						// 增加海外adb表的数据入库
						umengMonitorMapper.insertUmengAdIncomeOverseaList(dataList);
					}catch (Exception e){
						logger.info("新增adb海外数据报错：", e);
					}
					try {
						// 增加海外adb表的数据入库
						haiwai2DnwxBiUsMapper.insertUmengAdIncomeList(dataList);
					}catch (Exception e){
						logger.info("新增海外adb报错：", e);
					}
				}
				int i = adMapper.insertUmengAdIncomeList(dataList);
				return "{\"ret\":1, \"msg\":\"同步成功！\"}";
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return "{\"ret\":0, \"msg\":\"同步失败，获取数据有误，请稍后重试！\"}";
	}

	private Map<String, String> getMaxNumLagerVer(String tdate, String channel) {
		Map<String, String> maxNumLargeVerMap = new HashMap<>();
		String templateSql = "SELECT * FROM dn_extend_sdk_relation WHERE tdate = '%s' AND cha_id = '%s' AND sdk_name = '大版本的版本号'";
		List<Map<String, Object>> mapList = dnwxBiMapper.queryListMap(String.format(templateSql, tdate, channel));
		if (CollectionUtils.isEmpty(mapList)) {
			return maxNumLargeVerMap;
		}
		//根据 appid，channel进行分组操作
		Map<String, List<Map<String, Object>>> collect = mapList.stream().collect(Collectors.groupingBy(data -> data.get("appid") + "_" + data.get("cha_id")));

		for (Entry<String, List<Map<String, Object>>> entry : collect.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> values = entry.getValue();
			//获取最大的 覆盖量num
			Integer maxNum = values.stream().map(data -> ObjectUtils.isEmpty(data.get("num")) ? 0 : (Integer) data.get("num")).max(Integer::compareTo).orElse(0);
			//获取模块版本
			LinkedHashSet<String> largeVerSet = new LinkedHashSet<>();
			for(Map<String, Object> value : values) {
				Object numObj = value.get("num");
				if (ObjectUtils.isEmpty(numObj)) {
					continue;
				}
				//对比最大覆盖量，获取覆盖量最大的local_ver
				String localVer = value.getOrDefault("local_ver",Strings.EMPTY).toString();
				if (maxNum.equals(numObj) && !StringUtils.isEmpty(localVer)) {
					largeVerSet.add(localVer);
				}
			}
			//存储 功能标识
			if (!CollectionUtils.isEmpty(largeVerSet)) {
				maxNumLargeVerMap.put(key,String.join("|", largeVerSet));
			}
		}
		return maxNumLargeVerMap;
	}

	/**
	 * 根据时间，渠道获取最大覆盖量项目的temp_id,temp_name，存在多个使用 ”|“ 隔开
	 * @param tdate 时间，格式：2024-10-10
	 * @param channel 渠道
	 * @return 查询处理结果
	 */
	public Map<String, Map<String, String>> getMaxNumProjectTempId(String tdate, String channel) {
		Map<String, Map<String, String>> maxNumTempIdMap = new HashMap<>();
		String templateSql = "SELECT extend.*,project.temp_id,tempmodule.tempName FROM dn_extend_sdk_relation extend LEFT JOIN wbgui_project_sdk_relation project ON extend.prjid = project.project_id AND extend.sdk_name = project.sdk_name AND extend.ver = project.sdk_version AND extend.local_ver = project.version LEFT JOIN wbgui_tempmodule tempmodule ON project.temp_id = tempmodule.singleid WHERE extend.tdate = '%s' AND extend.cha_id = '%s'";
		List<Map<String, Object>> mapList = dnwxBiMapper.queryListMap(String.format(templateSql, tdate, channel));
		if (CollectionUtils.isEmpty(mapList)) return maxNumTempIdMap;
		//根据 appid，channel进行分组操作
		Map<String, List<Map<String, Object>>> collect = mapList.stream().collect(Collectors.groupingBy(data -> data.get("appid") + "_" + data.get("cha_id")));

		for (Entry<String, List<Map<String, Object>>> entry : collect.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> values = entry.getValue();
			//获取最大的 覆盖量num
			Integer maxNum = values.stream().map(data -> ObjectUtils.isEmpty(data.get("num")) ? 0 : (Integer) data.get("num")).max(Integer::compareTo).orElse(0);
			//获取最大值的temp_id;
			LinkedHashSet<String> tempIdSet = new LinkedHashSet<>();
			LinkedHashSet<String> tempNameSet = new LinkedHashSet<>();
			for(Map<String, Object> value : values) {
				Object numObj = value.get("num");
				if (ObjectUtils.isEmpty(numObj)) continue;
				//对比最大覆盖量，获取覆盖量最大的temp_id
				String tempId = value.getOrDefault("temp_id",Strings.EMPTY).toString();
				String tempName = value.getOrDefault("tempName",Strings.EMPTY).toString();
				if (!StringUtils.isEmpty(tempId) && maxNum.equals(numObj)) {
					tempIdSet.add(tempId);
					tempNameSet.add(tempName);
				}
			}
			//存储 功能标识
			if (!CollectionUtils.isEmpty(tempIdSet)) {
				Map<String, String> map = new HashMap<>();
				map.put("temp_id",String.join("|", tempIdSet));
				map.put("temp_name",String.join("|", tempNameSet));
				maxNumTempIdMap.put(key,map);
			}
		}
		return maxNumTempIdMap;
	}





	private JSONArray getApiDataNewFromCash(String day, String channel) {
//		String date = DateUtil.typeT2TypeA(day);
		String sql = "select placement_id, pv, click, revenue,dnappid,app_name,IFNULL(request_count,0) request_count,IFNULL(return_count,0) fill_count from dn_cha_cash_total t1" +
				" left join app_info t2 on t1.dnappid = t2.id" +
				" where date = '"+day+"' and cha_id = '"+channel+"'";
		// 变现明细表数据
		List<Map<String, String>> cashMap = adMapper.queryListMapOne(sql);
		if (CollectionUtils.isEmpty(cashMap)) {
			return null;
		}

		JSONArray array = new JSONArray();
		for (Map<String, String> each : cashMap) {
			JSONObject sink = new JSONObject();
			sink.put("posId", each.get("placement_id"));
			sink.put("view", each.get("pv"));
			sink.put("income", each.get("revenue"));
			sink.put("click", each.get("click"));
			sink.put("appid", each.get("dnappid"));
			sink.put("app_name", each.get("app_name"));
			sink.put("request_count",each.get("request_count"));
			sink.put("fill_count",each.get("fill_count"));
			array.add(sink);
		}
		return array;
	}


	public JSONArray getVivoApiData(String day){

		JSONArray array = new JSONArray();

		String sql = "select tttoken from app_channel_config where channel = 'vivo' ORDER BY createtime asc";
		List<String> list = adMapper.queryListString(sql);

		String query = "select ad_code from umeng_adcode_list where `status`= 'open' and channel like '%vivo%'";
		List<String> codeList = adMapper.queryListString(query);

		for (String cook : list) {
			Map<String, String> headMap = new HashMap<>();
			headMap.put(":authority","dev.vivo.com.cn");
			headMap.put(":method","GET");
			headMap.put(":scheme","https");
			headMap.put("accept","application/json, text/plain, */*");
			headMap.put("accept-encoding","gzip, deflate, br");
			headMap.put("accept-language","zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,und;q=0.6");
			headMap.put("cache-control","no-cache");
			headMap.put("cookie",cook);
			headMap.put("csrftoken","2Lgtm7");
			headMap.put("pragma","no-cache");
			headMap.put("user-agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.86 Safari/537.36");
			try {
				String[] types = {"2","3","4","5","9"};
				for (String p : types) {
					String li = "https://adnet.vivo.com.cn/api/report/getReportTableData?order=&orderBy="+
						"&startDate="+day+"&endDate="+day+"&dimensions=positionId&platformType="+
						"&positionType="+p+"&metrics=view&searchKeyWord=&pageIndex=1&pageSize=200&timestamp="+DateTime.now().getMillis();

					String result = HttpClientUtils.getInstance().httpGet(li, headMap);
					JSONObject reObject = JSONObject.parseObject(result);
					if(reObject != null && 1 == reObject.getIntValue("code")){
						JSONArray jsonArray = reObject.getJSONObject("data").getJSONArray("dataList");
						for (int k = 0; k < jsonArray.size(); k++) {
							JSONObject data = jsonArray.getJSONObject(k);
							if(!data.getString("positionName").contains("小游戏") && codeList.contains(data.getString("positionUuid"))){
								JSONObject sink = new JSONObject();
								sink.put("posId", data.getString("positionUuid"));
								sink.put("view", data.getString("view"));
								sink.put("income", data.getString("income"));
								sink.put("click", data.getString("click"));
								array.add(sink);
							}
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			}
		}
		return array;
	}

	/**
	 * 辅助方法：安全地将两个字符串表示的数字相加
	 * @param num1 第一个数字字符串
	 * @param num2 第二个数字字符串
	 * @return 相加结果的字符串表示
	 */
	public String addBigDecimal(String num1, String num2) {
		BigDecimal bd1 = new BigDecimal(num1 == null ? "0" : num1);
		BigDecimal bd2 = new BigDecimal(num2 == null ? "0" : num2);
		return bd1.add(bd2).toString();
	}


    /**
     * vivo api接口拉取广告位数据
     * @param day
     * @return
     */
    public JSONArray getVivoApiDataNew(String day,String channel) {
        JSONArray array = new JSONArray();
        day = day.replace("-", "");
        String query = "select ad_code from umeng_adcode_list where `status`= 'open' and channel = '"+channel+"' ";
        List<String> codeList = adMapper.queryListString(query);

        Map<String, String> map = new HashMap<String, String>();

		String sql = "select accountName,secretKey from  dn_vivo_account where enabled =1 ";
		List<Map<String, Object>> list = adMapper.queryListMap(sql);
		for (Map<String, Object> eachMap : list) {
			String accountName = (String) eachMap.get("accountName");
			String secretKey = (String) eachMap.get("secretKey");
			map.put(accountName,secretKey);
		}

        Map<String, String> headMap = new HashMap<>();

        try {
            for (Map.Entry<String, String> account : map.entrySet()) {
                String url = "https://adnet.vivo.com.cn/api/open/report?dimension=position";
                String token = generateVivoToken(account.getKey(), account.getValue());
                headMap.put("token", token);
                url += "&accountName=" + account.getKey() + "&startDate=" + day + "&endDate=" + day;
                String result = HttpRequest.get(url, headMap);
                if (!BlankUtils.checkBlank(result)) {
                    JSONObject ret = JSONObject.parseObject(result);
                    if (ret != null && "0".equals(ret.getString("code"))) {
                        JSONArray data = ret.getJSONArray("data");
                        if (data != null && data.size() > 0) {
                            for (int k = 0; k < data.size(); k++) {
                                JSONObject each = data.getJSONObject(k);
                                if (!each.getString("positionName").contains("小游戏") && codeList.contains(each.getString("positionId"))) {
                                    JSONObject sink = new JSONObject();
                                    sink.put("posId", each.getString("positionId"));
                                    sink.put("view", each.getString("view"));
                                    sink.put("income", each.getString("income"));
                                    sink.put("click", each.getString("click"));
                                    array.add(sink);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("getVivoApiDataNew error:", e);
        }
        return array;
    }

    private static String generateVivoToken(String account, String secretKey) {
        String token = "";
        long time = System.currentTimeMillis();
        String sign = DigestUtils.sha256Hex(account + secretKey + time);
        token = Base64.encodeBase64String((time + "," + sign).getBytes());
        return token;
    }



	public static String getOppoApiData(String day){
		try {
			String userid = "*********";
			String api_key = "OTEyMzEyY2EtZmU2ZC00OWIwLTk1OTktNjJkMmIwZjQxMzlm";
			String timestamp = (DateTime.now().getMillis()/1000)+"";
			String sign = DigestUtils.sha1Hex(userid+api_key+timestamp);
			String token = Base64.encodeBase64String((userid+","+timestamp+","+sign).getBytes());

			Map<String, String> headMap = new HashMap<>();
			headMap.put("content-type","application/x-www-form-urlencoded");
			String param = "userId="+userid+"&token="+token+"&timestamp="+timestamp+"&startTime="+day+"&endTime="+day+"";

			String url = "https://uapi.ads.heytapmobi.com/union/api/report/posQuery";
			String httpGet = HttpClientUtils.getInstance().httpGet(url+"?"+param, headMap);
			return httpGet;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static String getOppoApiData(String userid,String api_key,String day){
		try {
			String timestamp = (DateTime.now().getMillis()/1000)+"";
			String sign = DigestUtils.sha1Hex(userid+api_key+timestamp);
			String token = Base64.encodeBase64String((userid+","+timestamp+","+sign).getBytes());

			Map<String, String> headMap = new HashMap<>();
			headMap.put("content-type","application/x-www-form-urlencoded");
			String param = "userId="+userid+"&token="+token+"&timestamp="+timestamp+"&startTime="+ day +"&endTime="+ day +"";

			String url = "https://uapi.ads.heytapmobi.com/union/api/report/posQuery";
			String httpGet = HttpClientUtils.getInstance().httpGet(url+"?"+param, headMap);
			return httpGet;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}


	public JSONArray getCommonDataFromCashTotal(String day,String agent,String channel, Set<String> codeSet){
		JSONArray array = new JSONArray();
//		day = DateTime.parse(day).toString("yyyyMMdd");
		day = DateUtils.changeDateStringToHyphen(day);

		String cashSql = "SELECT placement_id posId, pv view, revenue income, click,IFNULL(request_count,0) request_count,IFNULL(return_count,0) fill_count FROM `dn_cha_cash_total`\n" +
				"where agent = '"+agent+"' and date = '"+day+"' and cha_id = '" + channel +"'";
		List<Map<String, Object>> cashData = adMapper.queryListMap(cashSql);
		if (BlankUtils.isEmptyCollection(cashData)) {
			logger.info("同步变现明细表没有数据, date: {}, channel: {}", day, channel);
			return array;
		}
		logger.debug("同步变现明细表获取到数据: {}, date: {}, channel: {}", cashData.size(), day, channel);
		cashData.stream().filter(map -> codeSet.contains(BlankUtils.getString(map.get("posId"))))
				.forEach(array::add);
		return array;
	}

	public JSONArray getCommonDataFromCashTotalOfCountry(String day,String channel, Set<String> codeSet){
		JSONArray array = new JSONArray();
		day = DateUtils.changeDateStringToHyphen(day);

		String cashSql = "SELECT country,placement_id posId, pv view, dollar_revenue income, click,IFNULL(request_count,0) request_count,IFNULL(return_count,0) fill_count FROM dnwx_bi.dn_cha_cash_total " +
				"where date = '"+day+"' and cha_id = '" + channel +"'";
		List<Map<String, Object>> cashData = haiwaiCfgMapper.queryListMap(cashSql);
		if (BlankUtils.isEmptyCollection(cashData)) {
			logger.info("同步变现明细表没有数据, date: {}, channel: {}", day, channel);
			return array;
		}
		logger.debug("同步变现明细表获取到数据: {}, date: {}, channel: {}", cashData.size(), day, channel);
		cashData.stream()
				.filter(map -> codeSet.contains(BlankUtils.getString(map.get("posId"))))
				.forEach(array::add);
		return array;
	}



	/**
	 * 小米api 由于应用过多，请求过慢，先写好代码
	 * @param day
	 * @param channel
	 * @return
	 */
	public JSONArray getXiaomiApiDataNew(String day,String channel){
		JSONArray array = new JSONArray();
		day = DateTime.parse(day).toString("yyyyMMdd");
		String adcode = "SELECT ad_code FROM `umeng_adcode_list` where channel = '"+channel+"' and `status`= 'open'";
		List<String> adcodeList = adMapper.queryListString(adcode);
		//获取所有应用的配置
		String sql = "select appid,appSecret as secret,a.account as devid from dnwx_cfg.dn_xiaomi_app a inner join dnwx_cfg.dn_xiaomi_account b on a.account=b.account WHERE a.`status`=1";
		List<Map<String, Object>> list = adMapper.queryListMap(sql);


		String cashSql = "SELECT placement_id posId, pv view, revenue income, click FROM `dn_cha_cash_total`\n" +
				"where agent = 'Mi' and date = '"+day+"' and cha_id = '" + channel +"'";
		List<Map<String, Object>> cashData = adMapper.queryListMap(cashSql);
		if (BlankUtils.isEmptyCollection(cashData)) {
			logger.info("小米同步变现明细表没有数据, date: {}, channel: {}", day, channel);
			return array;
		}
		logger.info("小米同步变现明细表获取到数据: {}, date: {}, channel: {}", cashData.size(), day, channel);
		HashSet<String> codeSet = new HashSet<>(adcodeList);
		cashData.stream().filter(map -> codeSet.contains(BlankUtils.getString(map.get("posId"))))
				.forEach(array::add);


//		//api地址
//		String url = "https://dev.mi.com/admob/cgi/api/report/get";
//		for (Map<String, Object> map : list) {
//			String devid = (String) map.get("devid");
//			String appid = (String) map.get("appid");
//			String appSecret = (String) map.get("secret");
//			String timestamp = System.currentTimeMillis()/1000+"";
//			String sign = DigestUtils.sha1Hex(devid+appid+appSecret+timestamp);
//			String token = Base64.encodeBase64String((devid+","+appid+","+timestamp+","+sign).getBytes());
//			//设置请求参数
//			Map<String, String> paramsMap = new HashMap<>();
//			paramsMap.put("devid", devid);
//			paramsMap.put("appid", appid);
//			paramsMap.put("start_date", day);
//			paramsMap.put("end_date", day);
//			paramsMap.put("token", token);
//			//请求
//			try {
//				String result = HttpClientUtils.getInstance().httpPostQuick(url, paramsMap);
////				logger.info("devid: {},appid: {},result: {}",devid,appid,result);
//				JSONObject reObject = JSONObject.parseObject(result);
//				if (reObject != null && 0 == reObject.getIntValue("errorCode")) {
//					JSONArray jsonArray = reObject.getJSONArray("details");
//					for (int k = 0; k < jsonArray.size(); k++) {
//						JSONObject data = jsonArray.getJSONObject(k);
//						JSONObject sink = new JSONObject();
//						if(!adcodeList.contains(data.getString("placementId"))){
//							continue;
//						}
//						sink.put("posId", data.getString("placementId"));
//						sink.put("view", data.getString("view"));
//						sink.put("income","0.00");
//						if (BlankUtils.isNumeric(data.getString("revenue"))){
//							sink.put("income",new BigDecimal(data.getString("revenue")));
//						}
//						sink.put("click", data.getString("click"));
//						array.add(sink);
//					}
//				}
//			} catch (Exception e) {
//				e.printStackTrace();
//				return array;
//			}
//			try {
//				Thread.sleep(500);
//			}catch (Exception e){
//
//			}
//
//		}
		return array;
	}

	/**
	 * xiaomi/xmyy 同属xiaomi平台api  爬虫
	 * @param day
	 * @param channel
	 * @return
	 */
	public JSONArray getXiaomiApiData(String day,String channel){
		day = DateTime.parse(day).getMillis()/1000+"";

		JSONArray array = new JSONArray();

		String sql = "select tttoken,ttparam from app_channel_config where channel = '"+channel+"' ORDER BY createtime asc";
		List<Map<String, Object>> list = adMapper.queryListMap(sql);

		String adcode = "SELECT ad_code FROM `umeng_adcode_list` where channel = '"+channel+"' and `status`= 'open'";
		List<String> adcodeList = adMapper.queryListString(adcode);

		for (Map<String, Object> map : list) {
			String cookie = (String) map.get("tttoken");
			String userId = (String) map.get("ttparam");

			Map<String, String> headMap = new HashMap<>();
			headMap.put(":authority", "dev.mi.com");
			headMap.put(":method", "GET");
			headMap.put(":scheme", "https");
			headMap.put("accept", "application/json, text/plain, */*");
			headMap.put("accept-encoding", "gzip, deflate, br");
			headMap.put("accept-language", "zh-CN,zh;q=0.9");
			headMap.put("cache-control", "no-cache");
			headMap.put("cookie",cookie);
			headMap.put("user-agent",
					"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.86 Safari/537.36");
			boolean flag = true;
			Integer pageNum = 1;
			Integer pageSize = 100;
			Integer totalCount = 0;
			try {
				while (flag){
					String url = "https://dev.mi.com/union/stat/data/detail?publisher_id=selectAll&placement_id=selectAll&style=selectAll&budget_type=effect&page_size="+pageSize+
							"&page_number="+pageNum+"&userId="+userId+"&time_start="+day+"&time_end="+day;
					String result = HttpClientUtils.getInstance().httpGet(url, headMap);
					if (BlankUtils.checkBlank(result)){
						flag =false;
					}
					JSONObject reObject = JSONObject.parseObject(result);
					if (reObject != null && 1 == reObject.getIntValue("status")) {
						totalCount = reObject.getJSONObject("result").getIntValue("total_count");
						JSONArray jsonArray = reObject.getJSONObject("result").getJSONArray("list_data");
						for (int k = 0; k < jsonArray.size(); k++) {
							JSONObject data = jsonArray.getJSONObject(k);
							JSONObject sink = new JSONObject();
							if(!adcodeList.contains(data.getString("placement_id"))){
								continue;
							}
							sink.put("posId", data.getString("placement_id"));
							sink.put("view", data.getString("adview"));
							sink.put("income","0.00");
							if(BlankUtils.isNumeric(data.getString("gain").toString())){
								sink.put("income", data.getString("gain"));
							}
							sink.put("click", data.getString("click"));

							array.add(sink);
						}
						if (pageNum*pageSize>totalCount){
							flag =false;
						}
						pageNum++;
					}else{
						flag =false;
					}
				}
			} catch (Exception e) {
				logger.error("getXiaomiApiData error:",e);
				//只要有异常全部失败,让操作人员重新拉取
				return new JSONArray();

			}

		}
		return array;
	}

	public JSONArray get4399ApiData(String day){
		JSONArray array = new JSONArray();

		String tokensql = "select tttoken from app_channel_config where channel = 'ad4399' ORDER BY createtime asc";
		List<String> tokenlist = adMapper.queryListString(tokensql);

		for (String cook : tokenlist) {
		Map<String, String> headMap = new HashMap<>();
        headMap.put("cookie", cook);

		String sql = "SELECT * FROM `umeng_adcode_list` where channel = 'ad4399' and `status`= 'open'   ";
		List<Map<String, Object>> list = adMapper.queryListMap(sql);

		int threadSize = 10; //线程个数
		int size = list.size();
		int a = size / threadSize;
		CountDownLatch doneSignal = new CountDownLatch(threadSize);

		for(int i= 0; i< threadSize ;i++){
			int start  = i * a;
			int end = (i+1) < threadSize ? (i+1) * a :  size;
			Thread t = new Thread(new Ad4399Run(doneSignal, list, start, end, array,headMap,day));
			t.start();
		}

		try {
			doneSignal.await();
		} catch (InterruptedException e1) {
			e1.printStackTrace();
		}

		}
		return array;
	}

	public JSONArray getHuaweiApiData(String day,String channel){
		JSONArray array = new JSONArray();
		Map<String, String> map = new HashMap<String, String>();

		String sql = "select client_id,client_secret from  dn_huawei_account where enabled=1 ";
		List<Map<String, Object>> list = tfxtMapper.queryListMap(sql);
		for (Map<String, Object> eachMap : list) {
			String client_id = (String) eachMap.get("client_id");
			String client_secret = (String) eachMap.get("client_secret");
			map.put(client_id,client_secret);
		}

		String query = "select ad_code from umeng_adcode_list where `status`= 'open' and  channel ='"+channel+"' ";
		List<String> codeList = adMapper.queryListString(query);

		try {
			for (String key : map.keySet()) {
		        String clientId = key;
		        String secret = map.get(key);
		        String token = HuaweiApiUtil.getOauthToken(clientId,secret);
		        String resp = HuaweiApiUtil.callApiUseToken(token,day,day);
		        JSONObject reObject = JSONObject.parseObject(resp);
		        if(reObject != null && 0 == reObject.getIntValue("code")){
		        	JSONArray jsonArray = reObject.getJSONObject("data").getJSONArray("list");
		        	for (int k = 0; k < jsonArray.size(); k++) {
						JSONObject data = jsonArray.getJSONObject(k);
						if(!BlankUtils.checkBlank(data.getString("placement_id")) && codeList.contains(data.getString("placement_id"))){
							JSONObject sink = new JSONObject();
							sink.put("posId", data.getString("placement_id"));
							sink.put("view", data.getString("show_count"));
							sink.put("income", data.getString("earnings"));
							sink.put("click", data.getString("click_count"));
							array.add(sink);
						}
					}
		        }
			}
		} catch (Exception e) {
			// TODO: handle exception
		}

		//google_huawei取Mobvista
		if ("google_huawei".equals(channel)){
			JSONArray mobvistaArray = getMobvistaApiData(day);
			if (mobvistaArray.size()>0){
				array.addAll(mobvistaArray);
			}
		}
		return array;
	}


	public JSONArray getOppoApiDataNew(String day,String channel){
		JSONArray dataArray = new JSONArray();
		Map<String, String> map = new HashMap<String, String>();

		String sql = "select account,apiKey from  dn_oppo_account where enabled =1 ";
		List<Map<String, Object>> list = tfxtMapper.queryListMap(sql);
		for (Map<String, Object> eachMap : list) {
			String account = (String) eachMap.get("account");
			String apiKey = (String) eachMap.get("apiKey");
			map.put(account,apiKey);
		}

		String adcode = "SELECT ad_code FROM `umeng_adcode_list` where channel = '"+channel+"' and `status`= 'open'";
		List<String> adcodeList = adMapper.queryListString(adcode);

		for(Map.Entry<String,String> accountMap:map.entrySet()){
			String userid = accountMap.getKey();
			String api_key = accountMap.getValue();
			try {
				String timestamp = (DateTime.now().getMillis()/1000)+"";
				String sign = DigestUtils.sha1Hex(userid+api_key+timestamp);
				String token = Base64.encodeBase64String((userid+","+timestamp+","+sign).getBytes());

				Map<String, String> headMap = new HashMap<>();
				headMap.put("content-type","application/x-www-form-urlencoded");
				String param = "userId="+userid+"&token="+token+"&timestamp="+timestamp+"&startTime="+day+"&endTime="+day+"";

				String url = "https://uapi.ads.heytapmobi.com/union/api/report/posQuery";
				String result = HttpClientUtils.getInstance().httpGet(url+"?"+param, headMap);


				if(result == null
						|| !"0".equals(JSONObject.parseObject(result).getString("code"))
						|| JSONObject.parseObject(result).getJSONArray("data").size() == 0){
					continue;
				}
				JSONArray temp = JSONObject.parseObject(result).getJSONArray("data");
				for (Object obj:temp){
					JSONObject json = (JSONObject) obj;
					if(adcodeList.contains(json.getString("posId"))){
						dataArray.add(json);
					}
				}

			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return dataArray;
	}




	public JSONArray getMobvistaApiData(String day){
		JSONArray dataArray = new JSONArray();
		String sql = "select account,skey,secret from dn_mobvista_account where enabled=1 ";
		List<Map<String, Object>> accountList = tfxtMapper.queryListMap(sql);

		String query = "select ad_code from umeng_adcode_list where `status`= 'open' and  channel ='google_huawei' ";
		List<String> codeList = adMapper.queryListString(query);

		Map<String,String> exchangeMap =commonMapper.selectExchnageRateConfig("USD,CNY",day);
		for (Map<String,Object> each:accountList){
			String skey = each.get("skey").toString();
			String secret = each.get("secret").toString();
			List<Map> eachMapList = getMobvistaReportingPage(day,skey,secret,1,null);

			List<ChannelAdDataVo> adDataList = new ArrayList<>();
			if (eachMapList.size()>0){
				for (Map map:eachMapList){
					if (map.get("unit_id")!=null&&codeList.contains(map.get("unit_id").toString())){
						ChannelAdDataVo adData = new ChannelAdDataVo();
						adData.setPosId(map.get("unit_id").toString());
						adData.setClick(map.get("click")!=null?map.get("click").toString():"0");
						adData.setView(map.get("impression")!=null?map.get("impression").toString():"0");
						String income = map.get("est_revenue")!=null?map.get("est_revenue").toString():"0";
						if (exchangeMap!=null){
							BigDecimal incomeData = new BigDecimal(income).multiply(new BigDecimal(exchangeMap.get("rate")));
							incomeData.setScale(5,RoundingMode.HALF_UP);
							income = incomeData.toString();
						}
						adData.setIncome(income);
						adDataList.add(adData);
					}
				}
			}

			if (adDataList.size()>0){
				Map<String,List<ChannelAdDataVo>> mapList = adDataList.stream().collect(Collectors.groupingBy(t->t.getPosId()));
				for (Map.Entry<String,List<ChannelAdDataVo>> eachData:mapList.entrySet()){
					if (codeList.contains(eachData.getKey())){
						JSONObject sink = new JSONObject();
						Integer click = 0;
						Integer view = 0;
						BigDecimal income = new BigDecimal("0");
						List<ChannelAdDataVo> list = eachData.getValue();
						for (ChannelAdDataVo vo:list){
							click = click + Integer.parseInt(vo.getClick());
							view = view + Integer.parseInt(vo.getView());
							income = income.add(new BigDecimal(vo.getIncome()));
						}
						sink.put("posId",eachData.getKey());
						sink.put("click",click.toString());
						sink.put("view",view.toString());
						sink.put("income",income.toString());
						dataArray.add(sink);
					}

				}
			}
		}
		return dataArray;
	}



	private List<Map> getMobvistaReportingPage(String day,String skey,String secret, int page, List<Map> list) {
		String reportUrl = "https://api.mintegral.com/reporting/data";
		if (CollectionUtils.isEmpty(list)) {
			list = new ArrayList<>();
		}
		String time = String.valueOf(System.currentTimeMillis() / 1000);
		String sign = getSign(skey, secret, day, time, page);
		String params = putPairsSequenceAndTogether(getConditionMap(skey, day, time, page), false);
		String url = reportUrl + "?sign=" + sign + "&" + params;
		String result = HttpRequest.get(url,new HashMap<>());
		if (!BlankUtils.checkBlank(result)){
			Map map = JsonUtils.json2map(result);
			Map dataMap = (Map) map.get("data");
			List<Map> dataList = (List<Map>) dataMap.get("lists");
			if (!CollectionUtils.isEmpty(dataList)) {
				list.addAll(dataList);
			}
			page = (int) dataMap.get("page");
			int total_page = (int) dataMap.get("total_page");
			if (page < total_page) {
				getMobvistaReportingPage(day,skey,secret, ++page, list);
			}
		}
		return list;
	}




	private String getSign(String skey, String secret, String day, String time, int page) {
		Map<String, String> map = getConditionMap(skey, day, time, page);
		String str = putPairsSequenceAndTogether(map, true);
		str = getMD5(str);
		String sign = getMD5(str + secret);
		return sign;
	}


	private Map getConditionMap(String skey, String day, String time, int page) {
		day = day.replace("-", "");
		Map<String, String> map = new HashMap<>();
		map.put("time", time);
		map.put("end", day);
		map.put("group_by", "date,app_id,unit_id,country");
		map.put("limit", "1000");
		map.put("page", String.valueOf(page == 0 ? 1 : page));
		map.put("skey", skey);
		map.put("start", day);

		return map;
	}

	private String getMD5(String msg) {
		MessageDigest md = null;
		try {
			md = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		md.reset();
		md.update(msg.getBytes());
		byte[] bytes = md.digest();

		String result = "";
		for (byte b : bytes) {
			// byte转换成16进制
			result += String.format("%02x", b);
		}

		return result;
	}

	private String putPairsSequenceAndTogether(Map<String, String> info, boolean flag) {
		List<Map.Entry<String, String>> infoIds = new ArrayList<Map.Entry<String, String>>(info.entrySet());
		Collections.sort(infoIds, new Comparator<Map.Entry<String, String>>() {
			@Override
			public int compare(Map.Entry<String, String> arg0, Map.Entry<String, String> arg1) {
				// TODO Auto-generated method stub
				return (arg0.getKey()).compareTo(arg1.getKey());
			}
		});
		String ret = "";

		for (Map.Entry<String, String> entry : infoIds) {
			ret += entry.getKey();
			ret += "=";
			if (flag) {
				try {
					ret += URLEncoder.encode(entry.getValue(), "UTF-8");
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			} else {
				ret += entry.getValue();
			}
			ret += "&";
		}
		ret = ret.substring(0, ret.length() - 1);
		return ret;
	}





	@Override
	@Async("aaaScheduler")
	public void syncOppoAdData(String day, String channel) {
		List<UmengAdIncomeVo> dataList = new ArrayList<UmengAdIncomeVo>();
		Map<String, UmengAdIncomeVo> valMap = new HashMap<String, UmengAdIncomeVo>();

		// 通过appkey来聚集多条广告类型内容到一个应用中
		String query = "select ad_code mapkey,appkey,adtype,status from umeng_adcode_list where channel = '" + channel + "' and `status`= 'open'";
		Map<String, Map<String, Object>> codeMap = adMapper.queryListMapOfKey(query);

		// 获取appid信息
		String appQuery = "select concat(umeng_key,'') as mapkey,app_name,id as appid from yyhz_0308.app_info ";
		Map<String, Map<String, Object>> appInfoMap =  adMapper.queryListMapOfKey(appQuery);

		//"oppo","oppoml","oppo2","oppomj","vivo","vivoml","vivo2","xiaomi","xmyy","xiaomimj","huawei","huaweimj"
		JSONArray array = new JSONArray();
		if ("oppo".equals(channel)||"oppoml".equals(channel)||"oppo2".equals(channel)||"oppomj".equals(channel)){
			//array = getOppoApiDataNew(day, channel);
			array = getCommonDataFromCashTotal(day,"oppo", channel, codeMap.keySet());
		}else if ("huawei".equals(channel)||"huaweimj".equals(channel)||"huawei2".equals(channel)){
			//String tdate = day.substring(0, 4)+"-"+day.substring(4, 6)+"-"+day.substring(6, 8);
			//array = getHuaweiApiData(tdate,channel);
			array = getCommonDataFromCashTotal(day,"Huawei", channel, codeMap.keySet());
		}else if ("vivo".equals(channel)||"vivoml".equals(channel)||"vivo2".equals(channel)||"vivoml2".equals(channel)){
			//array = getVivoApiDataNew(day,channel);
			array = getCommonDataFromCashTotal(day,"vivo", channel, codeMap.keySet());
		}else if ("xiaomi".equals(channel)||"xmyy".equals(channel)||"xiaomimj".equals(channel)||"xiaomiml".equals(channel)||"xiaomiwt".equals(channel)||"xiaomiml2".equals(channel)){
			//String tdate = day.substring(0, 4)+"-"+day.substring(4, 6)+"-"+day.substring(6, 8);
			//array = getXiaomiApiDataNew(tdate,channel);
			array = getCommonDataFromCashTotal(day,"Mi", channel, codeMap.keySet());
		}
		try {
			for (int i = 0; i < array.size(); i++) {
				JSONObject object = array.getJSONObject(i);

				String[] vals = {object.getString("posId"), object.getString("view"), object.getString("income"), object.getString("click"), object.getString("request_count"), object.getString("fill_count")};


				// 得到广告位ID的map对象
				Map<String, Object> code = codeMap.get(vals[0]);

				String appkey = code.get("appkey").toString();
				String adtype = code.get("adtype").toString();
				UmengAdIncomeVo ua = valMap.get(appkey);
				if (ua == null || ua.getAppkey() == null) {
					// 初始化应用数据
					ua = new UmengAdIncomeVo();
					ua.setTdate(day);
					ua.setChannel(channel);
					ua.setAppkey(appkey);
					ua.setAdcode(vals[0]);

					ua.setBanner_show("0");
					ua.setPlaque_show("0");
					ua.setSplash_show("0");
					ua.setVideo_show("0");
					ua.setNative_banner_show("0");
					ua.setNative_plaque_show("0");
					ua.setNative_splash_show("0");
					ua.setPlaque_video_show("0");
					ua.setNative_msg_show("0");

					//2021-08-26 添加
					ua.setSystem_splash_show("0");
					ua.setNative_new_banner_show("0");
					ua.setNative_new_plaque_show("0");
					ua.setSuspend_icon_show("0");

					ua.setBanner_income("0");
					ua.setPlaque_income("0");
					ua.setSplash_income("0");
					ua.setVideo_income("0");
					ua.setNative_banner_income("0");
					ua.setNative_plaque_income("0");
					ua.setNative_splash_income("0");
					ua.setPlaque_video_income("0");
					ua.setNative_msg_income("0");

					//2021-08-26 添加
					ua.setSystem_splash_income("0");
					ua.setNative_new_banner_income("0");
					ua.setNative_new_plaque_income("0");
					ua.setSuspend_icon_income("0");

					ua.setBanner_ecpm("0");
					ua.setPlaque_ecpm("0");
					ua.setSplash_ecpm("0");
					ua.setVideo_ecpm("0");
					ua.setNative_banner_ecpm("0");
					ua.setNative_plaque_ecpm("0");
					ua.setNative_splash_ecpm("0");
					ua.setPlaque_video_ecpm("0");
					ua.setNative_msg_ecpm("0");
					//2021-08-26 添加
					ua.setSystem_splash_ecpm("0");
					ua.setNative_new_banner_ecpm("0");
					ua.setNative_new_plaque_ecpm("0");
					ua.setSuspend_icon_ecpm("0");


					ua.setBanner_click("0");
					ua.setPlaque_click("0");
					ua.setSplash_click("0");
					ua.setVideo_click("0");
					ua.setNative_banner_click("0");
					ua.setNative_plaque_click("0");
					ua.setNative_splash_click("0");
					ua.setPlaque_video_click("0");
					ua.setNative_msg_click("0");
					//2021-08-26 添加
					ua.setSystem_splash_click("0");
					ua.setNative_new_banner_click("0");
					ua.setNative_new_plaque_click("0");
					ua.setSuspend_icon_click("0");

					//2024-10-08 添加 请求量和填充量
					ua.setBanner_request("0");
					ua.setPlaque_request("0");
					ua.setSplash_request("0");
					ua.setVideo_request("0");
					ua.setNative_banner_request("0");
					ua.setNative_plaque_request("0");
					ua.setNative_splash_request("0");
					ua.setPlaque_video_request("0");
					ua.setNative_msg_request("0");
					ua.setSystem_splash_request("0");
					ua.setNative_new_plaque_request("0");
					ua.setNative_new_banner_request("0");
					ua.setSuspend_icon_request("0");

					ua.setBanner_fill("0");
					ua.setPlaque_fill("0");
					ua.setSplash_fill("0");
					ua.setVideo_fill("0");
					ua.setNative_banner_fill("0");
					ua.setNative_plaque_fill("0");
					ua.setNative_splash_fill("0");
					ua.setPlaque_video_fill("0");
					ua.setNative_msg_fill("0");
					ua.setSystem_splash_fill("0");
					ua.setNative_new_plaque_fill("0");
					ua.setNative_new_banner_fill("0");
					ua.setSuspend_icon_fill("0");
				}

				// ***这里相同的一个应用数据，show和income数值需要叠加，并且ecpm需要自己计算***
				BigDecimal k = new BigDecimal(1000);
				if ("banner".equals(adtype)) {
					ua.setBanner_show(new BigDecimal(ua.getBanner_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setBanner_income(new BigDecimal(ua.getBanner_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setBanner_click(new BigDecimal(ua.getBanner_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setBanner_request(new BigDecimal(ua.getBanner_request()).add(new BigDecimal(vals[4])).toString());
					ua.setBanner_fill(new BigDecimal(ua.getBanner_fill()).add(new BigDecimal(vals[5])).toString());

					if (Integer.valueOf(ua.getBanner_show()) != 0
							&& Double.valueOf(ua.getBanner_income()) != 0) {

						ua.setBanner_ecpm(
								new BigDecimal(ua.getBanner_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getBanner_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("plaque".equals(adtype)) {
					ua.setPlaque_show(new BigDecimal(ua.getPlaque_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setPlaque_income(new BigDecimal(ua.getPlaque_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setPlaque_click(new BigDecimal(ua.getPlaque_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setPlaque_request(new BigDecimal(ua.getPlaque_request()).add(new BigDecimal(vals[4])).toString());
					ua.setPlaque_fill(new BigDecimal(ua.getPlaque_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getPlaque_show()) != 0
							&& Double.valueOf(ua.getPlaque_income()) != 0) {

						ua.setPlaque_ecpm(
								new BigDecimal(ua.getPlaque_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getPlaque_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}

				} else if ("splash".equals(adtype)) {
					ua.setSplash_show(new BigDecimal(ua.getSplash_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setSplash_income(new BigDecimal(ua.getSplash_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setSplash_click(new BigDecimal(ua.getSplash_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setSplash_request(new BigDecimal(ua.getSplash_request()).add(new BigDecimal(vals[4])).toString());
					ua.setSplash_fill(new BigDecimal(ua.getSplash_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getSplash_show()) != 0
							&& Double.valueOf(ua.getSplash_income()) != 0) {

						ua.setSplash_ecpm(
								new BigDecimal(ua.getSplash_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getSplash_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("video".equals(adtype)) {
					ua.setVideo_show(new BigDecimal(ua.getVideo_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setVideo_income(new BigDecimal(ua.getVideo_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setVideo_click(new BigDecimal(ua.getVideo_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setVideo_request(new BigDecimal(ua.getVideo_request()).add(new BigDecimal(vals[4])).toString());
					ua.setVideo_fill(new BigDecimal(ua.getVideo_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getVideo_show()) != 0
							&& Double.valueOf(ua.getVideo_income()) != 0) {

						ua.setVideo_ecpm(
								new BigDecimal(ua.getVideo_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getVideo_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("nativebanner".equals(adtype)) {
					ua.setNative_banner_show(new BigDecimal(ua.getNative_banner_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setNative_banner_income(new BigDecimal(ua.getNative_banner_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setNative_banner_click(new BigDecimal(ua.getNative_banner_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setNative_banner_request(new BigDecimal(ua.getNative_banner_request()).add(new BigDecimal(vals[4])).toString());
					ua.setNative_banner_fill(new BigDecimal(ua.getNative_banner_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getNative_banner_show()) != 0
							&& Double.valueOf(ua.getNative_banner_income()) != 0) {

						ua.setNative_banner_ecpm(
								new BigDecimal(ua.getNative_banner_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getNative_banner_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("nativeplaque".equals(adtype)) {
					ua.setNative_plaque_show(new BigDecimal(ua.getNative_plaque_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setNative_plaque_income(new BigDecimal(ua.getNative_plaque_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setNative_plaque_click(new BigDecimal(ua.getNative_plaque_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setNative_plaque_request(new BigDecimal(ua.getNative_plaque_request()).add(new BigDecimal(vals[4])).toString());
					ua.setNative_plaque_fill(new BigDecimal(ua.getNative_plaque_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getNative_plaque_show()) != 0
							&& Double.valueOf(ua.getNative_plaque_income()) != 0) {

						ua.setNative_plaque_ecpm(
								new BigDecimal(ua.getNative_plaque_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getNative_plaque_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("nativesplash".equals(adtype)) {
					ua.setNative_splash_show(new BigDecimal(ua.getNative_splash_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setNative_splash_income(new BigDecimal(ua.getNative_splash_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setNative_splash_click(new BigDecimal(ua.getNative_splash_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setNative_splash_request(new BigDecimal(ua.getNative_splash_request()).add(new BigDecimal(vals[4])).toString());
					ua.setNative_splash_fill(new BigDecimal(ua.getNative_splash_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getNative_splash_show()) != 0
							&& Double.valueOf(ua.getNative_splash_income()) != 0) {

						ua.setNative_splash_ecpm(
								new BigDecimal(ua.getNative_splash_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getNative_splash_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("plaquevideo".equals(adtype)) {
					ua.setPlaque_video_show(new BigDecimal(ua.getPlaque_video_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setPlaque_video_income(new BigDecimal(ua.getPlaque_video_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setPlaque_video_click(new BigDecimal(ua.getPlaque_video_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setPlaque_video_request(new BigDecimal(ua.getPlaque_video_request()).add(new BigDecimal(vals[4])).toString());
					ua.setPlaque_video_fill(new BigDecimal(ua.getPlaque_video_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getPlaque_video_show()) != 0
							&& Double.valueOf(ua.getPlaque_video_income()) != 0) {

						ua.setPlaque_video_ecpm(
								new BigDecimal(ua.getPlaque_video_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getPlaque_video_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}

				} else if ("nativemsg".equals(adtype)) {
					ua.setNative_msg_show(new BigDecimal(ua.getNative_msg_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setNative_msg_income(new BigDecimal(ua.getNative_msg_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setNative_msg_click(new BigDecimal(ua.getNative_msg_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setNative_msg_request(new BigDecimal(ua.getNative_msg_request()).add(new BigDecimal(vals[4])).toString());
					ua.setNative_msg_fill(new BigDecimal(ua.getNative_msg_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getNative_msg_show()) != 0
							&& Double.valueOf(ua.getNative_msg_income()) != 0) {

						ua.setNative_msg_ecpm(
								new BigDecimal(ua.getNative_msg_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getNative_msg_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				}
				//2021-08-26 添加
				else if ("systemsplash".equals(adtype)) {
					ua.setSystem_splash_show(new BigDecimal(ua.getSystem_splash_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setSystem_splash_income(new BigDecimal(ua.getSystem_splash_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setSystem_splash_click(new BigDecimal(ua.getSystem_splash_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setSystem_splash_request(new BigDecimal(ua.getSystem_splash_request()).add(new BigDecimal(vals[4])).toString());
					ua.setSystem_splash_fill(new BigDecimal(ua.getSystem_splash_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getSystem_splash_show()) != 0
							&& Double.valueOf(ua.getSystem_splash_income()) != 0) {

						ua.setSystem_splash_ecpm(
								new BigDecimal(ua.getSystem_splash_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getSystem_splash_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("nativenewplaque".equals(adtype)) {
					ua.setNative_new_plaque_show(new BigDecimal(ua.getNative_new_plaque_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setNative_new_plaque_income(new BigDecimal(ua.getNative_new_plaque_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setNative_new_plaque_click(new BigDecimal(ua.getNative_new_plaque_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setNative_new_plaque_request(new BigDecimal(ua.getNative_new_plaque_request()).add(new BigDecimal(vals[4])).toString());
					ua.setNative_new_plaque_fill(new BigDecimal(ua.getNative_new_plaque_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getNative_new_plaque_show()) != 0
							&& Double.valueOf(ua.getNative_new_plaque_income()) != 0) {

						ua.setNative_new_plaque_ecpm(
								new BigDecimal(ua.getNative_new_plaque_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getNative_new_plaque_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("nativenewbanner".equals(adtype)) {
					ua.setNative_new_banner_show(new BigDecimal(ua.getNative_new_banner_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setNative_new_banner_income(new BigDecimal(ua.getNative_new_banner_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setNative_new_banner_click(new BigDecimal(ua.getNative_new_banner_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setNative_new_banner_request(new BigDecimal(ua.getNative_new_banner_request()).add(new BigDecimal(vals[4])).toString());
					ua.setNative_new_banner_fill(new BigDecimal(ua.getNative_new_banner_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getNative_new_banner_show()) != 0
							&& Double.valueOf(ua.getNative_new_banner_income()) != 0) {

						ua.setNative_new_banner_ecpm(
								new BigDecimal(ua.getNative_new_banner_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getNative_new_banner_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				} else if ("suspendicon".equals(adtype)) {
					ua.setSuspend_icon_show(new BigDecimal(ua.getSuspend_icon_show())
							.add(new BigDecimal(vals[1])).toString());
					ua.setSuspend_icon_income(new BigDecimal(ua.getSuspend_icon_income())
							.add(new BigDecimal(vals[2])).toString());
					ua.setSuspend_icon_click(new BigDecimal(ua.getSuspend_icon_click())
							.add(new BigDecimal(vals[3])).toString());
					ua.setSuspend_icon_request(new BigDecimal(ua.getSuspend_icon_request()).add(new BigDecimal(vals[4])).toString());
					ua.setSuspend_icon_fill(new BigDecimal(ua.getSuspend_icon_fill()).add(new BigDecimal(vals[5])).toString());
					if (Integer.valueOf(ua.getSuspend_icon_show()) != 0
							&& Double.valueOf(ua.getSuspend_icon_income()) != 0) {

						ua.setSuspend_icon_ecpm(
								new BigDecimal(ua.getSuspend_icon_income())
										.multiply(k)
										.divide(new BigDecimal(ua.getSuspend_icon_show()), 2, RoundingMode.FLOOR)
										.toString()
						);
					}
				}
				valMap.put(appkey, ua);
			}

			// 友盟key对应的活跃用户数据
			DateTimeFormatter format = DateTimeFormat.forPattern("yyyyMMdd");
			DateTime dayDate = DateTime.parse(day,format);
			String dateStr = dayDate.toString("yyyy-MM-dd");
			String sql = "select addnum,tdate,b.app_name appname, b.umeng_key appkey,dau from adv_platform_pagedata_info a  " +
					" inner join app_info b on a.appid=b.id inner join adv_platform_app_info c on a.appid =c.appid and a.tappid =c.tappid "
					+ " where tdate = '" + dateStr + "' and c.channel = '" + channel + "'";
			List<Map<String, Object>> appMap = adMapper.queryListMap(sql);

			// 获取展示，点击 覆盖用户数
			List<Map<String, Object>> cilckActUserList = dnwxBiAdtMapper.selectClickActuser(day);
			Map<String, Map<String, Object>> cilckActUserMap = cilckActUserList.stream().collect(Collectors.toMap(data -> ""+data.get("appid") + data.get("download_channel"), Function.identity(),(k1,k2)->k1));

			List<Map<String, Object>> showActUserList = dnwxBiAdtMapper.selectAdshowActuser(day);
			Map<String, List<Map<String, Object>>> showActUserMap = showActUserList.stream().collect(Collectors.groupingBy(data -> ""+data.get("appid") + data.get("download_channel")));

			for (Entry<String, UmengAdIncomeVo> next : valMap.entrySet()) {
				UmengAdIncomeVo value = next.getValue();

				boolean flag = true;
				// 展示收入内容的友盟key与数据库友盟key匹配，获取活跃用户数
				for (Map<String, Object> map : appMap) {
					if (value.getAppkey().equals(map.get("appkey").toString())) {
						value.setAppid(appInfoMap.get(map.get("appkey").toString()).get("appid") !=null?appInfoMap.get(map.get("appkey").toString()).get("appid").toString():"");
						value.setAppname(map.get("appname").toString());
						value.setActnum(Integer.parseInt(map.get("dau").toString()));
						value.setAddnum(Integer.parseInt(map.get("addnum").toString()));
						flag = false;
						break;
					}
				}
				// 没有匹配到渠道里的这个应用
				if (flag) {
					continue;
				}

				// 获得活跃数后计算其它数据
				BigDecimal actnum = new BigDecimal(value.getActnum());
				if (value.getActnum() != 0) {
					value.setTotal_income(
							new BigDecimal(value.getBanner_income())
									.add(new BigDecimal(value.getPlaque_income()))
									.add(new BigDecimal(value.getSplash_income()))
									.add(new BigDecimal(value.getVideo_income()))
									.add(new BigDecimal(value.getNative_banner_income()))
									.add(new BigDecimal(value.getNative_plaque_income()))
									.add(new BigDecimal(value.getNative_splash_income()))
									.add(new BigDecimal(value.getPlaque_video_income()))
									.add(new BigDecimal(value.getNative_msg_income()))
									//2021-08-26 添加
									.add(new BigDecimal(value.getSystem_splash_income()))
									.add(new BigDecimal(value.getNative_new_plaque_income()))
									.add(new BigDecimal(value.getNative_new_banner_income()))
									.add(new BigDecimal(value.getSuspend_icon_income()))

									.setScale(2, RoundingMode.HALF_UP).toString()
					);
					value.setDau_arpu(
							new BigDecimal(value.getTotal_income())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);

					//新增占比
					value.setAvgnum(new BigDecimal(value.getAddnum()).multiply(new BigDecimal(100))
							.divide(actnum, 2, RoundingMode.HALF_UP)
							+ "%");

					// 各类型 人均pv
					value.setBanner_pv(
							new BigDecimal(value.getBanner_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setPlaque_pv(
							new BigDecimal(value.getPlaque_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setSplash_pv(
							new BigDecimal(value.getSplash_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setVideo_pv(
							new BigDecimal(value.getVideo_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setMsg_pv(
							new BigDecimal(value.getNative_msg_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setPlaque_video_pv(
							new BigDecimal(value.getPlaque_video_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);

					//2021-08-26 添加
					value.setSystem_splash_pv(
							new BigDecimal(value.getSystem_splash_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					//人均pv-原生新样式插屏 = (原生新样式插屏)/活跃 2021-09-08
					value.setNative_new_plaque_pv(
							new BigDecimal(value.getNative_new_plaque_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					//人均pv-原生新样式banner = （原生新样式banner）/活跃 2021-09-08
					value.setNative_new_banner_pv(
							new BigDecimal(value.getNative_new_banner_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setSuspend_icon_pv(
							new BigDecimal(value.getSuspend_icon_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					//原生插屏人均pv
					value.setNative_plaque_pv(
							new BigDecimal(value.getNative_plaque_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					//原生banner人均pv
					value.setNative_banner_pv(
							new BigDecimal(value.getNative_banner_show())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					// 每用户平均收入 arpu
					//所有banner收入（banner入+原生banner收入+原生新样式banner收入）/活跃 banner人均arpu 2021-09-02修改
					value.setBanner_arpu(
							new BigDecimal(value.getBanner_income())
									.add(new BigDecimal(value.getNative_banner_income()))
									.add(new BigDecimal(value.getNative_new_banner_income()))
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					//所有插屏收入（插屏+原生插屏+原生新样式插屏）/活跃  插屏人均arpu 2021-09-02修改
					value.setPlaque_arpu(
							new BigDecimal(value.getPlaque_income())
									.add(new BigDecimal(value.getNative_plaque_income()))
									.add(new BigDecimal(value.getNative_new_plaque_income()))
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);

					//所有开屏收入(开屏收入+原生开屏收入+系统开屏收入)/活跃 开屏人均arpu 2021-09-02修改
					value.setSplash_arpu(
							new BigDecimal(value.getSplash_income())
									.add(new BigDecimal(value.getNative_splash_income()))
									.add(new BigDecimal(value.getSystem_splash_income()))
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setVideo_arpu(
							new BigDecimal(value.getVideo_income())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setMsg_arpu(
							new BigDecimal(value.getNative_msg_income())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setPlaque_video_arpu(
							new BigDecimal(value.getPlaque_video_income())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);

					//2021-08-26 添加
					value.setSystem_splash_arpu(
							new BigDecimal(value.getSystem_splash_income())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setNative_new_plaque_arpu(
							new BigDecimal(value.getNative_new_plaque_income())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setNative_new_banner_arpu(
							new BigDecimal(value.getNative_new_banner_income())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
					value.setSuspend_icon_arpu(
							new BigDecimal(value.getSuspend_icon_income())
									.divide(actnum, 2, RoundingMode.HALF_UP)
									.toString()
					);
				}
				//渗透率相关字段
				// 获取对应的展示，点击的覆盖用户数
				calculateCilckActUserNum(value,cilckActUserMap);
				calculateShowActUserNum(value,showActUserMap);

				dataList.add(value);
			}
		}catch (Exception e){
			logger.error("syncPlatformAdData error:",e);
		}

		if (dataList != null && dataList.size() > 0) {
			try {
				dataList.forEach(data -> {
					String cha = data.getChannel();
					data.setMedia(UMENG_CHANNEL_MEDIA_MAP.get(cha));
				});

				adMapper.insertOppoAdIncomeList(dataList);
			} catch (Exception e) {
				logger.error("syncPlatformAdData insert error:",e);
			}
		}
	}




	/**
	 * 创建自定义事件
	 * @param appkey 友盟appid
	 * @param umeng_account 友盟账号
	 * @return
	 */
	@Async("aaaScheduler")
	@Override
	public void syncUmengUappEventCreate(String appkey, String umeng_account) {

		ApiExecutor apiExecutor = UsertagTask.getUmengAccountExecutor(umeng_account);

		// 需要创建的友盟自定义事件列表
		String sql = "select * from umeng_event_create_list";
		List<Map<String, Object>> eventList = adMapper.queryListMap(sql);
		try {
			for (Map<String, Object> act : eventList) {
				Thread.sleep(2100L);

				UmengUappEventCreateParam param = new UmengUappEventCreateParam();
				// 测试环境只支持http
				param.getOceanRequestPolicy().setUseHttps(false);
				param.setAppkey(appkey);
				param.setEventName(act.get("event_name").toString());
				param.setEventDisplayName(URLEncoder.encode(act.get("event_displayname").toString()));
				param.setEventType(false);

				UmengUappEventCreateResult result = apiExecutor.execute(param);
				System.out.println("创建自定义事件结果打印："+JSONObject.toJSONString(result));
			}

		} catch (OceanException e) {
			System.out.println("errorCode=" + e.getErrorCode() + ", errorMessage=" + e.getErrorMessage());
			commonService.sendFsMsg("友盟产品 "+appkey+" 创建自定义事件异常：errorCode=" + e.getErrorCode() + ", errorMessage=" + e.getErrorMessage());
		} catch (Exception e) {
			commonService.sendFsMsg("友盟产品 "+appkey+" 创建自定义事件异常：" + e.getMessage());
			throw new RuntimeException(e);
		}
	}

    @Override
    public void syncChannelSelfData(String day, String channel) {
        List<UmengAdIncomeVo> dataList = new ArrayList<UmengAdIncomeVo>();
        Map<String, UmengAdIncomeVo> valMap = new HashMap<String, UmengAdIncomeVo>();
        JSONObject ret = new JSONObject();
        //查询自统计数据 ads_realization_income_assess_daily : 根据时间 + 子渠道
        String biSqlTemplate = "SELECT CONCAT(adi.appid,adi.cha_id,'%s') AS posId,adi.appid,adi.adpos_type,adi.sdk_adtype,CONCAT(adi.sdk_adtype,\"_\",adi.adpos_type) AS adtype,IFNULL(SUM(income),0) AS income,IFNULL(SUM(show_num),0) AS view,IFNULL(SUM(click_num),0) AS click,IFNULL(SUM(req_num),0) AS request_count,IFNULL(SUM(fill_num), 0) AS fill_count,app.app_name AS appname,app.umeng_key AS appkey FROM ads_realization_income_assess_daily adi LEFT JOIN dnwx_bi.dn_extend_adsid_list bb ON adi.adsid=bb.sid LEFT JOIN app_info app ON adi.appId = app.id WHERE adi.tdate = '%s' AND adi.cha_id = '%s' GROUP BY adi.tdate,adi.appid,adi.cha_id,adi.adpos_type,adi.sdk_adtype";
        String biSql = String.format(biSqlTemplate, "_self_key", DateUtils.changeDateStringToHyphen(day), channel);
        List<Map<String, Object>> realizationIncomes = dnwxBiMapper.queryListMap(biSql);
        JSONArray array = new JSONArray();
        array.addAll(realizationIncomes);
        if(CollectionUtils.isEmpty(realizationIncomes)){
            ret.put("ret",0);
            ret.put("msg","channel:"+channel+","+day+"未获取到数据，请切换日期重试！");
            logger.info(JSON.toJSONString(ret));
            return;
        }
        //day 时间格式转换
        day = DateUtils.changeDateStringToHyphen(day);
        //初始化自统计数据
        initSelfUmengData(day, channel, valMap, array);

		// 友盟key对应的活跃用户数据
		String sql = "select add_num addnum,tdate,app_key appkey,act_num actnum from umeng_user_channel_total"
				+ " where tdate = '"+day+"' and install_channel = '"+channel+"'";
		//特殊处理 渠道为huawei时 新增活跃以 huawei+huaweiml
		if ("huawei".equals(channel)){
			sql = "select IFNULL(sum(add_num),0) addnum,tdate,app_key appkey,IFNULL(sum(act_num),0) actnum from umeng_user_channel_total "
					+ " where tdate = '"+day+"' and install_channel in('huawei','huaweiml') GROUP BY tdate,app_key ";
		}
		if ("huawei2".equals(channel)){
			sql = "select IFNULL(sum(add_num),0) addnum,tdate,app_key appkey,IFNULL(sum(act_num),0) actnum from umeng_user_channel_total "
					+ " where tdate = '"+day+"' and install_channel in('huawei2','huaweiml2') GROUP BY tdate,app_key ";
		}
		//特殊处理 渠道为oppomj时 新增活跃以oppomj+oppomj2
		if ("oppomj".equals(channel)){
			sql = "select IFNULL(sum(add_num),0) addnum,tdate,app_key appkey,IFNULL(sum(act_num),0) actnum from umeng_user_channel_total "
					+ " where tdate = '"+day+"' and install_channel in('oppomj','oppomj2') GROUP BY tdate,app_key ";
		}
		//特殊处理 渠道为h5_vivo时 新增活跃从vivo小游戏表中获取
		if ("h5_vivo".equals(channel)){
			sql = "SELECT tdate, channel, new_user addnum, day_active_user actnum,t2.appid,t2.appid appkey  FROM `vivo_quick_game_info` t1\n" +
					"LEFT JOIN adv_platform_app_info t2 on t1.tpackage = t2.packagename\n" +
					"where t1.tdate = '"+day+"' and t2.appid is not null";
		}
		//特殊处理：渠道为h5_oppo时 新增活跃 从 渠道详情页转化分析 获取
		if ("h5_oppo".equals(channel)){
			// 调试时注意day 时间格式是否正确合理
			sql = "SELECT a.tdate,a.appid,a.appid AS appkey,b.channel,IFNULL(SUM(CAST(dau AS UNSIGNED)), 0) AS actnum,IFNULL(SUM(CAST(addnum AS UNSIGNED)), 0) AS addnum FROM adv_platform_pagedata_info a LEFT JOIN adv_platform_app_info b ON a.appid = b.appid AND a.platform = b.platform and a.tappid = b.tappid WHERE a.tdate = '"+day+"' AND b.channel = 'h5_oppo' GROUP BY a.tdate,a.appid,b.channel";
		}
		List<Map<String, Object>> appMap = adMapper.queryListMap(sql);
		// 获取展示，点击 覆盖用户数
		List<Map<String, Object>> cilckActUserList = dnwxBiAdtMapper.selectClickActuser(day);
		Map<String, Map<String, Object>> cilckActUserMap = cilckActUserList.stream().collect(Collectors.toMap(data -> ""+data.get("appid") + data.get("download_channel"), Function.identity(),(k1,k2)->k1));

		List<Map<String, Object>> showActUserList = dnwxBiAdtMapper.selectAdshowActuser(day);
		Map<String, List<Map<String, Object>>> showActUserMap = showActUserList.stream().collect(Collectors.groupingBy(data -> ""+data.get("appid") + data.get("download_channel")));

		for (Entry<String, UmengAdIncomeVo> next : valMap.entrySet()) {
            UmengAdIncomeVo value = next.getValue();
            //封装活跃度actnum和新增数addnum
            // 展示收入内容的友盟key与数据库友盟key匹配，获取活跃用户数
			for (Map<String, Object> map : appMap) {
				if(value.getAppkey().equals(map.get("appkey").toString())){
					//2022-06-09替换成新的数据源表 产品名称 app_info 新增活跃 umeng_user_channel_total
					value.setActnum(Integer.parseInt(map.get("actnum")==null?"0":map.get("actnum").toString()));
					value.setAddnum(Integer.parseInt(map.get("addnum")==null?"0":map.get("addnum").toString()));
				}
			}
            //根据活跃度计算其他相关数据
			calculateUmengFromActnum(value);
			// 获取对应的展示，点击的覆盖用户数
			calculateCilckActUserNum(value,cilckActUserMap);
			calculateShowActUserNum(value,showActUserMap);
            dataList.add(value);
        }
        if(!CollectionUtils.isEmpty(dataList)){
            try {
                String advPlatformSql = "SELECT concat(appid, '_', channel) mapkey, tappname FROM `adv_platform_app_info`\n" +
                        "where channel = '"+channel+"' and bindEndTime >= '"+day+"'\n" +
                        "ORDER BY bindEndTime desc";
                Map<String, Map<String, Object>> advPlatformMap = adMapper.queryListMapOfKey(advPlatformSql);
                dataList.forEach(data -> {
                            String cha = data.getChannel();
                            data.setMedia(UMENG_CHANNEL_MEDIA_MAP.get(cha));
                            Map<String, Object> map = advPlatformMap.get(data.getAppid() + "_" + cha);
                            if (map != null) {
                                data.setGameName(map.get("tappname") == null ? null : map.get("tappname").toString());
                            } else {
                                data.setGameName(null);
                            }
                            data.setAppkey(data.getAppkey() + "_2");
                        });
                // 2024-05-08 sdk功能标识查询改为，状态 已上线，且日期不超过查询日期，的最大prjId
                String tempModuleSql = "SELECT singleid mapkey, tempName,largeVer FROM dnwx_client.`wbgui_tempmodule`";
                String formconfigSql = "SELECT  left(pjId, 5) appid, pjId, channelTag,  tmp_mod_id, state, OnlineDate  FROM dnwx_client.`wbgui_formconfig`\n" +
                        "where state = '已上线' \n" +
                        "and channelTag = '"+channel+"'\n" +
                        "and STR_TO_DATE(OnlineDate, '%Y/%m/%d %H:%i:%S') < STR_TO_DATE('"+day.replaceAll("-","")+" 23:59:59', '%Y%m%d %H:%i:%S')\n" +
                        "and (tmp_mod_id is not null and tmp_mod_id != '')";
                List<Map<String, String>> maps = adMapper.queryListMapOne(formconfigSql);
                Map<String, Map<String, Object>> tempModule = adMapper.queryListMapOfKey(tempModuleSql);
                maps.stream().filter(map -> BlankUtils.isNotBlank(map.get("tmp_mod_id")))
                        .forEach(map -> {
                            String tmpModId = map.get("tmp_mod_id");
                            if (tmpModId.contains(";")) {
                                String[] split = tmpModId.split(";");
                                StringJoiner sj = new StringJoiner(" | ");
                                StringJoiner largeVer = new StringJoiner(" | ");
                                for (String s : split) {
                                    Map<String, Object> map1 = tempModule.get(s);
                                    if (!CollectionUtils.isEmpty(map1)) {
                                        sj.add(BlankUtils.getString(map1.get("tempName")));
										largeVer.add(BlankUtils.getString(map1.get("largeVer")));
                                    }
                                }
                                map.put("large_ver", largeVer.toString());
                                map.put("temp_name", sj.toString());
                                map.put("tmp_mod_id", tmpModId.replace(";", " | "));
                            } else {
                                Map<String, Object> map1 = tempModule.get(tmpModId);
                                if (!CollectionUtils.isEmpty(map1)) {
                                    map.put("temp_name", BlankUtils.getString(map1.get("tempName")));
                                    map.put("large_ver", BlankUtils.getString(map1.get("largeVer")));
                                }
                            }
                        });
                Map<String, Map<String, String>> sdkMap = maps.stream().collect(Collectors.toMap(
                        map -> map.get("appid") + "_" + map.get("channelTag"), map -> map,
                        (x, y) -> {
                            if (x.get("pjId").compareTo(y.get("pjId")) > 0) {
                                return x;
                            } else {
                                return y;
                            }
                        }
                ));
				//获取小米违规记录数据
				List<XiaomiViolationRecordVo> recordVoList = violationRecordMapper.queryAdsenseTypes(day);
				Map<String,String> recordVoMap = new HashMap<>();
				Map<String, List<XiaomiViolationRecordVo>> collect = recordVoList.stream().collect(Collectors.groupingBy(data -> data.getAppid() + "_" + data.getChannel()));
				for (Entry<String, List<XiaomiViolationRecordVo>> entry : collect.entrySet()) {
					String adsenseType = entry.getValue().stream().map(XiaomiViolationRecordVo::getAdsense_type).distinct().collect(Collectors.joining("|"));
					recordVoMap.put(entry.getKey(),adsenseType);
				}
				// 获取 产品中活跃最大的项目id所带的临时模块
				Map<String, Map<String, String>> maxNumTempId = getMaxNumProjectTempId(day,channel);
				//获取最大覆盖量的模块版本
				Map<String, String> maxNumLagerVer = getMaxNumLagerVer(day, channel);

				for (UmengAdIncomeVo umengAdIncomeVo : dataList) {
                    //匹配功能标识
					String key = umengAdIncomeVo.getAppid() +"_"+ umengAdIncomeVo.getChannel();
                    if (sdkMap.containsKey(key)) {
						Map<String, String> map = sdkMap.get(key);
                        umengAdIncomeVo.setTemp_id(map.get("tmp_mod_id"));
                        umengAdIncomeVo.setTemp_name(map.get("temp_name"));
						umengAdIncomeVo.setLarge_ver(map.get("large_ver"));
                    } else {
						//不存在功能标识时，获取覆盖量最大的模块版本
						umengAdIncomeVo.setLarge_ver(maxNumLagerVer.get(key));
					}
					//封装 产品中活跃最大的项目id所带的临时模块
					if (maxNumTempId.containsKey(key)) {
						Map<String, String> tempMap = maxNumTempId.get(key);
						umengAdIncomeVo.setActive_temp_id(tempMap.get("temp_id"));
						umengAdIncomeVo.setActive_temp_name(tempMap.get("temp_name"));
					}
					if (recordVoMap.containsKey(key)) {
						//封装违规广告类型数据
						umengAdIncomeVo.setAd_violation_type(recordVoMap.get(key));
					}
                }
                try {
                    //增加adb表的数据入库 (测试环境无权限新增操作,使用下下面的数据进行新增操作)
                    umengMonitorMapper.insertUmengAdIncomeList(dataList);
                }catch (Exception e){
                    logger.info("自统计--新增adb报错：", e);
                }
                int i = adMapper.insertUmengAdIncomeList(dataList);
                logger.info("channel:"+channel+",时间:"+day+" 自统计同步完成");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

	private void calculateShowActUserNum(UmengAdIncomeVo value, Map<String, List<Map<String, Object>>> showActUserMap) {
		if (ObjectUtils.isEmpty(value) || ObjectUtils.isEmpty(showActUserMap)) {
			return;
		}
		String mainKey = value.getAppid() + value.getChannel();
		if (showActUserMap.containsKey(mainKey)) {
			List<Map<String, Object>> showActUserList = showActUserMap.get(mainKey);
			//根据ad_type类型进行数据封装
			for (Map<String, Object> showActUser : showActUserList) {
				String adActiveUser = showActUser.get("ad_active_user") + "";
				String globalAdActiveUser = showActUser.get("global_ad_active_user") + "";
				value.setShow_total_ad_active_cnt(globalAdActiveUser);
				String ad_type = showActUser.get("ad_type") + "";
				if ("banner".equals(ad_type)) {
					value.setShow_banner_ad_active_cnt(adActiveUser);
				} else if ("msg".equals(ad_type)) {
					value.setShow_msg_ad_active_cnt(adActiveUser);
				} else if ("plaque".equals(ad_type)) {
					value.setShow_plaque_ad_active_cnt(adActiveUser);
				} else if ("splash".equals(ad_type)) {
					value.setShow_splash_ad_active_cnt(adActiveUser);
				} else if ("video".equals(ad_type)) {
					value.setShow_video_ad_active_cnt(adActiveUser);
				} else if ("icon".equals(ad_type)) {
					value.setShow_icon_ad_active_cnt(adActiveUser);
				}
			}
		}
	}

	private void calculateCilckActUserNum(UmengAdIncomeVo value, Map<String, Map<String, Object>> cilckActUserMap) {
		if (ObjectUtils.isEmpty(value) || ObjectUtils.isEmpty(cilckActUserMap)) {
			return;
		}
		String mainKey = value.getAppid() + value.getChannel();
		if (cilckActUserMap.containsKey(mainKey)) {
			Map<String, Object> cilckMap = cilckActUserMap.get(mainKey);
			//根据ad_type类型进行数据封装
			value.setClick_total_ad_active_cnt(cilckMap.get("global_click_user") + "");
			value.setClick_banner_ad_active_cnt(cilckMap.get("banner_click_users") + "");
			value.setClick_msg_ad_active_cnt(cilckMap.get("msg_click_users") + "");
			value.setClick_plaque_ad_active_cnt(cilckMap.get("plaque_click_users") + "");
			value.setClick_splash_ad_active_cnt(cilckMap.get("splash_click_users") + "");
			value.setClick_video_ad_active_cnt(cilckMap.get("video_click_users") + "");
			value.setClick_icon_ad_active_cnt(cilckMap.get("icon_click_users") + "");
		}
	}

    /**
    * oppo渠道自统计部分数据生成
    * @param day 时间,格式:20240811
    * @param channel 子渠道
    */
    @Override
    public void syncOppoAdSelfData(String day, String channel) {
        List<UmengAdIncomeVo> dataList = new ArrayList<>();
        Map<String, UmengAdIncomeVo> valMap = new HashMap<>();
        //查询自统计数据 ads_realization_income_assess_daily : 根据时间 + 子渠道
        String biSqlTemplate = "SELECT CONCAT(adi.appid,adi.cha_id,'%s') AS posId,adi.appid,adi.adpos_type,adi.sdk_adtype,CONCAT(adi.sdk_adtype,\"_\",adi.adpos_type) AS adtype,IFNULL(SUM(income),0) AS income,IFNULL(SUM(show_num),0) AS view,IFNULL(SUM(click_num),0) AS click,IFNULL(SUM(req_num),0) AS request_count,IFNULL(SUM(fill_num), 0) AS fill_count,app.app_name AS appname,app.umeng_key AS appkey FROM ads_realization_income_assess_daily adi LEFT JOIN dnwx_bi.dn_extend_adsid_list bb ON adi.adsid=bb.sid LEFT JOIN app_info app ON adi.appId = app.id WHERE adi.tdate = '%s' AND adi.cha_id = '%s' GROUP BY adi.tdate,adi.appid,adi.cha_id,adi.adpos_type,adi.sdk_adtype";
        String biSql = String.format(biSqlTemplate, "_self_key", DateUtils.changeDateStringToHyphen(day), channel);
        List<Map<String, Object>> realizationIncomes = dnwxBiMapper.queryListMap(biSql);
        JSONArray array = new JSONArray();
        array.addAll(realizationIncomes);
        if (CollectionUtils.isEmpty(realizationIncomes)) {
            return;
        }
        //day 时间格式转换 yyyy-mm-dd
        day = DateUtils.changeDateStringToHyphen(day);
        //初始化自统计数据
        initSelfUmengData(day, channel, valMap, array);
        // 友盟key对应的活跃用户数据
        String sql = "select addnum,tdate,b.app_name appname, b.umeng_key appkey,dau from adv_platform_pagedata_info a  " +
                " inner join app_info b on a.appid=b.id inner join adv_platform_app_info c on a.appid =c.appid and a.tappid =c.tappid "
                + " where tdate = '" + day + "' and c.channel = '" + channel + "'";
        List<Map<String, Object>> appMap = adMapper.queryListMap(sql);

		// 获取展示，点击 覆盖用户数
		List<Map<String, Object>> cilckActUserList = dnwxBiAdtMapper.selectClickActuser(day);
		Map<String, Map<String, Object>> cilckActUserMap = cilckActUserList.stream().collect(Collectors.toMap(data -> ""+data.get("appid") + data.get("download_channel"),Function.identity(),(k1,k2)->k1));

		List<Map<String, Object>> showActUserList = dnwxBiAdtMapper.selectAdshowActuser(day);
		Map<String, List<Map<String, Object>>> showActUserMap = showActUserList.stream().collect(Collectors.groupingBy(data -> ""+data.get("appid") + data.get("download_channel")));


		for (Entry<String, UmengAdIncomeVo> next : valMap.entrySet()) {
            UmengAdIncomeVo value = next.getValue();
            //封装活跃度actnum和新增数addnum
            // 展示收入内容的友盟key与数据库友盟key匹配，获取活跃用户数
            boolean flag = true;
            for (Map<String, Object> map : appMap) {
                if(value.getAppkey().equals(map.get("appkey").toString())){
                    //2022-06-09替换成新的数据源表 产品名称 app_info 新增活跃 umeng_user_channel_total
                    value.setActnum(Integer.parseInt(map.get("actnum")==null?"0":map.get("actnum").toString()));
                    value.setAddnum(Integer.parseInt(map.get("addnum")==null?"0":map.get("addnum").toString()));
                    flag = false;
                    break;
                }
            }
            // 没有匹配到渠道里的这个应用
            if (flag) {
                continue;
            }
            //根据活跃度计算其他相关数据
            calculateUmengFromActnum(value);
			// 获取对应的展示，点击的覆盖用户数
			calculateCilckActUserNum(value,cilckActUserMap);
			calculateShowActUserNum(value,showActUserMap);
            dataList.add(value);
        }
        if (!CollectionUtils.isEmpty(dataList)) {
            try {
                dataList.forEach(data -> {
                    String cha = data.getChannel();
                    data.setAppkey(data.getAppkey() + "_2");
                    data.setMedia(UMENG_CHANNEL_MEDIA_MAP.get(cha));
                });
                adMapper.insertOppoAdIncomeList(dataList);
            } catch (Exception e) {
                logger.error("syncPlatformAdSelfData insert error:",e);
            }
        }
    }

    private static void calculateUmengFromActnum(UmengAdIncomeVo value) {
        // 获得活跃数后计算其它数据
        BigDecimal actnum = new BigDecimal(value.getActnum());
        value.setTotal_income(
                        new BigDecimal(value.getBanner_income())
                        .add(new BigDecimal(value.getPlaque_income()))
                        .add(new BigDecimal(value.getSplash_income()))
                        .add(new BigDecimal(value.getVideo_income()))
                        .add(new BigDecimal(value.getNative_banner_income()))
                        .add(new BigDecimal(value.getNative_plaque_income()))
                        .add(new BigDecimal(value.getNative_splash_income()))
                        .add(new BigDecimal(value.getPlaque_video_income()))
                        .add(new BigDecimal(value.getNative_msg_income()))
                        //2021-08-26 添加
                        .add(new BigDecimal(value.getSystem_splash_income()))
                        .add(new BigDecimal(value.getNative_new_plaque_income()))
                        .add(new BigDecimal(value.getNative_new_banner_income()))
                        .add(new BigDecimal(value.getSuspend_icon_income()))
                        .setScale(2, RoundingMode.HALF_UP).toString()
                    );
        if(value.getActnum() != 0){
            value.setDau_arpu(
                new BigDecimal(value.getTotal_income())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );

            //新增占比
            value.setAvgnum(new BigDecimal(value.getAddnum()).multiply(new BigDecimal(100))
                    .divide(actnum, 2, RoundingMode.HALF_UP)
                    +"%");

            // 各类型 人均pv
            value.setBanner_pv(
                new BigDecimal(value.getBanner_show())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            value.setPlaque_pv(
                new BigDecimal(value.getPlaque_show())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            value.setSplash_pv(
                new BigDecimal(value.getSplash_show())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            value.setVideo_pv(
                new BigDecimal(value.getVideo_show())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            value.setMsg_pv(
                new BigDecimal(value.getNative_msg_show())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            value.setPlaque_video_pv(
                new BigDecimal(value.getPlaque_video_show())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );

            //2021-08-26 添加
            value.setSystem_splash_pv(
                    new BigDecimal(value.getSystem_splash_show())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            //人均pv-原生新样式插屏 = (原生新样式插屏)/活跃 2021-09-08
            value.setNative_new_plaque_pv(
                    new BigDecimal(value.getNative_new_plaque_show())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            //人均pv-原生新样式banner = （原生新样式banner）/活跃 2021-09-08
            value.setNative_new_banner_pv(
                    new BigDecimal(value.getNative_new_banner_show())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            value.setSuspend_icon_pv(
                    new BigDecimal(value.getSuspend_icon_show())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            //原生插屏人均pv
            value.setNative_plaque_pv(
                    new BigDecimal(value.getNative_plaque_show())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            //原生banner人均pv
            value.setNative_banner_pv(
                    new BigDecimal(value.getNative_banner_show())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            // 每用户平均收入 arpu
            //所有banner收入（banner入+原生banner收入+原生新样式banner收入）/活跃 banner人均arpu 2021-09-02修改
            value.setBanner_arpu(
                new BigDecimal(value.getBanner_income())
                .add(new BigDecimal(value.getNative_banner_income()))
                .add(new BigDecimal(value.getNative_new_banner_income()))
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            //所有插屏收入（插屏+原生插屏+原生新样式插屏）/活跃  插屏人均arpu 2021-09-02修改
            value.setPlaque_arpu(
                new BigDecimal(value.getPlaque_income())
                .add(new BigDecimal(value.getNative_plaque_income()))
                .add(new BigDecimal(value.getNative_new_plaque_income()))
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );

            //所有开屏收入(开屏收入+原生开屏收入+系统开屏收入)/活跃 开屏人均arpu 2021-09-02修改
            value.setSplash_arpu(
                new BigDecimal(value.getSplash_income())
                .add(new BigDecimal(value.getNative_splash_income()))
                .add(new BigDecimal(value.getSystem_splash_income()))
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            value.setVideo_arpu(
                new BigDecimal(value.getVideo_income())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            value.setMsg_arpu(
                new BigDecimal(value.getNative_msg_income())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );
            value.setPlaque_video_arpu(
                new BigDecimal(value.getPlaque_video_income())
                .divide(actnum, 2, RoundingMode.HALF_UP)
                .toString()
            );

            //2021-08-26 添加
            value.setSystem_splash_arpu(
                    new BigDecimal(value.getSystem_splash_income())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            value.setNative_new_plaque_arpu(
                    new BigDecimal(value.getNative_new_plaque_income())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            value.setNative_new_banner_arpu(
                    new BigDecimal(value.getNative_new_banner_income())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
            value.setSuspend_icon_arpu(
                    new BigDecimal(value.getSuspend_icon_income())
                            .divide(actnum, 2, RoundingMode.HALF_UP)
                            .toString()
            );
        }
    }

    private static void initSelfUmengData(String day, String channel, Map<String,UmengAdIncomeVo> valMap, JSONArray array) {

        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            String[] vals = {object.getString("posId"), object.getString("view"), object.getString("income"), object.getString("click"), object.getString("request_count"), object.getString("fill_count")};
            String appkey = object.getString("appkey");
            String adtype = AD_SHOW_TYPE_MAP.getOrDefault(object.getString("adtype"), Strings.EMPTY);
            UmengAdIncomeVo ua = valMap.get(appkey);
            if(ObjectUtils.isEmpty(ua) || StringUtils.isEmpty(ua.getAppkey())){
                // 初始化应用数据
                ua = new UmengAdIncomeVo();
                ua.setTdate(day);
                ua.setChannel(channel);
                if (StringUtils.isEmpty(appkey)) continue;
                ua.setAppkey(appkey);
                ua.setAdcode(vals[0]);
                ua.setAppid(object.getString("appid"));
                ua.setAppname(object.getString("appname"));
                ua.setSource("2");
                ua.setBanner_show("0");
                ua.setPlaque_show("0");
                ua.setSplash_show("0");
                ua.setVideo_show("0");
                ua.setNative_banner_show("0");
                ua.setNative_plaque_show("0");
                ua.setNative_splash_show("0");
                ua.setPlaque_video_show("0");
                ua.setNative_msg_show("0");
                //2021-08-26 添加
                ua.setSystem_splash_show("0");
                ua.setNative_new_banner_show("0");
                ua.setNative_new_plaque_show("0");
                ua.setSuspend_icon_show("0");
                ua.setBanner_income("0");
                ua.setPlaque_income("0");
                ua.setSplash_income("0");
                ua.setVideo_income("0");
                ua.setNative_banner_income("0");
                ua.setNative_plaque_income("0");
                ua.setNative_splash_income("0");
                ua.setPlaque_video_income("0");
                ua.setNative_msg_income("0");
                //2021-08-26 添加
                ua.setSystem_splash_income("0");
                ua.setNative_new_banner_income("0");
                ua.setNative_new_plaque_income("0");
                ua.setSuspend_icon_income("0");
                ua.setBanner_ecpm("0");
                ua.setPlaque_ecpm("0");
                ua.setSplash_ecpm("0");
                ua.setVideo_ecpm("0");
                ua.setNative_banner_ecpm("0");
                ua.setNative_plaque_ecpm("0");
                ua.setNative_splash_ecpm("0");
                ua.setPlaque_video_ecpm("0");
                ua.setNative_msg_ecpm("0");
                //2021-08-26 添加
                ua.setSystem_splash_ecpm("0");
                ua.setNative_new_banner_ecpm("0");
                ua.setNative_new_plaque_ecpm("0");
                ua.setSuspend_icon_ecpm("0");
                ua.setBanner_click("0");
                ua.setPlaque_click("0");
                ua.setSplash_click("0");
                ua.setVideo_click("0");
                ua.setNative_banner_click("0");
                ua.setNative_plaque_click("0");
                ua.setNative_splash_click("0");
                ua.setPlaque_video_click("0");
                ua.setNative_msg_click("0");
                //2021-08-26 添加
                ua.setSystem_splash_click("0");
                ua.setNative_new_banner_click("0");
                ua.setNative_new_plaque_click("0");
                ua.setSuspend_icon_click("0");

				//2024-10-08 添加 请求量和填充量
				ua.setBanner_request("0");
				ua.setPlaque_request("0");
				ua.setSplash_request("0");
				ua.setVideo_request("0");
				ua.setNative_banner_request("0");
				ua.setNative_plaque_request("0");
				ua.setNative_splash_request("0");
				ua.setPlaque_video_request("0");
				ua.setNative_msg_request("0");
				ua.setSystem_splash_request("0");
				ua.setNative_new_plaque_request("0");
				ua.setNative_new_banner_request("0");
				ua.setSuspend_icon_request("0");

				ua.setBanner_fill("0");
				ua.setPlaque_fill("0");
				ua.setSplash_fill("0");
				ua.setVideo_fill("0");
				ua.setNative_banner_fill("0");
				ua.setNative_plaque_fill("0");
				ua.setNative_splash_fill("0");
				ua.setPlaque_video_fill("0");
				ua.setNative_msg_fill("0");
				ua.setSystem_splash_fill("0");
				ua.setNative_new_plaque_fill("0");
				ua.setNative_new_banner_fill("0");
				ua.setSuspend_icon_fill("0");
            }

            // ***这里相同的一个应用数据，show和income数值需要叠加，并且ecpm需要自己计算***
            BigDecimal k = new BigDecimal(1000);
            if("banner".equals(adtype)){
                ua.setBanner_show(new BigDecimal(ua.getBanner_show())
                    .add(new BigDecimal(vals[1])).toString());
                ua.setBanner_income(new BigDecimal(ua.getBanner_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setBanner_click(new BigDecimal(ua.getBanner_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setBanner_request(new BigDecimal(ua.getBanner_request()).add(new BigDecimal(vals[4])).toString());
				ua.setBanner_fill(new BigDecimal(ua.getBanner_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getBanner_show()) != 0
                        && Double.valueOf(ua.getBanner_income()) != 0){
                    ua.setBanner_ecpm(
                        new BigDecimal(ua.getBanner_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getBanner_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }
            }else if("plaque".equals(adtype)){
                ua.setPlaque_show(new BigDecimal(ua.getPlaque_show())
                    .add(new BigDecimal(vals[1])).toString());
                ua.setPlaque_income(new BigDecimal(ua.getPlaque_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setPlaque_click(new BigDecimal(ua.getPlaque_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setPlaque_request(new BigDecimal(ua.getPlaque_request()).add(new BigDecimal(vals[4])).toString());
				ua.setPlaque_fill(new BigDecimal(ua.getPlaque_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getPlaque_show()) != 0
                        && Double.valueOf(ua.getPlaque_income()) != 0){

                    ua.setPlaque_ecpm(
                        new BigDecimal(ua.getPlaque_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getPlaque_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }

            }else if("splash".equals(adtype)){
                ua.setSplash_show(new BigDecimal(ua.getSplash_show())
                    .add(new BigDecimal(vals[1])).toString());
                ua.setSplash_income(new BigDecimal(ua.getSplash_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setSplash_click(new BigDecimal(ua.getSplash_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setSplash_request(new BigDecimal(ua.getSplash_request()).add(new BigDecimal(vals[4])).toString());
				ua.setSplash_fill(new BigDecimal(ua.getSplash_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getSplash_show()) != 0
                        && Double.valueOf(ua.getSplash_income()) != 0){

                    ua.setSplash_ecpm(
                        new BigDecimal(ua.getSplash_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getSplash_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }
            }else if("video".equals(adtype)){
                ua.setVideo_show(new BigDecimal(ua.getVideo_show())
                    .add(new BigDecimal(vals[1])).toString());
                ua.setVideo_income(new BigDecimal(ua.getVideo_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setVideo_click(new BigDecimal(ua.getVideo_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setVideo_request(new BigDecimal(ua.getVideo_request()).add(new BigDecimal(vals[4])).toString());
				ua.setVideo_fill(new BigDecimal(ua.getVideo_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getVideo_show()) != 0
                        && Double.valueOf(ua.getVideo_income()) != 0){

                    ua.setVideo_ecpm(
                        new BigDecimal(ua.getVideo_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getVideo_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }
            }else if("nativebanner".equals(adtype)){
                ua.setNative_banner_show(new BigDecimal(ua.getNative_banner_show())
                    .add(new BigDecimal(vals[1])).toString());
                ua.setNative_banner_income(new BigDecimal(ua.getNative_banner_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setNative_banner_click(new BigDecimal(ua.getNative_banner_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setNative_banner_request(new BigDecimal(ua.getNative_banner_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_banner_fill(new BigDecimal(ua.getNative_banner_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getNative_banner_show()) != 0
                        && Double.valueOf(ua.getNative_banner_income()) != 0){

                    ua.setNative_banner_ecpm(
                        new BigDecimal(ua.getNative_banner_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getNative_banner_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }
            }else if("nativeplaque".equals(adtype)){
                ua.setNative_plaque_show(new BigDecimal(ua.getNative_plaque_show())
                    .add(new BigDecimal(vals[1])).toString());
                ua.setNative_plaque_income(new BigDecimal(ua.getNative_plaque_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setNative_plaque_click(new BigDecimal(ua.getNative_plaque_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setNative_plaque_request(new BigDecimal(ua.getNative_plaque_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_plaque_fill(new BigDecimal(ua.getNative_plaque_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getNative_plaque_show()) != 0
                        && Double.valueOf(ua.getNative_plaque_income()) != 0){

                    ua.setNative_plaque_ecpm(
                        new BigDecimal(ua.getNative_plaque_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getNative_plaque_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }
            }else if("nativesplash".equals(adtype)){
                ua.setNative_splash_show(new BigDecimal(ua.getNative_splash_show())
                    .add(new BigDecimal(vals[1])).toString());
                ua.setNative_splash_income(new BigDecimal(ua.getNative_splash_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setNative_splash_click(new BigDecimal(ua.getNative_splash_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setNative_splash_request(new BigDecimal(ua.getNative_splash_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_splash_fill(new BigDecimal(ua.getNative_splash_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getNative_splash_show()) != 0
                        && Double.valueOf(ua.getNative_splash_income()) != 0){

                    ua.setNative_splash_ecpm(
                        new BigDecimal(ua.getNative_splash_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getNative_splash_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }
            }else if("plaquevideo".equals(adtype)){
                ua.setPlaque_video_show(new BigDecimal(ua.getPlaque_video_show())
                        .add(new BigDecimal(vals[1])).toString());
                ua.setPlaque_video_income(new BigDecimal(ua.getPlaque_video_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setPlaque_video_click(new BigDecimal(ua.getPlaque_video_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setPlaque_video_request(new BigDecimal(ua.getPlaque_video_request()).add(new BigDecimal(vals[4])).toString());
				ua.setPlaque_video_fill(new BigDecimal(ua.getPlaque_video_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getPlaque_video_show()) != 0
                        && Double.valueOf(ua.getPlaque_video_income()) != 0){

                    ua.setPlaque_video_ecpm(
                        new BigDecimal(ua.getPlaque_video_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getPlaque_video_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }

            }else if("nativemsg".equals(adtype)){
                ua.setNative_msg_show(new BigDecimal(ua.getNative_msg_show())
                        .add(new BigDecimal(vals[1])).toString());
                ua.setNative_msg_income(new BigDecimal(ua.getNative_msg_income())
                    .add(new BigDecimal(vals[2])).toString());
                ua.setNative_msg_click(new BigDecimal(ua.getNative_msg_click())
                    .add(new BigDecimal(vals[3])).toString());
				ua.setNative_msg_request(new BigDecimal(ua.getNative_msg_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_msg_fill(new BigDecimal(ua.getNative_msg_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getNative_msg_show()) != 0
                        && Double.valueOf(ua.getNative_msg_income()) != 0){

                    ua.setNative_msg_ecpm(
                        new BigDecimal(ua.getNative_msg_income())
                        .multiply(k)
                        .divide(new BigDecimal(ua.getNative_msg_show()), 2, RoundingMode.FLOOR)
                        .toString()
                    );
                }
            }
            //2021-08-26 添加
            else if("systemsplash".equals(adtype)){
                ua.setSystem_splash_show(new BigDecimal(ua.getSystem_splash_show())
                        .add(new BigDecimal(vals[1])).toString());
                ua.setSystem_splash_income(new BigDecimal(ua.getSystem_splash_income())
                        .add(new BigDecimal(vals[2])).toString());
                ua.setSystem_splash_click(new BigDecimal(ua.getSystem_splash_click())
                        .add(new BigDecimal(vals[3])).toString());
				ua.setSystem_splash_request(new BigDecimal(ua.getSystem_splash_request()).add(new BigDecimal(vals[4])).toString());
				ua.setSystem_splash_fill(new BigDecimal(ua.getSystem_splash_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getSystem_splash_show()) != 0
                        && Double.valueOf(ua.getSystem_splash_income()) != 0){

                    ua.setSystem_splash_ecpm(
                            new BigDecimal(ua.getSystem_splash_income())
                                    .multiply(k)
                                    .divide(new BigDecimal(ua.getSystem_splash_show()), 2, RoundingMode.FLOOR)
                                    .toString()
                    );
                }
            }else if("nativenewplaque".equals(adtype)){
                ua.setNative_new_plaque_show(new BigDecimal(ua.getNative_new_plaque_show())
                        .add(new BigDecimal(vals[1])).toString());
                ua.setNative_new_plaque_income(new BigDecimal(ua.getNative_new_plaque_income())
                        .add(new BigDecimal(vals[2])).toString());
                ua.setNative_new_plaque_click(new BigDecimal(ua.getNative_new_plaque_click())
                        .add(new BigDecimal(vals[3])).toString());
				ua.setNative_new_plaque_request(new BigDecimal(ua.getNative_new_plaque_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_new_plaque_fill(new BigDecimal(ua.getNative_new_plaque_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getNative_new_plaque_show()) != 0
                        && Double.valueOf(ua.getNative_new_plaque_income()) != 0){

                    ua.setNative_new_plaque_ecpm(
                            new BigDecimal(ua.getNative_new_plaque_income())
                                    .multiply(k)
                                    .divide(new BigDecimal(ua.getNative_new_plaque_show()), 2, RoundingMode.FLOOR)
                                    .toString()
                    );
                }
            }else if("nativenewbanner".equals(adtype)){
                ua.setNative_new_banner_show(new BigDecimal(ua.getNative_new_banner_show())
                        .add(new BigDecimal(vals[1])).toString());
                ua.setNative_new_banner_income(new BigDecimal(ua.getNative_new_banner_income())
                        .add(new BigDecimal(vals[2])).toString());
                ua.setNative_new_banner_click(new BigDecimal(ua.getNative_new_banner_click())
                        .add(new BigDecimal(vals[3])).toString());
				ua.setNative_new_banner_request(new BigDecimal(ua.getNative_new_banner_request()).add(new BigDecimal(vals[4])).toString());
				ua.setNative_new_banner_fill(new BigDecimal(ua.getNative_new_banner_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getNative_new_banner_show()) != 0
                        && Double.valueOf(ua.getNative_new_banner_income()) != 0){

                    ua.setNative_new_banner_ecpm(
                            new BigDecimal(ua.getNative_new_banner_income())
                                    .multiply(k)
                                    .divide(new BigDecimal(ua.getNative_new_banner_show()), 2, RoundingMode.FLOOR)
                                    .toString()
                    );
                }
            } else if("suspendicon".equals(adtype)){
                ua.setSuspend_icon_show(new BigDecimal(ua.getSuspend_icon_show())
                        .add(new BigDecimal(vals[1])).toString());
                ua.setSuspend_icon_income(new BigDecimal(ua.getSuspend_icon_income())
                        .add(new BigDecimal(vals[2])).toString());
                ua.setSuspend_icon_click(new BigDecimal(ua.getSuspend_icon_click())
                        .add(new BigDecimal(vals[3])).toString());
				ua.setSuspend_icon_request(new BigDecimal(ua.getSuspend_icon_request()).add(new BigDecimal(vals[4])).toString());
				ua.setSuspend_icon_fill(new BigDecimal(ua.getSuspend_icon_fill()).add(new BigDecimal(vals[5])).toString());
                if(Integer.valueOf(ua.getSuspend_icon_show()) != 0
                        && Double.valueOf(ua.getSuspend_icon_income()) != 0){

                    ua.setSuspend_icon_ecpm(
                            new BigDecimal(ua.getSuspend_icon_income())
                                    .multiply(k)
                                    .divide(new BigDecimal(ua.getSuspend_icon_show()), 2, RoundingMode.FLOOR)
                                    .toString()
                    );
                }
            }
            valMap.put(appkey, ua);
         }
    }

}
