package com.wbgame.service;

import java.util.List;
import java.util.Map;


/**
 * 合作方收支相关接口
 * <AUTHOR>
 */
public interface PartnerService {
	
	public <T> List<T> queryListBean(String sql, Class<T> clazz);
	
	// 合作方投放明细查询-新
	public List<Map<String,Object>> selectPartnerInvestInfoNew(Map<String, String> paramMap);
	public Map<String,Object> selectPartnerInvestInfoNewSum(Map<String, String> paramMap);
	// 合作方变现明细查询-新
	public List<Map<String,Object>> selectPartnerRevenueInfoNew(Map<String, String> paramMap);
	public Map<String,Object> selectPartnerRevenueInfoNewSum(Map<String, String> paramMap);
	// 合作产品收支稽核查询
	public List<Map<String,Object>> selectPartnerAppRevenueNew(Map<String, String> paramMap);
	public Map<String,Object> selectPartnerAppRevenueNewSum(Map<String, String> paramMap);
	
	// 同步拉取投放明细查询-新
	public boolean syncPartnerInvestInfoNew(String tdate);
	// 同步拉取变现明细查询-新
	public boolean syncPartnerRevenueInfoNew(String tdate);
	// 同步拉取新增活跃支付收入查询-新
	public boolean syncPartnerAppBillingNew(String tdate);
	
	// 最终审核数据写入到合作产品收支查询
	public boolean checkPartnerAppCostTotal(Map<String, String> paramMap);
	public boolean updatePartnerAppCostBilling(Map<String, String> paramMap);


	// ===============================================================
	// Overseas Methods (海外版方法)
	// ===============================================================

	// 国家地区Map集合
	public Map<String, Map<String, Object>> selectDimCountryMap();
	// 合作方投放明细查询-海外版
	public List<Map<String,Object>> selectPartnerInvestInfoNewOverseas(Map<String, String> paramMap);
	public Map<String,Object> selectPartnerInvestInfoNewSumOverseas(Map<String, String> paramMap);
	// 合作方变现明细查询-海外版
	public List<Map<String,Object>> selectPartnerRevenueInfoNewOverseas(Map<String, String> paramMap);
	public Map<String,Object> selectPartnerRevenueInfoNewSumOverseas(Map<String, String> paramMap);
	// 合作产品收支稽核查询-海外版
	public List<Map<String,Object>> selectPartnerAppRevenueNewOverseas(Map<String, String> paramMap);
	public Map<String,Object> selectPartnerAppRevenueNewSumOverseas(Map<String, String> paramMap);

	// 同步拉取投放明细查询-海外版
	public boolean syncPartnerInvestInfoNewOverseas(String tdate, Map<String, String> contextParams);
	// 同步拉取变现明细查询-海外版
	public boolean syncPartnerRevenueInfoNewOverseas(String tdate, Map<String, String> contextParams);
	// 同步拉取新增活跃支付收入查询-海外版 (注意参数类型变化)
	public boolean syncPartnerAppBillingNewOverseas(String tdate, Map<String, String> contextParams);

	// 最终审核数据写入到合作产品收支查询-海外版
	public boolean checkPartnerAppCostTotalOverseas(Map<String, String> paramMap);
	public boolean updatePartnerAppCostBillingOverseas(Map<String, String> paramMap);
	


	/**
	 * 分摊核减金额到指定月份的每一天
	 * @param paramMap 包含月份(month)、产品ID(appid)和核减金额(reduce_amount)
	 * @return 操作是否成功
	 */
	boolean distributeReduceAmount(Map<String, String> paramMap);

	/**
	 * 将核减后的付费收入同步到稽核页面
	 * @param paramMap 包含月份(month)、产品ID(appid)
	 * @return 操作是否成功
	 */
	boolean syncReducedPayToAudit(Map<String, String> paramMap);

	/**
	 * 修改单条记录的核减金额
	 * @param paramMap 包含日期(tdate)、产品ID(appid)和核减后金额(reduce_pay_revenue)
	 * @return 操作是否成功
	 */
	boolean updateReduceAmount(Map<String, String> paramMap);

	/**
	 * 查询合作产品收入与核减信息
	 * @param paramMap 查询参数
	 * @return 查询结果列表
	 */
	List<Map<String, Object>> selectPartnerAppRevenueWithReduce(Map<String, String> paramMap);

	/**
	 * 查询合作产品收入与核减信息汇总
	 * @param paramMap 查询参数
	 * @return 汇总数据
	 */
	Map<String, Object> selectPartnerAppRevenueWithReduceSum(Map<String, String> paramMap);
	
}
