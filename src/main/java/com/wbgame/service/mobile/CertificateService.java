package com.wbgame.service.mobile;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.mobile.CertificateDTO;
import com.wbgame.pojo.mobile.CertificateRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/26 20:25
 */
public interface CertificateService {

    Result<String> createCertificate(CertificateRepository certificate);

    Result<List<CertificateRepository>> queryCertificates(CertificateDTO dto);
    CertificateRepository queryById(String id);

    Result<String> updateCertificate(CertificateRepository certificate);

    Result<String> deleteCertificate(Integer id);

    /**
     * 上传证书并更新关联的域名的任务证书
     *
     * @param certificate 证书信息
     * @return 更新部署结果
     */
    Result<Long> handleAfterApply(String certificate);

    /**
     * 上传证书并更新关联的域名的任务证书
     *
     * @param certificate 证书信息
     * @return 更新部署结果
     */
    Result<Long> uploadCertificate(CertificateRepository certificate);


    /**
     * 上传证书并更新关联的域名的任务证书
     *
     * @param id 证书库管理数据唯一标识
     * @return 更新部署结果
     */
    Result<Long> uploadCertificate(String id);

    Result<Long> updateCertificateStatus(String id, String status,String failMsg);
}
