package com.wbgame.service.mobile.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.cas20200407.models.UploadUserCertificateResponseBody;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.enums.CertificateStatusEnum;
import com.wbgame.mapper.master.mobile.CertificateMapper;
import com.wbgame.pojo.mobile.CertificateConfig;
import com.wbgame.pojo.mobile.CertificateDTO;
import com.wbgame.pojo.mobile.CertificateRepository;
import com.wbgame.service.mobile.CertificateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/26 20:25
 */
@Service
@Slf4j
public class CertificateServiceImpl implements CertificateService {

    @Autowired
    private CertificateMapper certificateMapper;


    @Override
    public Result<String> createCertificate(CertificateRepository certificate) {
        //当前操作人
        String username = LOGIN_USER_NAME.get();
        certificate.setCreate_user(username);
        int insert = certificateMapper.insert(certificate);
        return insert > 0 ? ResultUtils.success() : ResultUtils.failure();
    }

    @Override
    public Result<List<CertificateRepository>> queryCertificates(CertificateDTO dto) {
        PageHelper.startPage(dto.getStart(), dto.getLimit());
        List<CertificateRepository> certificateList = certificateMapper.queryList(dto);
        PageInfo<CertificateRepository> pageInfo = new PageInfo<>(certificateList);
        return ResultUtils.success(Constants.OK, pageInfo.getList(), null, pageInfo.getTotal());
    }

    @Override
    public CertificateRepository queryById(String id) {
        return certificateMapper.queryById(id);
    }

    @Override
    public Result<String> updateCertificate(CertificateRepository certificate) {
        Integer id = certificate.getId();
        if (ObjectUtils.isEmpty(id)) {
            return ResultUtils.failure(Constants.isNotNull);
        }
        //当前操作人
        String username = LOGIN_USER_NAME.get();
        certificate.setUpdate_user(username);
        int update = certificateMapper.update(certificate);
        return update > 0 ? ResultUtils.success() : ResultUtils.failure();
    }

    @Override
    public Result<String> deleteCertificate(Integer id) {
        int delete = certificateMapper.delete(id);
        return delete > 0 ? ResultUtils.success() : ResultUtils.failure();
    }


    /**
     * 申请完证书后对证书上传操作
     *
     * @param certificate 证书信息
     * @return 更新部署结果
     */
    @Override
    public Result<Long> handleAfterApply(String certificate) {
        JSONObject jsonObject = JSON.parseObject(certificate);
        String status = jsonObject.getString("status");
        String id = jsonObject.getString("sys_id");
        CertificateRepository certificateRepository = certificateMapper.queryById(id);
        if (ObjectUtils.isEmpty(certificateRepository)) {
            return ResultUtils.failure("参数异常");
        }
        //复制申请时间
        certificateRepository.setAp_time(Long.toString(System.currentTimeMillis()));
        if (!"success".equals(status)) {
            //申请证书失败：2=申请失败,前端显示[!申请]
            certificateRepository.setUrl_st(CertificateStatusEnum.APPLY_FAIL.value());
            certificateRepository.setFail_msg("证书申请失败:" + certificate);
            certificateMapper.update(certificateRepository);
            return ResultUtils.failure("证书申请失败");
        }
        String downloadUrl = jsonObject.getString("ssl_down_full_url");
        certificateRepository.setDow_ssl(downloadUrl);
        //获取是否为测试模式
        Integer testDns = certificateRepository.getTest_dns();
        if (testDns == null || testDns == 1) {
            //为测试模式：0 = 更新成功,前端显示[续签]
            certificateRepository.setUrl_st(CertificateStatusEnum.SUCCESS.value());
            certificateRepository.setFail_msg("测试模式不进行上传证书操作");
            certificateMapper.update(certificateRepository);
            return ResultUtils.success();
        }
        //判断是否需要上传证书操作
        //宝塔地址为空且类型为阿里飞鸟的需要上传证书操作
        String btFile = certificateRepository.getBt_file();
        String btSend = certificateRepository.getBt_send();
        if (!StringUtils.isEmpty(btFile) || !StringUtils.isEmpty(btSend)) {
            //存在不为空，不进行上传证书操作
            //为测试模式：0 = 更新成功,前端显示[续签]
            certificateRepository.setUrl_st(CertificateStatusEnum.SUCCESS.value());
            certificateRepository.setFail_msg("宝塔地址不为空不进行上传证书操作");
            certificateMapper.update(certificateRepository);
            return ResultUtils.success();
        }
        //获取部署账号
        String dnsUser = certificateRepository.getDns_user();
        if (StringUtils.isEmpty(dnsUser)) {
            // 没有配置账户，也不进行上传操作
            certificateRepository.setUrl_st(CertificateStatusEnum.UPLOAD_FAIL.value());
            certificateRepository.setFail_msg("证书上传异常：未配置部署账号");
            certificateMapper.update(certificateRepository);
            return ResultUtils.failure("证书上传异常：未配置部署账号");
        }
        Result<Long> uploaded = uploadCertificate(certificateRepository);

        if (Constants.OK.getRet() == uploaded.getRet()) {
            //上传成功
            certificateRepository.setUrl_st(CertificateStatusEnum.SUCCESS.value());
            certificateRepository.setFail_msg("证书申请并上传成功");
            certificateRepository.setLatest_cert_id(uploaded.getData());
        } else {
            //上传失败
            log.error("证书上传失败，失败原因:" + uploaded.getMsg());
            certificateRepository.setFail_msg("证书上传失败:" + uploaded.getMsg());
            certificateRepository.setUrl_st(CertificateStatusEnum.UPLOAD_FAIL.value());
        }
        certificateMapper.update(certificateRepository);
        return uploaded;
    }


    /**
     * 上传证书
     *
     * @param id 证书库管理数据唯一标识
     * @return 更新部署结果
     */
    @Override
    public Result<Long> uploadCertificate(String id) {
        CertificateRepository certificate = certificateMapper.queryById(id);
        if (ObjectUtils.isEmpty(certificate)) {
            return ResultUtils.failure(Constants.ParamError);
        }
        Result<Long> uploadedResult = uploadCertificate(certificate);
        if (Constants.OK.getRet() == uploadedResult.getRet()) {
            //上传成功
            certificate.setLatest_cert_id(uploadedResult.getData());
            certificate.setUrl_st(CertificateStatusEnum.SUCCESS.value());
            certificate.setFail_msg("证书上传成功");
        } else {
            //上传失败
            certificate.setUrl_st(CertificateStatusEnum.UPLOAD_FAIL.value());
            certificate.setFail_msg("证书上传失败:" + uploadedResult.getMsg());
        }
        certificateMapper.update(certificate);
        return uploadedResult;
    }

    /**
     * 上传更新证书阿里云
     *
     * @param certificate 证书信息
     * @return 上传更新结果
     */
    @Override
    public Result<Long> uploadCertificate(CertificateRepository certificate) {
        Path certificateZipPath = null;
        try {
            //上传证书账号配置查询
            CertificateConfig config = certificateMapper.queryCertificateConfig(certificate.getDns_user());
            if (ObjectUtils.isEmpty(config)) {
                return ResultUtils.failure("证书上传账号配置异常");
            }
            //1.获取申请的证书
            String dowSsl = certificate.getDow_ssl();
            if (StringUtils.isEmpty(dowSsl)) {
                return ResultUtils.failure("证书下载地址为空");
            }
            //主域名
            String domain = certificate.getName();
            if (StringUtils.isEmpty(domain)) {
                return ResultUtils.failure("主域名地址为空");
            }
            // 1. 下载ZIP文件到临时目录
            certificateZipPath = downloadZipFile(dowSsl);
            // 2. 解压ZIP文件并查找目标域名证书
            Map<String, String> certificateContent = extractCertificate(certificateZipPath, domain);
            // 获取证书内容
            String cert = certificateContent.get("cert");
            String privateKey = certificateContent.get("key");
            //2.上传证书至aliyun
            UploadUserCertificateResponseBody uploadedResp = AliyunCertificateService.uploadUserCertificate(config,domain, cert, privateKey);
            log.info("上传证书返回参数：{}", JSON.toJSONString(uploadedResp));
            Long certId = uploadedResp.getCertId();
            return ResultUtils.success(certId);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtils.failure("证书上传异常," + e.getMessage());
        } finally {
            //删除下载的证书文件
            if (certificateZipPath != null) {
                certificateZipPath.toFile().deleteOnExit();
            }
        }
    }

    @Override
    public Result<Long> updateCertificateStatus(String id, String status,String failMsg) {
        if (StringUtils.isEmpty(id)) {
            //异常数据，不进行更新数据操作
            return ResultUtils.failure(Constants.ParamError);
        }
        certificateMapper.updateStatus(id, status,failMsg);
        return ResultUtils.success();
    }

    /**
     * 根据zip文件获取证书内容信息
     *
     * @param zipPath      zip文件
     * @param targetDomain 目标域名
     * @return 获取域名证书内容结果
     * @throws IOException
     */
    private Map<String, String> extractCertificate(Path zipPath, String targetDomain) throws IOException {
        Map<String, String> certificateContent = new HashMap<>();
        //域名通配符"*"替换
        targetDomain = targetDomain.toLowerCase().replace("*.", "");
        try (ZipFile zipFile = new ZipFile(zipPath.toFile())) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                String entryName = entry.getName().toLowerCase();
                // 检查是否是证书文件（通常为.crt或.pem扩展名）且包含目标域名
                if ("fullchain.pem".equals(entryName)) {
                    // 读取证书内容
                    try (BufferedInputStream bis = new BufferedInputStream(zipFile.getInputStream(entry));
                         ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = bis.read(buffer)) > 0) {
                            bos.write(buffer, 0, len);
                        }
                        String cert = bos.toString("UTF-8");
                        certificateContent.put("cert", cert);
                    }
                }
                // 检查是否是证书文件（通常为.crt或.pem扩展名）且包含目标域名
                if (entryName.endsWith(".key") && entryName.contains(targetDomain)) {
                    // 读取证书内容
                    try (BufferedInputStream bis = new BufferedInputStream(zipFile.getInputStream(entry));
                         ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = bis.read(buffer)) > 0) {
                            bos.write(buffer, 0, len);
                        }
                        String cert = bos.toString("UTF-8");
                        certificateContent.put("key", cert);
                    }
                }
            }
            if (certificateContent.containsKey("cert") && certificateContent.containsKey("key")) {
                return certificateContent;
            }
        }
        throw new IOException("No certificate found for domain: " + targetDomain);
    }


    /**
     * 根据传入的下载地址获取zip文件
     *
     * @param zipUrl zip下载地址
     * @return
     * @throws IOException
     */
    private static Path downloadZipFile(String zipUrl) throws IOException {
        URL url = new URL(zipUrl);
        Path tempFile = Files.createTempFile("certificate", ".zip");
        try (BufferedInputStream in = new BufferedInputStream(url.openStream())) {
            Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
        }
        return tempFile;
    }

}
