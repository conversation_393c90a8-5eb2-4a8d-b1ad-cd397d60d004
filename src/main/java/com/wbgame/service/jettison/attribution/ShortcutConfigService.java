package com.wbgame.service.jettison.attribution;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.mapper.tfxt.AdtMapper;
import com.wbgame.pojo.jettison.vo.ShortcutConfig;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import static com.wbgame.common.ThreadLocalConstants.LOGIN_USER_NAME;

/**
 * <AUTHOR>
 * @date 2024/8/6
 * @description
 **/
@Service
public class ShortcutConfigService {

    @Resource
    private AdtMapper adtMapper;

    private static final String DEFAULT_CALLBACK_URL = "https://77pin.net/attribution/${shortcut}?idfa=__IDFA__&imei=__IMEI__&androidid=__ANDROIDID__&oaid=__OAID__&caid=__CAID__&ip=__IP__&ts=__TS__&os=__OS__&callback_param=__CALLBACK_PARAM__&callback=__CALLBACK_URL__&csite=__CSITE__&ctype=__CTYPE__&siteid=__UNION_SITE__&adgroup_id=__CAMPAIGN_ID__&adgroup_name=__CAMPAIGN_NAME__&adplan_id=__AID__&adplan_name=__AID_NAME__&adcreative_id=__CID__&adcreative_name=__CID_NAME__&accountid=__ADVERTISER_ID__&convert=__CONVERT_ID__&requestid=__REQUEST_ID__&model=__MODEL__&advert_id=__PROMOTION_ID__&advert_name=__PROMOTION_NAME__&project_id=__PROJECT_ID__&project_name=__PROJECT_NAME__&mid1=__MID1__&mid2=__MID2__&mid3=__MID3__&mid4=__MID4__&mid5=__MID5__&mid6=__MID6__";
    private static final String MIX_HEADLINE_CALLBACK_URL = "https://77pin.net/attribution/mix/${shortcut}?idfa=__IDFA__&imei=__IMEI__&androidid=__ANDROIDID__&oaid=__OAID__&caid=__CAID__&ip=__IP__&ts=__TS__&os=__OS__&callback_param=__CALLBACK_PARAM__&callback=__CALLBACK_URL__&csite=__CSITE__&ctype=__CTYPE__&siteid=__UNION_SITE__&adgroup_id=__CAMPAIGN_ID__&adgroup_name=__CAMPAIGN_NAME__&adplan_id=__AID__&adplan_name=__AID_NAME__&adcreative_id=__CID__&adcreative_name=__CID_NAME__&accountid=__ADVERTISER_ID__&convert=__CONVERT_ID__&requestid=__REQUEST_ID__&model=__MODEL__&advert_id=__PROMOTION_ID__&advert_name=__PROMOTION_NAME__&project_id=__PROJECT_ID__&project_name=__PROJECT_NAME__&mid1=__MID1__&mid2=__MID2__&mid3=__MID3__&mid4=__MID4__&mid5=__MID5__&mid6=__MID6__";
    private static final String MIX_KS_CALLBACK_URL = "https://77pin.net/attribution/mix/${shortcut}?accountid=__ACCOUNTID__&adgroup_id=__AID__&adplan_id=__DID__&adcreative_id=__CID__&adgroup_name=__UNITNAME__&adplan_name=__DNAME__&mid1=__PHOTOID__&oaid=__OAID2__&imei=__IMEI2__&idfa=__IDFA2__&ts=__TS__&ua=__UA__&os=__OS__&model=__MODEL__&callback=__CALLBACK__&ip=__IPV4__&csite=__CSITE__";
    private static final String MIX_OPPO_UNION_CALLBACK_URL = "https://77pin.net/attribution/mix/${shortcut}?imei=$im$&oaid=__OAID__&adplan_id=$ad$&adplan_name=$an$&adgroup_id=$planid$&accountid=$ownerid$&os=0&pn=$pkg$&ip=__IP__&ts=__TS__&ua=$ua$&model=$m$&requestid=$req$";
    private static final String MIX_OPPO_STORE_CALLBACK_URL = "https://77pin.net/attribution/mix/${shortcut}?adplan_id=__ADID__&imei=__IMEI__&oaid=__OAID__&ts=__TS__&ip=__IP__&androidid=__ANDROIDID__&os=0";
    private static final String MIX_VIVO_CALLBACK_URL = "https://77pin.net/attribution/mix/${shortcut}?os=__OS__&imei=__IMEI__&oaid=__OAIDPLAIN__&androidid=__ANDROIDID__&ip=__IP__&ua=__UA__&ts=__TS__&accountid=__ADVERTISERID__&ry_adcreative_id=__CREATIVEID__&ry_adplan_id=__GROUPID__&ry_adgroup_id=__CAMPAIGNID__&csite=__ADID__&ctype=__PLACETYPE__&requestid=__REQUESTID__";
    private static final String MIX_HUAWEI_ADS_CALLBACK_URL = "https://77pin.net/attribution/hwads/${shortcut}?isredirect=true";
    private static final String MIX_HONOR_CALLBACK_URL = "https://77pin.net/attribution/homix/{shortcut}?oaid=__OAID__&ip=__IP__&os=0&ts=__TIME__&ua=__UA__&callback=__TRACK_ID__&accountid=__ADVERTISER_ID__&requestid=__REQUESTID__&adgroup_id=__CAMPAIGNID__&adplan_id=__GROUPID__&adcreative_id=__CREATIVE_ID__&csite=__AD_PLACEMENT_ID__";

    private static final String[] random_str = new String[]{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l",
            "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G",
            "H", "I", "J", "K", "L", "M", "N","O", "P", "Q", "R", "S",
            "T", "U", "V", "W", "X", "Y", "Z"};
    // , "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
    Random random = new Random();

    public HashMap<String, Object> getShortcutConfigList(String appid, String specialName, Integer enabled, int start, int limit){
        HashMap<String, Object> map = new HashMap<>();
        PageHelper.startPage(start, limit);
        List<ShortcutConfig> shortcutConfigList = adtMapper.getShortcutConfigList(appid, specialName, enabled);
        PageInfo<ShortcutConfig> pageInfo = new PageInfo<>(shortcutConfigList);
        for (ShortcutConfig shortcutConfig : shortcutConfigList) {
            String shortcut = shortcutConfig.getShortcut();
            shortcutConfig.setUrl(DEFAULT_CALLBACK_URL.replace("${shortcut}", shortcut));
        }
        map.put("list",shortcutConfigList);
        map.put("totalSize",pageInfo.getTotal());
        return map;
    }

    public String addShortcutConfig(ShortcutConfig shortcutConfig) {
        String shortcut = shortcutBuilder();
        while (adtMapper.isShortcutDefined(shortcut) > 0) {
            shortcut = shortcutBuilder();
        }

        shortcutConfig.setShortcut(shortcut);
        String userName = LOGIN_USER_NAME.get();
        shortcutConfig.setCreate_user(userName);
        shortcutConfig.setUpdate_user(userName);
        adtMapper.insertShortcutConfig(shortcutConfig);

        return null;
    }

    @NotNull
    public String shortcutBuilder() {
        StringBuilder shortcutSb = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            int rand = random.nextInt(random_str.length);
            shortcutSb.append(random_str[rand]);
        }
        String shortcut = shortcutSb.toString();
        return shortcut;
    }

    public void updateShortcutConfig(ShortcutConfig shortcutConfig) {
        String userName = LOGIN_USER_NAME.get();
        shortcutConfig.setUpdate_user(userName);

        adtMapper.updateShortcutConfig(shortcutConfig);
    }
}
