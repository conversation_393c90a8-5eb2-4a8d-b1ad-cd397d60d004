package com.wbgame.service.game.impl;

import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.adb.GiftMapper;
import com.wbgame.mapper.master.TokenManageMapper;
import com.wbgame.mapper.adb.CurrencyMapper;
import com.wbgame.pojo.game.*;
import com.wbgame.pojo.game.report.MGCurrencyReportVo;
import com.wbgame.pojo.game.report.PayConversionVo;
import com.wbgame.pojo.game.report.PayDailyVo;
import com.wbgame.pojo.game.report.query.MGCurrencyQueryVo;
import com.wbgame.pojo.game.report.query.PayConversionQueryVo;
import com.wbgame.pojo.game.report.query.PayDailyQueryVo;
import com.wbgame.pojo.operate.TokenManageVo;
import com.wbgame.service.game.GiftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname GiftServiceImpl
 * @Description TODO
 * @Date 2022/3/9 15:18
 */
@Service
public class GiftServiceImpl implements GiftService {

    @Autowired
    GiftMapper giftMapper;

    @Autowired
    CurrencyMapper currencyMapper;

    @Autowired
    private TokenManageMapper tokenManageMapper;


    @Override
    public List<GiftInfoVo> getGiftInfoList(GiftInfoVo vo) {
        return giftMapper.getGiftInfoList(vo);
    }

    @Override
    public int saveGiftInfo(GiftInfoVo vo) {
        return giftMapper.saveGiftInfo(vo);
    }

    @Override
    public int batchSaveGiftInfo(List<GiftInfoVo> list) {
        return giftMapper.batchSaveGiftInfo(list);
    }

    @Override
    public int updateGiftInfo(GiftInfoVo vo) {
        return giftMapper.updateGiftInfo(vo);
    }

    @Override
    public int delGiftInfo(GiftInfoVo vo) {
        return giftMapper.delGiftInfo(vo);
    }

    @Override
    public List<GiftAnalysisVo> getGiftAnalysisList(Map<String, String> map) {
        return giftMapper.getGiftAnalysisList(map);
    }
    @Override
    public  List<Map<String,Object>>   getLineChart(Map<String, String> map) {
        return giftMapper.getLineChart(map);
    }
    @Override
    public GiftAnalysisVo getGiftAnalysisSum(Map<String, String> map) {
        return giftMapper.getGiftAnalysisSum(map);
    }

    @Override
    public List<GiftAnalysisVo> getGiftAnalysisUserList(Map<String, String> map) {
        return giftMapper.getGiftAnalysisUserList(map);
    }

    @Override
    public List<GiftFirstPayVo> getGiftFirstPayList(Map<String, String> map) {
        return giftMapper.getGiftFirstPayList(map);
    }

    @Override
    public GiftFirstPayVo getGiftFirstPaySum(Map<String, String> map) {
        return giftMapper.getGiftFirstPaySum(map);
    }

    @Override
    public List<ActivityEffectVo> getActivityEffectList(Map<String, String> map) {
        return giftMapper.getActivityEffectList(map);
    }

    @Override
    public ActivityEffectVo getActivityEffectSum(Map<String, String> map) {
        return giftMapper.getActivityEffectSum(map);
    }

    @Override
    public List<PayConversionVo> getPayConversionList(PayConversionQueryVo vo) {
        return giftMapper.getPayConversionList(vo);
    }

    @Override
    public List<PayConversionVo> getPayConversionListChart(PayConversionQueryVo vo) {
        return giftMapper.getPayConversionListChart(vo);
    }

    @Override
    public PayConversionVo getPayConversionSum(PayConversionQueryVo vo) {
        return giftMapper.getPayConversionSum(vo);
    }

    @Override
    public List<MGCurrencyReportVo> getMicGameCurrencyList(MGCurrencyQueryVo vo) {
        return currencyMapper.getMicGameCurrencyList(vo);
    }

    @Override
    public List<MGCurrencyReportVo> getMicGameCurrencyExport(MGCurrencyQueryVo vo) {
        return currencyMapper.getMicGameCurrencyExport(vo);
    }

    @Override
    public List<PayDailyVo> getPayDailyList(PayDailyQueryVo vo) {
        return giftMapper.getPayDailyList(vo);
    }

    @Override
    public PayDailyVo getPayDailySum(PayDailyQueryVo vo) {
        return giftMapper.getPayDailySum(vo);
    }

    @Override
    public Result<Map<String, Map<String, List<Map<String, Object>>>>> selectPackageAnalysisSales(Map<String, String> map) {

        List<Map<String, Object>> list = giftMapper.selectPackageAnalysisSales(map);

        Map<String, Map<String, List<Map<String, Object>>>> mapVO = list
                .stream()
                .collect(Collectors.groupingBy(vo -> String.valueOf(vo.get("buy_date")),
                        Collectors.groupingBy(vo -> (String) vo.get("is_new_user"))));

        return ResultUtils.success(mapVO);
    }

    @Override
    public Result<Map<String,List<GiftFirstPayVo>>> selectFirstPayToNewOldUser(GiftFirstPayDTO giftFirstPayDTO) {

        List<GiftFirstPayVo> list = giftMapper.selectFirstPayToNewOldUser(giftFirstPayDTO);

        return ResultUtils.success(list
                .stream()
                .collect(Collectors.groupingBy(GiftFirstPayVo::getCreate_date)));
    }

    @Override
    public List<TokenManageVo> getTokenList(TokenManageVo vo) {
        List<TokenManageVo> list = tokenManageMapper.tokenList(vo);
        return list;
    }
}
