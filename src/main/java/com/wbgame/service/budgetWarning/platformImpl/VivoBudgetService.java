package com.wbgame.service.budgetWarning.platformImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.common.strategyEnum.Platform;
import com.wbgame.mapper.tfxt.PlatformMapper;
import com.wbgame.pojo.budgetWarning.AccountBudget;
import com.wbgame.pojo.budgetWarning.CampaignBudget;
import com.wbgame.pojo.budgetWarning.GroupBudget;
import com.wbgame.pojo.finance.FinanceReport;
import com.wbgame.pojo.jettison.report.account.VivoAccount;
import com.wbgame.pojo.jettison.report.dto.AccountDTO;
import com.wbgame.pojo.jettison.report.param.AccountParam;
import com.wbgame.service.budgetWarning.BaseBudgetService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/5
 * @description vivo 预算请求\ 处理相关
 **/
@Slf4j
@Service
public class VivoBudgetService extends BaseBudgetService {

    private static final String VIVO_ACCOUNT_BUDGET_URL = "https://marketing-api.vivo.com.cn/openapi/v1/account/fetch";
    private static final String VIVO_ACCOUNT_FINANCE_URL = "https://marketing-api.vivo.com.cn/openapi/v1/finance/funds/queryFounds";
    private static final String VIVO_GROUP_BUDGET_URL = "https://marketing-api.vivo.com.cn/openapi/v1/ad/campaign/pageInfoByLastId";
    private static final String VIVO_CAMPAIGN_BUDGET_URL = "https://marketing-api.vivo.com.cn/openapi/v1/ad/group/pageInfoByLastId";
    public static final int LIMIT = 100;

    private Semaphore semaphore = new Semaphore(2);

    Map<String, Map<String, Object>> accountSpend = new HashMap<>();
    @Resource
    private PlatformMapper platformMapper;

    @Override
    public ArrayList<AccountBudget> requestAccountBudget() {
        List<AccountDTO> accounts = getAccount();
        ArrayList<AccountBudget> res = new ArrayList<>();
        ArrayList<VivoAccount> accountRes = new ArrayList<>();
        ArrayList<CompletableFuture<Map<String, Object>>> tasks = new ArrayList<>();
        for (AccountDTO account : accounts) {
            CompletableFuture<Map<String, Object>> future =
                    CompletableFuture.supplyAsync(() -> doGetAccountBudget(account), spendAPIExecutor);
            tasks.add(future);
        }

        for (CompletableFuture<Map<String, Object>> task : tasks) {
            Map<String, Object> join = task.join();
            if (join != null) {
                res.add(vivoMap2Account(join));
                try {
                    accountRes.add(vivoMap2AccountUuid(join));
                } catch (Exception e) {
                    log.error("vivo 账户uuid获取失败：{}", join);
                }
            }
        }
        saveAccountBudget(res, Platform.VIVO.media);
        if (!CollectionUtils.isEmpty(accountRes)) {
            platformMapper.updateVivoAccount(accountRes);
        }
        return res;
    }

    private AccountBudget vivoMap2Account(Map<String, Object> map) {
        AccountBudget accountBudget = new AccountBudget();
        accountBudget.setAccount(BlankUtils.getString(map.get("account")));
        accountBudget.setMedia(Platform.VIVO.media);
        double dailyBudget = BlankUtils.getDouble(map.get("dailyBudget"));
        accountBudget.setBudget(dailyBudget == -1 ? dailyBudget : dailyBudget / 100000);
        double balance = BlankUtils.getDouble(map.get("balance"));
        accountBudget.setBalance(balance / 100000);
        accountBudget.setBudget_state(BlankUtils.getInt(map.get("dailyBudgetState")));
        return accountBudget;
    }

    private VivoAccount vivoMap2AccountUuid(Map<String, Object> map) {
        VivoAccount account = new VivoAccount();
        account.setAccount(BlankUtils.getString(map.get("account")));
        account.setUuid(BlankUtils.getString(map.get("uuid")));
        return account;
    }

    private GroupBudget vivoMap2Group(Map<String, Object> map, String account) {
        GroupBudget accountBudget = new GroupBudget();
//        accountBudget.setAccount(BlankUtils.getString(map.get("account")));
        accountBudget.setMedia(Platform.VIVO.media);
        accountBudget.setAccount(account);
        double dailyBudget = BlankUtils.getDouble(map.get("dailyBudget"));
        accountBudget.setBudget(dailyBudget == -1 ? dailyBudget : dailyBudget / 100000);
        accountBudget.setBudget_state(BlankUtils.getInt(map.get("dailyBudgetState")));
        accountBudget.setGroup_id(BlankUtils.getString(map.get("id")));
        accountBudget.setGroup_name(BlankUtils.getString(map.get("name")));
        return accountBudget;
    }

    private CampaignBudget vivoMap2Campaign(Map<String, Object> map, String account) {
        CampaignBudget accountBudget = new CampaignBudget();
        accountBudget.setMedia(Platform.VIVO.media);
        accountBudget.setAccount(account);
        double dailyBudget = BlankUtils.getDouble(map.get("dailyBudget"));
        accountBudget.setBudget(dailyBudget == -1 ? dailyBudget : dailyBudget / 100000);
        accountBudget.setBudget_state(BlankUtils.getInt(map.get("dailyBudgetState")));
        accountBudget.setGroup_id(BlankUtils.getString(map.get("campaignId")));
        accountBudget.setCampaign_id(BlankUtils.getString(map.get("id")));
        accountBudget.setCampaign_name(BlankUtils.getString(map.get("name")));

        return accountBudget;
    }

    /**
     * @param account vivo 投放账户
     * @return map :
     * dailyBudgetState     Integer	日限额状态：0-正常/1-到达日限额
     * dailyBudget          Long	日限额，-1表示不限额;有限额时显示具体限额，单位为毫分，1元=100000毫分;
     * balance              Long	现金余额，现金可在所有资源中使用。单位为毫分（千分之一分），1元=100000毫分;
     */
    private Map<String, Object> doGetAccountBudget(AccountDTO account) {
        try {
            semaphore.acquire(1);
            String accessToken = account.getAccessToken();
            long times = System.currentTimeMillis();
            long nonce = times % 1000000 + random.nextInt(10000) + random.nextInt(199);

            String url = VIVO_ACCOUNT_BUDGET_URL + "?access_token=" + accessToken + "&timestamp=" + times + "&nonce=" + nonce;
            String ret = HttpClientUtils.getInstance().httpGetThrow(url, null, true);
            if (BlankUtils.isBlank(ret)) {
                log.info("vivo账户预算请求失败 {}, data is null", account.getAccount());
                return null;
            }

            JSONObject reportMap = JSONObject.parseObject(ret);
            if (reportMap.getInteger("code") != 0) {
                log.info("vivo账户预算请求失败 {}, 失败信息: {}", account.getAccount(), reportMap.getString("message"));
                if (reportMap.getString("message").equals("参数nonce不合法")) {
                    try {
                        Thread.sleep(20_000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
//                    return doGetAccountBudget(account);
                }
                return null;
            }

            Map<String, Object> innerMap = reportMap.getJSONObject("data").getInnerMap();
            innerMap.put("account", account.getAccount());
            return innerMap;
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            semaphore.release();
        }
    }

    public static void main(String[] args) {
        VivoBudgetService service = new VivoBudgetService();
        AccountDTO accountDTO = new AccountDTO();
        accountDTO.setAccessToken("bca3da1e84074db8e4446ebb5275a9990ff9f53582f31131f5fa0c42ddc696ec");
//        service.doGetAccountBudget(accountDTO);
        String date = "2023-12-13";
        service.getFinanceData(accountDTO, date);
    }

    @Override
    protected void commonDataPrepare(String date) {
        List<Map<String, Object>> spendData = dnwxBiAdtMapper.selectSpendGroupByAccount(date, Platform.VIVO.media);
        accountSpend = spendData.stream().collect(Collectors.toMap(map -> map.get("account").toString(), map -> map, (x, y) -> y));
    }

    @Override
    public FinanceReport getFinanceData(AccountDTO accountDTO, String date) {
        //全部
        CompletableFuture<JSONArray> allTask = CompletableFuture.supplyAsync(() -> doGetAccountFinance(accountDTO, date, date, "all"), spendAPIExecutor);
        //现金
        CompletableFuture<JSONArray> cashTask = CompletableFuture.supplyAsync(() -> doGetAccountFinance(accountDTO, date, date, "cash"), spendAPIExecutor);
        //商店虚拟金
        CompletableFuture<JSONArray> appstoreTask = CompletableFuture.supplyAsync(() -> doGetAccountFinance(accountDTO, date, date, "appstore"), spendAPIExecutor);
        //非商店虚拟金
        CompletableFuture<JSONArray> feedTask = CompletableFuture.supplyAsync(() -> doGetAccountFinance(accountDTO, date, date, "feed"), spendAPIExecutor);
        //联盟虚拟金
        CompletableFuture<JSONArray> leagueTask = CompletableFuture.supplyAsync(() -> doGetAccountFinance(accountDTO, date, date, "league"), spendAPIExecutor);
        //通用虚拟金
        CompletableFuture<JSONArray> generalTask = CompletableFuture.supplyAsync(() -> doGetAccountFinance(accountDTO, date, date, "general"), spendAPIExecutor);

        JSONArray allArray =  allTask.join();
        JSONArray cashArray =  cashTask.join();
        JSONArray appstoreArray =  appstoreTask.join();
        JSONArray feedArray =  feedTask.join();
        JSONArray leagueArray =  leagueTask.join();
        JSONArray generalArray =  generalTask.join();

        //全部没有说明其他类型也没有
        if (allArray == null || allArray.size() == 0) {
            log.error("渠道流水拉取为空:媒体 = vivo, 账号 = {}, 日期 = {}",accountDTO.getAccount(),date);
            return null;
        }

        JSONObject all = allArray.getJSONObject(0);
        JSONObject cash = new JSONObject();
        JSONObject appstore = new JSONObject();
        JSONObject feed = new JSONObject();
        JSONObject league = new JSONObject();
        JSONObject general = new JSONObject();

        if (cashArray != null && cashArray.size() > 0) {
            cash = cashArray.getJSONObject(0);
        }
        if (appstoreArray != null && appstoreArray.size() > 0) {
            appstore = appstoreArray.getJSONObject(0);
        }
        if (feedArray != null && feedArray.size() > 0) {
            feed = feedArray.getJSONObject(0);
        }
        if (leagueArray != null && leagueArray.size() > 0) {
            league = leagueArray.getJSONObject(0);
        }
        if (generalArray != null && generalArray.size() > 0) {
            general = generalArray.getJSONObject(0);
        }

        long times = all.getLongValue("date");
        String day = DateUtil.timestamp2Date(times);
        double cashIncome = cash.getDoubleValue("income") / 100000;
        double income = all.getDoubleValue("income") / 100000;
        double cashBalance = cash.getDoubleValue("balance") / 100000;
        double balance = all.getDoubleValue("balance") / 100000;
        double cashExpenses = cash.getDoubleValue("expenses") / 100000;
        double expenses = all.getDoubleValue("expenses") / 100000;
        double appstoreExpenses = appstore.getDoubleValue("expenses") / 100000;
        double feedExpenses = feed.getDoubleValue("expenses") / 100000;
        double leagueExpenses = league.getDoubleValue("expenses") / 100000;
        double generalExpenses = general.getDoubleValue("expenses") / 100000;
        double virIncome = income - cashIncome;
        double virBalance = balance - cashBalance;
        double virExpenses = expenses - cashExpenses;
        FinanceReport financeReport = new FinanceReport();
        String account = accountDTO.getAccount();
        financeReport.setAccount(account);
        financeReport.setCost(expenses);
        financeReport.setCash_cost(cashExpenses);
        financeReport.setReward_cost(virExpenses);
        financeReport.setIncome(virIncome);
        financeReport.setTransfer_in(income);
        financeReport.setCash_balance(cashBalance);
        financeReport.setNot_cash_balance(virBalance);
        financeReport.setDay(day);
        financeReport.setAd_platform(Platform.VIVO.media);
        financeReport.setAgent(accountDTO.getAgent());
        financeReport.setPutUser(accountDTO.getPutUser());
        financeReport.setType(accountDTO.getType().toString());
        financeReport.setRebate(accountDTO.getRebate());
        financeReport.setAppstore_cost(appstoreExpenses);
        financeReport.setFeed_cost(feedExpenses);
        financeReport.setLeague_cost(leagueExpenses);
        financeReport.setGeneral_cost(generalExpenses);
        Map<String, Object> accountData = accountSpend.get(account);
        if (accountData != null) {
            financeReport.setInstalls(BlankUtils.getInt(accountData.get("installs")));
            financeReport.setVirtualSpend(BlankUtils.getDouble(accountData.get("virtualSpend")));
            if (financeReport.getVirtualSpend() == 0.00) {
                financeReport.setVirtualSpend(virExpenses);
            }
            financeReport.setSpend(BlankUtils.getDouble(accountData.get("spend")));
        } else {
            financeReport.setInstalls(0);
            financeReport.setSpend(0D);
            financeReport.setVirtualSpend(0D);
        }

        if (financeReport == null) {
            log.warn("vivo 流水拉取，日期 {} 账户 {} 没有数据", date, accountDTO.getAccount());
        }
        log.info("vivo 流水拉取, 日期 = {} 账户 = {} 拉取结束", date, accountDTO.getAccount());
        return financeReport;
    }

    private JSONArray doGetAccountFinance(AccountDTO account, String startDate, String endDate, String type) {
        try {
            semaphore.acquire(1);
            JSONObject base = doGetPageAccountFinance(account, startDate, endDate, 1, type);
            if (base == null) {
                return null;
            }
            JSONArray jsonArray = base.getJSONArray("list");

            JSONObject pageInfo = base.getJSONObject("pageInfo");
            Integer pageCount = pageInfo.getInteger("pageCount");
            if (pageCount == null) {
                return jsonArray;
            }

            for (int i = 2; i <= pageCount; i++) {
                try {
                    JSONObject pageBase = doGetPageAccountFinance(account, startDate, endDate, i, type);
                    JSONArray pageArray = pageBase.getJSONArray("list");
                    if (!CollectionUtils.isEmpty(pageArray)) {
                        jsonArray.addAll(pageArray);
                    }
                } catch (Exception e) {
                    log.error("账户流水拉取发生错误，account：{}，page：{}", account, i, e);
                }
            }
            return jsonArray;
        } catch (InterruptedException e) {
            log.warn("vivo 流水拉取，日期 {} 账户 {} 错误，", startDate, account, e);
            return null;
        } finally {
            semaphore.release();
        }
    }

    /**
     * @param account vivo 投放账户
     * @return
     */
    private JSONObject doGetPageAccountFinance(AccountDTO account, String startDate, String endDate, int pageIndex, String type) {
        String accessToken = account.getAccessToken();
        long times = System.currentTimeMillis();
        long nonce = times % 1000000 + random.nextInt(10000);

        String url = VIVO_ACCOUNT_FINANCE_URL + "?access_token=" + accessToken + "&timestamp=" + times + "&nonce=" + nonce;
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", 100);
        param.put("startDate", startDate);
        param.put("endDate", endDate);

        HashMap<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");


        String ret = HttpClientUtils.getInstance().httpPostThrow(url, JSONObject.toJSONString(param), header);
        if (BlankUtils.isBlank(ret)) {
            log.info("vivo账户流水请求失败 {}, data is null", account.getAccount());
            return null;
        }

        JSONObject reportMap = JSONObject.parseObject(ret);
        if (reportMap.getInteger("code") != 0) {
            log.info("vivo账户流水请求失败 {}, 失败信息: {}", account.getAccount(), reportMap.getString("message"));
            if (reportMap.getString("message").equals("参数nonce不合法")) {
                try {
                    Thread.sleep(20_000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                return doGetPageAccountFinance(account, startDate, endDate, pageIndex, type);
            }
            return null;
        }

        return reportMap.getJSONObject("data");
    }

    /**
     * 请求我们定义的group\campaign 级别的预算信息, vivo叫做计划
     *
     * @param account vivo 投放账户
     * @param lastId  必填	Long	数据起始偏移量，必填，首页填0
     * @return map :
     * dailyBudgetState     Integer	日限额状态：0-正常/1-到达日限额
     * dailyBudget          Long	日限额，-1表示不限额;有限额时显示具体限额，单位为毫分，1元=100000毫分;
     * balance              Long	现金余额，现金可在所有资源中使用。单位为毫分（千分之一分），1元=100000毫分;
     */
    private JSONArray doGetGroupBudget(AccountDTO account, Long lastId, String baseUrl) {

        JSONArray jsonArray = new JSONArray();
        String accessToken = account.getAccessToken();
        long times = System.currentTimeMillis();
        long nonce = times % 1000000 + random.nextInt(10000);

        String url = baseUrl + "?access_token=" + accessToken + "&timestamp=" + times + "&nonce=" + nonce;
        HashMap<String, Object> params = new HashMap<>();
        params.put("lastId", lastId);
        params.put("pageSize", LIMIT);

        HashMap<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        String ret = HttpClientUtils.getInstance().httpPostThrow(url, JSONObject.toJSONString(params), header);
        if (BlankUtils.isBlank(ret)) {
            log.info("vivo计划.组预算请求失败 {}, data is null", account.getAccount());
            return null;
        }

        JSONObject reportMap = JSONObject.parseObject(ret);
        if (reportMap.getInteger("code") != 0) {
            log.info("vivo计划.组预算请求失败 {}, 失败信息: {}", account.getAccount(), reportMap.getString("message"));
            boolean returnNull = true;
            if (reportMap.getString("message").equals("参数nonce不合法")) {
                // 重试两次
                for (int i = 0; i < 2; i++) {
                    try {
                        Thread.sleep(20_000 * (i + 1));
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    nonce = times % 1000000 + random.nextInt(1000000);
                    url = baseUrl + "?access_token=" + accessToken + "&timestamp=" + times + "&nonce=" + nonce;
                    ret = HttpClientUtils.getInstance().httpPostThrow(url, JSONObject.toJSONString(params), header);
                    reportMap = JSONObject.parseObject(ret);
                    if (reportMap.getInteger("code") != 0) {
                        returnNull = false;
                        break;
                    }
                }
            }

            if (returnNull) {
                return null;
            }
        }

        JSONObject data = reportMap.getJSONObject("data");
        JSONArray saveData = data.getJSONArray("data");
        if (!CollectionUtils.isEmpty(saveData)) {
            jsonArray.addAll(saveData);
        }

        if (data.size() == LIMIT) {
            Long flastId = data.getLong("lastId");
            JSONArray pageArray = doGetGroupBudget(account, flastId, baseUrl);
            if (!CollectionUtils.isEmpty(pageArray)) {
                jsonArray.addAll(pageArray);
            }
        }
        return jsonArray;
    }


    @Override
    public List<GroupBudget> requestGroupBudget() {
        List<AccountDTO> accounts = getAccount();

        ArrayList<CompletableFuture<List<GroupBudget>>> tasks = new ArrayList<>();
        for (AccountDTO account : accounts) {
            CompletableFuture<List<GroupBudget>> future = CompletableFuture.supplyAsync(() -> requestGroupBudget(account), spendAPIExecutor);
            tasks.add(future);
        }
        ArrayList<GroupBudget> res = new ArrayList<>();
        for (CompletableFuture<List<GroupBudget>> task : tasks) {
            List<GroupBudget> campaign = task.join();
            if (!CollectionUtils.isEmpty(campaign)) {
                res.addAll(campaign);
            }
        }
        saveGroupBudget(res, Platform.VIVO.media);

        return null;
    }

    @Override
    public List<GroupBudget> requestGroupBudget(AccountDTO account) {
        try {
            semaphore.acquire(1);
            JSONArray array = doGetGroupBudget(account, 0L, VIVO_GROUP_BUDGET_URL);
            if (CollectionUtils.isEmpty(array)) {
                return null;
            }
            return array.stream().map(o -> (JSONObject) o)
                    .filter(map -> map.getInteger("pauseState") != 1)
                    .map(JSONObject::getInnerMap)
                    .map(map -> vivoMap2Group(map, account.getAccount()))
                    .collect(Collectors.toList());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            semaphore.release();
        }
    }

    @Override
    public List<CampaignBudget> requestCampaignBudget() {
        List<AccountDTO> accounts = getAccount();
        ArrayList<CompletableFuture<List<CampaignBudget>>> tasks = new ArrayList<>();
        for (AccountDTO account : accounts) {
            CompletableFuture<List<CampaignBudget>> future = CompletableFuture.supplyAsync(() -> requestCampaignBudget(account), spendAPIExecutor);
            tasks.add(future);
        }
        ArrayList<CampaignBudget> res = new ArrayList<>();
        for (CompletableFuture<List<CampaignBudget>> task : tasks) {
            List<CampaignBudget> campaign = task.join();
            if (!CollectionUtils.isEmpty(campaign)) {
                res.addAll(campaign);
            }
        }
        saveCampaignBudget(res, Platform.VIVO.media);

        return null;
    }

    @Override
    public List<CampaignBudget> requestCampaignBudget(AccountDTO account) {
        try {
            semaphore.acquire(1);
            JSONArray array = doGetGroupBudget(account, 0L, VIVO_CAMPAIGN_BUDGET_URL);
            if (CollectionUtils.isEmpty(array)) {
                return null;
            }
            return array.stream().map(o -> (JSONObject) o)
                    .filter(map -> map.getInteger("pauseState") != 1)
                    .map(JSONObject::getInnerMap)
                    .map(map -> vivoMap2Campaign(map, account.getAccount()))
                    .collect(Collectors.toList());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            semaphore.release();
        }
    }

    @Override
    protected void businessErrorLog(String s, Exception e) {
        log.error(s, Platform.VIVO.media, e);
    }

    @Override
    protected List<AccountDTO> getAccount() {
        AccountParam accountParam = new AccountParam();
        accountParam.setEnabled(1);
        List<AccountDTO> vivoAccount = adtMapper.getVivoAccount(accountParam);
        return vivoAccount;
    }
}
