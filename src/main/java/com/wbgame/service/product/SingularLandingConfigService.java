package com.wbgame.service.product;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.product.SingularLandingConfig;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @Description Singular落地页配置Service接口
 * @Date 2025/06/27
 */
public interface SingularLandingConfigService {

    /**
     * 分页查询配置列表
     *
     * @param config 查询条件
     * @return 分页结果
     */
    Result<PageResult<SingularLandingConfig>> queryList(SingularLandingConfig config);

    /**
     * 根据ID查询配置详情
     *
     * @param id 主键ID
     * @return 配置详情
     */
    Result<SingularLandingConfig> queryById(Long id);

    /**
     * 根据SDK Key获取配置（供落地页使用）
     *
     * @param sdkKey SDK Key
     * @return 配置信息
     */
    Result<SingularLandingConfig> getConfigBySdkKey(String sdkKey);

    /**
     * 根据Bundle ID获取配置（供落地页使用）
     *
     * @param bundleId Bundle ID
     * @return 配置信息
     */
    Result<SingularLandingConfig> getConfigByBundleId(String bundleId);

    /**
     * 新增配置
     *
     * @param config 配置信息
     * @return 操作结果
     */
    Result<Integer> insert(SingularLandingConfig config);

    /**
     * 更新配置
     *
     * @param config 配置信息
     * @return 操作结果
     */
    Result<Integer> update(SingularLandingConfig config);

    /**
     * 删除配置
     *
     * @param id 主键ID
     * @return 操作结果
     */
    Result<Integer> deleteById(Long id);

    /**
     * 批量删除配置
     *
     * @param ids 主键ID列表
     * @return 操作结果
     */
    Result<Integer> batchDelete(List<Long> ids);

    /**
     * 校验配置数据
     *
     * @param config 配置信息
     * @param isUpdate 是否为更新操作
     * @return 校验结果
     */
    Result<String> validateConfig(SingularLandingConfig config, boolean isUpdate);
}
