package com.wbgame.service.product.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wbgame.common.Constants;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.master.product.SingularLandingConfigMapper;
import com.wbgame.pojo.product.SingularLandingConfig;
import com.wbgame.service.product.SingularLandingConfigService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.PageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description Singular落地页配置Service实现类
 * @Date 2025/06/27
 */
@Service
public class SingularLandingConfigServiceImpl implements SingularLandingConfigService {

    private static final Logger logger = LoggerFactory.getLogger(SingularLandingConfigServiceImpl.class);

    @Autowired
    private SingularLandingConfigMapper singularLandingConfigMapper;

    @Override
    public Result<PageResult<SingularLandingConfig>> queryList(SingularLandingConfig config) {
        try {
            // 参数校验
            config.checkPageParams();
            
            // 分页查询
            PageHelper.startPage(config.getStart(), config.getLimit());
            List<SingularLandingConfig> list = singularLandingConfigMapper.queryList(config);
            PageInfo<SingularLandingConfig> pageInfo = new PageInfo<>(list);
            
            return ResultUtils.success(PageResult.page(pageInfo));
        } catch (Exception e) {
            logger.error("查询Singular落地页配置列表失败", e);
            return ResultUtils.failure("操作失败："+e.getMessage());
        }
    }

    @Override
    public Result<SingularLandingConfig> queryById(Long id) {
        try {
            if (id == null || id <= 0) {
                return ResultUtils.failure("参数错误");
            }

            SingularLandingConfig config = singularLandingConfigMapper.queryById(id);
            return ResultUtils.success(config);
        } catch (Exception e) {
            logger.error("根据ID查询Singular落地页配置失败，ID: {}", id, e);
            return ResultUtils.failure("操作失败："+e.getMessage());
        }
    }

    @Override
    public Result<SingularLandingConfig> getConfigBySdkKey(String sdkKey) {
        try {
            if (BlankUtils.checkBlank(sdkKey)) {
                return ResultUtils.failure("参数错误");
            }

            SingularLandingConfig config = singularLandingConfigMapper.queryBySdkKey(sdkKey);
            if (config == null) {
                return ResultUtils.failure("配置不存在");
            }

            return ResultUtils.success(config);
        } catch (Exception e) {
            logger.error("根据SDK Key查询Singular落地页配置失败，sdkKey: {}", sdkKey, e);
            return ResultUtils.failure(Constants.ERROR);
        }
    }

    @Override
    public Result<SingularLandingConfig> getConfigByBundleId(String bundleId) {
        try {
            if (BlankUtils.checkBlank(bundleId)) {
                return ResultUtils.failure("参数错误");
            }

            SingularLandingConfig config = singularLandingConfigMapper.queryByBundleId(bundleId);
            if (config == null) {
                return ResultUtils.failure("配置不存在");
            }

            return ResultUtils.success(config);
        } catch (Exception e) {
            logger.error("根据Bundle ID查询Singular落地页配置失败，bundleId: {}", bundleId, e);
            return ResultUtils.failure("操作失败："+e.getMessage());
        }
    }

    @Override
    public Result<Integer> insert(SingularLandingConfig config) {
        try {
            // 参数校验
            Result<String> validateResult = validateConfig(config, false);
            if (Constants.OK.getRet() != (validateResult.getRet())) {
                return ResultUtils.failure(validateResult.getMsg());
            }
            
            // 设置默认值
            if (config.getStatus() == null) {
                config.setStatus(1);
            }
            
            int result = singularLandingConfigMapper.insert(config);
            if (result > 0) {
                return ResultUtils.success(result);
            } else {
                return ResultUtils.failure("新增失败");
            }
        } catch (Exception e) {
            logger.error("新增Singular落地页配置失败", e);
            return ResultUtils.failure("操作失败："+e.getMessage());
        }
    }

    @Override
    public Result<Integer> update(SingularLandingConfig config) {
        try {
            if (config.getId() == null || config.getId() <= 0) {
                return ResultUtils.failure("参数错误");
            }

            // 参数校验
            Result<String> validateResult = validateConfig(config, true);
            if (Constants.OK.getRet() != validateResult.getRet()) {
                return ResultUtils.failure(validateResult.getMsg());
            }
            
            int result = singularLandingConfigMapper.update(config);
            if (result > 0) {
                return ResultUtils.success(result);
            } else {
                return ResultUtils.failure("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新Singular落地页配置失败", e);
            return ResultUtils.failure("操作失败："+e.getMessage());
        }
    }

    @Override
    public Result<Integer> deleteById(Long id) {
        try {
            if (id == null || id <= 0) {
                return ResultUtils.failure("参数错误");
            }

            int result = singularLandingConfigMapper.deleteById(id);
            if (result > 0) {
                return ResultUtils.success(result);
            } else {
                return ResultUtils.failure("删除失败");
            }
        } catch (Exception e) {
            logger.error("删除Singular落地页配置失败，ID: {}", id, e);
            return ResultUtils.failure("操作失败："+e.getMessage());
        }
    }

    @Override
    public Result<Integer> batchDelete(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return ResultUtils.failure("参数错误");
            }

            int result = singularLandingConfigMapper.batchDelete(ids);
            return ResultUtils.success(result);
        } catch (Exception e) {
            logger.error("批量删除Singular落地页配置失败", e);
            return ResultUtils.failure("操作失败："+e.getMessage());
        }
    }

    @Override
    public Result<String> validateConfig(SingularLandingConfig config, boolean isUpdate) {
        try {
            // 必填字段校验
            if (BlankUtils.checkBlank(config.getConfigName())) {
                return ResultUtils.failure("配置名称不能为空");
            }
            if (BlankUtils.checkBlank(config.getSdkKey())) {
                return ResultUtils.failure("SDK Key不能为空");
            }
            if (BlankUtils.checkBlank(config.getSdkSecret())) {
                return ResultUtils.failure("SDK Secret不能为空");
            }
            if (BlankUtils.checkBlank(config.getBundleId())) {
                return ResultUtils.failure("Bundle ID不能为空");
            }
            if (BlankUtils.checkBlank(config.getBaseLink())) {
                return ResultUtils.failure("基础链接不能为空");
            }
            
            // 按钮组JSON格式校验
            if (!BlankUtils.checkBlank(config.getButtonGroups())) {
                try {
                    JSONArray.parseArray(config.getButtonGroups());
                } catch (Exception e) {
                    return ResultUtils.failure("按钮组信息格式错误，必须为有效的JSON数组");
                }
            }
            
//            // 唯一性校验
//            Long excludeId = isUpdate ? config.getId() : null;
//
//            // SDK Key唯一性校验
//            int sdkKeyCount = singularLandingConfigMapper.checkSdkKeyExists(config.getSdkKey(), excludeId);
//            if (sdkKeyCount > 0) {
//                return ResultUtils.failure("SDK Key已存在");
//            }
//
//            // Bundle ID唯一性校验
//            int bundleIdCount = singularLandingConfigMapper.checkBundleIdExists(config.getBundleId(), excludeId);
//            if (bundleIdCount > 0) {
//                return ResultUtils.failure("Bundle ID已存在");
//            }
            
            return ResultUtils.success("校验通过");
        } catch (Exception e) {
            logger.error("校验Singular落地页配置失败", e);
            return ResultUtils.failure("校验失败");
        }
    }
}
