package com.wbgame.service.clean.impl;

import com.github.pagehelper.PageHelper;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.mapper.clean.master.AdverConfigMapper;
import com.wbgame.pojo.clean.AdverConfig;
import com.wbgame.service.clean.IAdverConfigService;
import com.wbgame.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/12/21 021
 * @class: AdverConfigServiceImpl
 * @description:
 */
@Service
public class AdverConfigServiceImpl implements IAdverConfigService {

    @Autowired
    private AdverConfigMapper configMapper;

    @Override
    public Result<Integer> deleteAdverConfig(List<Integer> idList) {

        return ObjectUtils.isEmpty(idList) ? ResultUtils.failure("参数错误")
                : ResultUtils.success(configMapper.deleteAdverConfig(idList));
    }

    @Override
    public Result<Integer> insertAdverConfig(AdverConfig record) {

        if (configMapper.selectAdverName(record.getAdverName()) != null) {

            return ResultUtils.failure("广告名已存在");
        }
        configMapper.insertAdverConfig(record);
        return ResultUtils.success();
    }

    @Override
    public Result<PageResult<AdverConfig>> selectAdverConfig(AdverConfig example) {

        PageHelper.startPage(example.getStart(), example.getLimit());

        List<AdverConfig> list = configMapper.selectAdverConfig(example);
        return ResultUtils.success(PageResult.page(list));
    }

    @Override
    public Result<Integer> updateAdverConfig(AdverConfig record) {

        configMapper.updateAdverConfig(record);
        return ResultUtils.success();
    }
}
