package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.SafeMarkConfigDTO;
import com.wbgame.pojo.clean.SafeMarkConfigQuery;

import java.util.List;

/**
 * @authoer: zhangY
 * @createDate: 2022/6/14 16:39
 * @class: ICleanAppOutsideFunctionService
 * @description:
 */
public interface ICleanAppOutsideFunctionService {

    /**
     * 黑白名单配置
     *
     */
    String insertSafeMarkConfig(SafeMarkConfigDTO safeMarkConfigDTO);
    String deleteById(String id);
    String selectAll(SafeMarkConfigQuery query);

    Result<List<String>> batchImport(List<String[]> importDateList, String userName);
}
