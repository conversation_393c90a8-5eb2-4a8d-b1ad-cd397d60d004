package com.wbgame.service.clean;

import com.wbgame.common.response.Result;
import com.wbgame.pojo.clean.stepcount.StepCountConfig;
import com.wbgame.pojo.clean.stepcount.StepCountConfigVO;
import com.wbgame.utils.PageResult;

import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/11/03 003
 * @class: IStepCountConfigService
 * @description:
 */
public interface IStepCountConfigService {

    Result<Integer> deleteStepCountConfig(List<Long> idList);

    Result<Long> insertStepCountConfig(StepCountConfig record);

    Result<PageResult<StepCountConfigVO>> selectStepCountConfig(StepCountConfig dto);

    Result<Integer> updateStepCountConfig(StepCountConfig record);
}
