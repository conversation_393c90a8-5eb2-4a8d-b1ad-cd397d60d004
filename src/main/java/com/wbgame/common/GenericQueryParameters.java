package com.wbgame.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * @author: zhangY
 * @createDate: 2022/10/10
 * @class: GenericQueryParameters
 * @description:
 */
@ApiModel(value = "通用查询参数")
public class GenericQueryParameters {

    @ApiModelProperty("起始日期")
    @Pattern(regexp = Constant.DATE_YYYY_MM_DD, message = "日期格式错误", groups = {QueryGroup.class})
    private String start_date;

    @ApiModelProperty("结束日期")
    @Pattern(regexp = Constant.DATE_YYYY_MM_DD, message = "日期格式错误", groups = {QueryGroup.class})
    private String end_date;

    @ApiModelProperty(value = "页码",dataType = "Integer", required = true)
    @NotNull(message = "页码不能为空", groups = {QueryGroup.class, QueryRemoveDateGroup.class})
    private Integer start;

    @ApiModelProperty(value = "条数",dataType = "Integer", required = true)
    @NotNull(message = "条数不能为空", groups = {QueryGroup.class, QueryRemoveDateGroup.class})
    private Integer limit;

    @ApiModelProperty("产品id集合")
    private List<String> appidList;

    @ApiModelProperty("渠道集合")
    private List<String> channelList;

    @ApiModelProperty("媒体集合")
    private List<String> mediaList;

    @ApiModelProperty("项目id集合")
    private List<String> prjidList;

    @ApiModelProperty("导出参数，总数据数量")
    private Integer total;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public List<String> getAppidList() {
        return appidList;
    }

    public void setAppidList(List<String> appidList) {
        this.appidList = appidList;
    }

    public List<String> getChannelList() {
        return channelList;
    }

    public void setChannelList(List<String> channelList) {
        this.channelList = channelList;
    }

    public List<String> getMediaList() {
        return mediaList;
    }

    public void setMediaList(List<String> mediaList) {
        this.mediaList = mediaList;
    }

    public List<String> getPrjidList() {
        return prjidList;
    }

    public void setPrjidList(List<String> prjidList) {
        this.prjidList = prjidList;
    }
}
