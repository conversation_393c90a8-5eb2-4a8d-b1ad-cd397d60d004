package com.wbgame.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 审批人信息VO
 * <AUTHOR>
 */
@Data
@ApiModel("审批人信息")
public class ApproverInfoVO {


    @ApiModelProperty("用户名")
    private String login_name;

    @ApiModelProperty("昵称")
    private String nick_name;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("权限ID")
    private String org_id;

    @ApiModelProperty("角色ID")
    private String role_id;

    @ApiModelProperty("部门ID")
    private String department;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("为审批人：1-是 0-否")
    private String approver;

    @ApiModelProperty("菜单列表 (无视分类及菜单目录的页面index)")
    private String page_list;

    @ApiModelProperty("隐藏菜单列表")
    private String hidden_menu_list;

    @ApiModelProperty("应用列表")
    private String app_group;

    @ApiModelProperty("应用分类列表")
    private String app_category;

    @ApiModelProperty("审批级别：1-一级审批，2-二级审批")
    private Integer approvalLevel;

    @ApiModelProperty("是否有权限：true-有权限，false-无权限")
    private Boolean hasPermission;

    @ApiModelProperty("权限详情")
    private String permissionDetail;

    @ApiModelProperty("飞书用户ID")
    private String fsuser_id;

}
