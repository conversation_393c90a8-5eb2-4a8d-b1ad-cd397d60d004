package com.wbgame.pojo.operate;

import com.wbgame.common.GeneralReportParam;
import com.wbgame.common.GenericQueryParameters;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("用户分群关卡统计dto")
public class AdsUserGroupLevelSumDaily extends GeneralReportParam {

    @ApiModelProperty("'日期'")
    private String tdate;

    @ApiModelProperty("'应用id'")
    private String appid;

    @ApiModelProperty("'子渠道'")
    private String channel;

    @ApiModelProperty("'项目id'")
    private String pid;

    @ApiModelProperty("'关卡id'")
    private String levelId;

    @ApiModelProperty("'用户分组'")
    private String groupId;

    @ApiModelProperty("'停留人数'")
    private String stayNum;

    @ApiModelProperty("'用户分组(查询使用)'")
    private List<String> groupIdList;

    @ApiModelProperty("'排序字段'")
    private String order_str;

    @Override
    public String getOrder_str() {
        return order_str;
    }

    @Override
    public void setOrder_str(String order_str) {
        this.order_str = order_str;
    }

    public List<String> getGroupIdList() {
        return groupIdList;
    }

    public void setGroupIdList(List<String> groupIdList) {
        this.groupIdList = groupIdList;
    }

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate == null ? null : tdate.trim();
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid == null ? null : pid.trim();
    }

    public String getLevelId() {
        return levelId;
    }

    public void setLevelId(String levelId) {
        this.levelId = levelId == null ? null : levelId.trim();
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId == null ? null : groupId.trim();
    }

    public String getStayNum() {
        return stayNum;
    }

    public void setStayNum(String stayNum) {
        this.stayNum = stayNum == null ? null : stayNum.trim();
    }
}