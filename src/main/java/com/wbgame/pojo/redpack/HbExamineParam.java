package com.wbgame.pojo.redpack;

import java.math.BigDecimal;

/**
 * @description: 审核参数
 * @author: huangmb
 * @date: 2021/08/13
 **/
public class HbExamineParam {

    private String appid;

    private String channel;

    private String pid;

    private String userid;

    private BigDecimal amount;

    private Integer status;

    private String statusName;

    private Integer minRedticket;

    private Integer maxRedticket;

    private Integer minVideo;

    private Integer maxVideo;

    private BigDecimal minecpm;

    private BigDecimal maxecpm;

    private Integer minWithdraw;

    private Integer maxWithdraw;

    private String order;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getMinRedticket() {
        return minRedticket;
    }

    public void setMinRedticket(Integer minRedticket) {
        this.minRedticket = minRedticket;
    }

    public Integer getMaxRedticket() {
        return maxRedticket;
    }

    public void setMaxRedticket(Integer maxRedticket) {
        this.maxRedticket = maxRedticket;
    }

    public Integer getMinVideo() {
        return minVideo;
    }

    public void setMinVideo(Integer minVideo) {
        this.minVideo = minVideo;
    }

    public Integer getMaxVideo() {
        return maxVideo;
    }

    public void setMaxVideo(Integer maxVideo) {
        this.maxVideo = maxVideo;
    }

    public BigDecimal getMinecpm() {
        return minecpm;
    }

    public void setMinecpm(BigDecimal minecpm) {
        this.minecpm = minecpm;
    }

    public BigDecimal getMaxecpm() {
        return maxecpm;
    }

    public void setMaxecpm(BigDecimal maxecpm) {
        this.maxecpm = maxecpm;
    }

    public Integer getMinWithdraw() {
        return minWithdraw;
    }

    public void setMinWithdraw(Integer minWithdraw) {
        this.minWithdraw = minWithdraw;
    }

    public Integer getMaxWithdraw() {
        return maxWithdraw;
    }

    public void setMaxWithdraw(Integer maxWithdraw) {
        this.maxWithdraw = maxWithdraw;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
