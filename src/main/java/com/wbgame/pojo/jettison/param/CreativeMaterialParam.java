package com.wbgame.pojo.jettison.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 创意素材库查询参数
 * <AUTHOR>
 * @Date 2024/10/30 18:07
 */
@Data
@ApiModel("创意素材库查询参数")
public class CreativeMaterialParam {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("素材名称")
    private String fileName;

    @ApiModelProperty("标签产品名称")
    private String tag_app_name;

    @ApiModelProperty("类型(图片还是视频)")
    private String type;

    @ApiModelProperty("设计师")
    private String artist;

    @ApiModelProperty("创意人")
    private String creatives;

    @ApiModelProperty("3D制作人")
    private String producer3d;

    @ApiModelProperty("制作参与人")
    private String product_part;

    @ApiModelProperty("3D真人")
    private String immortal3d;

    @ApiModelProperty("创建日期-开始时间")
    private String start_time;

    @ApiModelProperty("创建日期-结束时间")
    private String end_time;

    @ApiModelProperty("制作方式")
    private String label1;

    @ApiModelProperty("手段")
    private String label2;

    @ApiModelProperty("内容标签")
    private String label3;

    @ApiModelProperty("宽")
    private Integer wide;

    @ApiModelProperty("高")
    private Integer height;

    @ApiModelProperty("大小")
    private Double size;

    @ApiModelProperty("格式")
    private String format;

    @ApiModelProperty("排序")
    private String order_str;

    @ApiModelProperty("文件夹产品")
    private String pathName;

    @ApiModelProperty("媒体")
    private String ad_platform;

}
