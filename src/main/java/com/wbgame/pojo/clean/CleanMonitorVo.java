package com.wbgame.pojo.clean;


/**
 * @description: 应用数据监控变更数据
 * @author: huangmb
 * @date: 2020/12/29
 **/

public class CleanMonitorVo {
    //日期
    private String ds;

    //应用名称
    private String appName;

    //app版本
    private String version;

    //渠道
    private String channel;

    //新用户
    private Integer addNum;

    //活跃用户
    private Integer actNum;

    //留存率
    private String keepRate;

    //新用户占比
    private String addRate;

    //首页用户
    private Integer homeShow;

    //开屏新用户
    private String newSplashShow;

    //开屏老用户
    private String oldSplashShow;

    //首页新用户
    private String newShowRate;

    //首页老用户
    private String oldShowRate;

    //锁屏人均展示
    private String lockAvgShow;

    //锁屏广告人均展示
    private String lockAdAvgShow;

    //锁屏广告人均点击
    private String lockAdAvgClick;

    //弹窗人均展示
    private String popupShowPv;

    //弹窗广告人均展示
    private String popupAdShowPv;

    //弹窗广告人均点击
    private String popupAdClickPv;

    //弹窗功能人均点击
    private String popupUseClickPv;

    //功能点击率
    private String popupUseClickRate;

    //红包人均展示
    private String popupMoneyAvgShow;

    //红包人均点击
    private String popupMoneyAvgClick;

    //红包点击率
    private String popupMoneyRate;

    public String getDs() {
        return ds;
    }

    public void setDs(String ds) {
        this.ds = ds;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Integer getAddNum() {
        return addNum;
    }

    public void setAddNum(Integer addNum) {
        this.addNum = addNum;
    }

    public Integer getActNum() {
        return actNum;
    }

    public void setActNum(Integer actNum) {
        this.actNum = actNum;
    }

    public String getKeepRate() {
        return keepRate;
    }

    public void setKeepRate(String keepRate) {
        this.keepRate = keepRate;
    }

    public String getAddRate() {
        return addRate;
    }

    public void setAddRate(String addRate) {
        this.addRate = addRate;
    }

    public Integer getHomeShow() {
        return homeShow;
    }

    public void setHomeShow(Integer homeShow) {
        this.homeShow = homeShow;
    }

    public String getNewSplashShow() {
        return newSplashShow;
    }

    public void setNewSplashShow(String newSplashShow) {
        this.newSplashShow = newSplashShow;
    }

    public String getOldSplashShow() {
        return oldSplashShow;
    }

    public void setOldSplashShow(String oldSplashShow) {
        this.oldSplashShow = oldSplashShow;
    }

    public String getNewShowRate() {
        return newShowRate;
    }

    public void setNewShowRate(String newShowRate) {
        this.newShowRate = newShowRate;
    }

    public String getOldShowRate() {
        return oldShowRate;
    }

    public void setOldShowRate(String oldShowRate) {
        this.oldShowRate = oldShowRate;
    }

    public String getLockAvgShow() {
        return lockAvgShow;
    }

    public void setLockAvgShow(String lockAvgShow) {
        this.lockAvgShow = lockAvgShow;
    }

    public String getLockAdAvgShow() {
        return lockAdAvgShow;
    }

    public void setLockAdAvgShow(String lockAdAvgShow) {
        this.lockAdAvgShow = lockAdAvgShow;
    }

    public String getLockAdAvgClick() {
        return lockAdAvgClick;
    }

    public void setLockAdAvgClick(String lockAdAvgClick) {
        this.lockAdAvgClick = lockAdAvgClick;
    }

    public String getPopupShowPv() {
        return popupShowPv;
    }

    public void setPopupShowPv(String popupShowPv) {
        this.popupShowPv = popupShowPv;
    }

    public String getPopupAdShowPv() {
        return popupAdShowPv;
    }

    public void setPopupAdShowPv(String popupAdShowPv) {
        this.popupAdShowPv = popupAdShowPv;
    }

    public String getPopupAdClickPv() {
        return popupAdClickPv;
    }

    public void setPopupAdClickPv(String popupAdClickPv) {
        this.popupAdClickPv = popupAdClickPv;
    }

    public String getPopupUseClickPv() {
        return popupUseClickPv;
    }

    public void setPopupUseClickPv(String popupUseClickPv) {
        this.popupUseClickPv = popupUseClickPv;
    }

    public String getPopupUseClickRate() {
        return popupUseClickRate;
    }

    public void setPopupUseClickRate(String popupUseClickRate) {
        this.popupUseClickRate = popupUseClickRate;
    }

    public String getPopupMoneyAvgShow() {
        return popupMoneyAvgShow;
    }

    public void setPopupMoneyAvgShow(String popupMoneyAvgShow) {
        this.popupMoneyAvgShow = popupMoneyAvgShow;
    }

    public String getPopupMoneyAvgClick() {
        return popupMoneyAvgClick;
    }

    public void setPopupMoneyAvgClick(String popupMoneyAvgClick) {
        this.popupMoneyAvgClick = popupMoneyAvgClick;
    }

    public String getPopupMoneyRate() {
        return popupMoneyRate;
    }

    public void setPopupMoneyRate(String popupMoneyRate) {
        this.popupMoneyRate = popupMoneyRate;
    }
}