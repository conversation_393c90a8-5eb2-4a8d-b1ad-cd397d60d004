package com.wbgame.pojo.clean;

import com.wbgame.common.GeneralReportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("锁屏黑名单统计查询")
public class AppcfgBlackLog extends GeneralReportParam {
    private Integer id;

    private String lsn;

    private String imei;

    private String andId;

    private String prjid;

    private String createTime;

    private String reason;

    @ApiModelProperty("ip")
    private String ip;

    @ApiModelProperty("查询使用：ip集合")
    private List<String> ipList;

    @ApiModelProperty("累计拦截")
    private Integer totalInterception;

    @ApiModelProperty("昨日拦截")
    private Integer interceptedYesterday;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLsn() {
        return lsn;
    }

    public void setLsn(String lsn) {
        this.lsn = lsn;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getAndId() {
        return andId;
    }

    public void setAndId(String andId) {
        this.andId = andId;
    }

    public String getPrjid() {
        return prjid;
    }

    public void setPrjid(String prjid) {
        this.prjid = prjid;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public List<String> getIpList() {
        return ipList;
    }

    public void setIpList(List<String> ipList) {
        this.ipList = ipList;
    }

    public Integer getTotalInterception() {
        return totalInterception;
    }

    public void setTotalInterception(Integer totalInterception) {
        this.totalInterception = totalInterception;
    }

    public Integer getInterceptedYesterday() {
        return interceptedYesterday;
    }

    public void setInterceptedYesterday(Integer interceptedYesterday) {
        this.interceptedYesterday = interceptedYesterday;
    }
}