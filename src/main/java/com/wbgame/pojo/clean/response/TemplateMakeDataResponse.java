package com.wbgame.pojo.clean.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname TemplateMakeDataResponse
 * @Description TODO
 * @Date 2025/5/28 16:22
 */
@Data
public class TemplateMakeDataResponse {

    @ApiModelProperty(value = "日期",dataType = "String")
    private String tdate;

    @ApiModelProperty(value = "应用ID",dataType = "String")
    private String app_id;

    @ApiModelProperty(value = "应用ID",dataType = "String")
    private String appname;

    @ApiModelProperty(value = "模板ID")
    private String template_id;

    @ApiModelProperty(value = "模板名称")
    private String template_name;

    @ApiModelProperty(value = "能力tag")
    private String ability_tag;

    @ApiModelProperty(value = "制作成功数")
    private Integer success_count;

    @ApiModelProperty(value ="制作失败数")
    private Integer failure_count;

    @ApiModelProperty(value = "制作总数")
    private Integer total_count;

    @ApiModelProperty(value = "制作失败率")
    private String failure_ratio;


}
