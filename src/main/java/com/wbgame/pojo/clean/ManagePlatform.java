package com.wbgame.pojo.clean;

import com.wbgame.common.GenericQueryParameters;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("管理平台")
public class ManagePlatform extends GenericQueryParameters {

    @ApiModelProperty("唯一id")
    private Integer id;

    @ApiModelProperty("状态 0待出包，1出包中，2已完成")
    private Byte state;

    @ApiModelProperty("产品名")
    private String appName;

    @ApiModelProperty("出包名称")
    private String gameName;

    @ApiModelProperty("渠道标识")
    private String channel;

    @ApiModelProperty("公司id")
    private Integer company;

    @ApiModelProperty("包名")
    private String packageName;

    @ApiModelProperty("项目id")
    private String pjId;

    @ApiModelProperty("版本名")
    private String versionName;

    @ApiModelProperty("版本号")
    private String versionCode;

    @ApiModelProperty("发起人")
    private String planners;

    @ApiModelProperty("海外国内标识 1:海外 2:国内")
    private Byte flag;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("完成时间")
    private String endTime;

    @ApiModelProperty("签名")
    private String sign;

    @ApiModelProperty("上传apk")
    private String apk;

    @ApiModelProperty("自定义参数{'key1':'value1','key2':'value2'}")
    private String res;

    @ApiModelProperty("自定义参数{'key1':'value1','key2':'value2'}")
    private String manifestPlaceHolder;

    @ApiModelProperty("自定义参数{'key1':'value1','key2':'value2'}")
    private String defaultConfig;

    
    @ApiModelProperty("指定密钥库的密码(获取keystore信息所需的密码)")
    private String storepass;
    @ApiModelProperty("指定别名条目的密码(私钥的密码)")
    private String keypass;
    @ApiModelProperty("alias指定别名")
    private String alias;
    @ApiModelProperty("签名文件")
    private String keystore;
    @ApiModelProperty("签名文件的MD5")
    private String md5;
    @ApiModelProperty("签名文件的SHA1")
    private String sha1;
    @ApiModelProperty("签名文件的SHA256")
    private String sha256;

    @ApiModelProperty("sdkFiles")
    private String sdkFiles;

    @ApiModelProperty("产品图标")
    private String icon;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("修改人")
    private String updateUser;

    @ApiModelProperty("修改时间")
    private String updateTime;

    @ApiModelProperty("公司集合")
    private List<Integer> companyList;

    public String getSdkFiles() {
        return sdkFiles;
    }

    public void setSdkFiles(String sdkFiles) {
        this.sdkFiles = sdkFiles;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public List<Integer> getCompanyList() {
        return companyList;
    }

    public void setCompanyList(List<Integer> companyList) {
        this.companyList = companyList;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getStorepass() {
        return storepass;
    }

    public void setStorepass(String storepass) {
        this.storepass = storepass;
    }

    public String getKeypass() {
        return keypass;
    }

    public void setKeypass(String keypass) {
        this.keypass = keypass;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getKeystore() {
        return keystore;
    }

    public void setKeystore(String keystore) {
        this.keystore = keystore;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getSha1() {
        return sha1;
    }

    public void setSha1(String sha1) {
        this.sha1 = sha1;
    }

    public String getSha256() {
        return sha256;
    }

    public void setSha256(String sha256) {
        this.sha256 = sha256;
    }

    public String getRes() {
        return res;
    }

    public void setRes(String res) {
        this.res = res == null ? null : res.trim();
    }

    public String getManifestPlaceHolder() {
        return manifestPlaceHolder;
    }

    public void setManifestPlaceHolder(String manifestPlaceHolder) {
        this.manifestPlaceHolder = manifestPlaceHolder == null ? null : manifestPlaceHolder.trim();
    }

    public String getDefaultConfig() {
        return defaultConfig;
    }

    public void setDefaultConfig(String defaultConfig) {
        this.defaultConfig = defaultConfig == null ? null : defaultConfig.trim();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName == null ? null : appName.trim();
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName == null ? null : gameName.trim();
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public Integer getCompany() {
        return company;
    }

    public void setCompany(Integer company) {
        this.company = company;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    public String getPjId() {
        return pjId;
    }

    public void setPjId(String pjId) {
        this.pjId = pjId == null ? null : pjId.trim();
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName == null ? null : versionName.trim();
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode == null ? null : versionCode.trim();
    }

    public String getPlanners() {
        return planners;
    }

    public void setPlanners(String planners) {
        this.planners = planners == null ? null : planners.trim();
    }

    public Byte getFlag() {
        return flag;
    }

    public void setFlag(Byte flag) {
        this.flag = flag;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign == null ? null : sign.trim();
    }

    public String getApk() {
        return apk;
    }

    public void setApk(String apk) {
        this.apk = apk == null ? null : apk.trim();
    }

}