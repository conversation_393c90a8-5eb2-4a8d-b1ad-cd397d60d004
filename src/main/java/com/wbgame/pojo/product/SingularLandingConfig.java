package com.wbgame.pojo.product;

import com.wbgame.pojo.PageSizeParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description Singular落地页配置实体类
 * @Date 2025/01/27
 */
@Data
@ApiModel("Singular落地页配置")
public class SingularLandingConfig extends PageSizeParam {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("背景图URL")
    private String backgroundImage;

    @ApiModelProperty("SDK Key")
    private String sdkKey;

    @ApiModelProperty("SDK Secret")
    private String sdkSecret;

    @ApiModelProperty("Bundle ID")
    private String bundleId;

    @ApiModelProperty("基础链接")
    private String baseLink;

    @ApiModelProperty("按钮组信息(JSON格式)")
    private String buttonGroups;

    @ApiModelProperty("状态：1-启用，0-禁用")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("更新人")
    private String updateUser;
}
