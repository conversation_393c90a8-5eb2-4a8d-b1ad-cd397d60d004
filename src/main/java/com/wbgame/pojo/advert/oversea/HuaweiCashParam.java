package com.wbgame.pojo.advert.oversea;

import com.wbgame.pojo.jettison.report.param.BaseReportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17
 * @description
 **/
@Data
@ApiModel(value = "华为投放支出报表查询条件")
public class HuaweiCashParam extends BaseReportParam<HuaweiCashParam> {

    @ApiModelProperty(value = "账户")
    private List<String> account;
    @ApiModelProperty(value = "代理")
    private List<String> agent;
    @ApiModelProperty(value = "优化师")
    private List<String> putUser;
    @ApiModelProperty(value = "投放主体")
    private List<String> accountSubject;
    @ApiModelProperty(value = "自定义日期")
    private List<String> custom_date;

}
