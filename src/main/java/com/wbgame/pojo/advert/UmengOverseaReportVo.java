package com.wbgame.pojo.advert;

import lombok.Data;

/**
 * <AUTHOR>
 * @Classname UmengAdIncomeVo
 * @Description TODO
 * @Date 2022/10/31 11:09
 */
@Data
public class UmengOverseaReportVo {

    private String tdate;
    private String appid;
    private String channel;
    private String media;
    private String gameName;
    private String country;

    private String state;
    private String avgnum;
    private String addnum;
    private String actnum;
    private String appkey;
    private String appname;
    private String daily_duration;
    private String temp_id;
    private String temp_name;
    private String temp_single_name;

    //是否是x模式
    private String is_x;


    private String msg_pv;
    private String banner_pv;
    private String native_msg_pv;
    //原生msg
    private String native_msg_pv_rate;
    /**
     * 前一天的值，用于告警展示
     */
    private String before_native_msg_pv;
    private String native_new_banner_pv;
    private String plaque_video_pv;
    private String native_plaque_pv;
    private String native_splash_pv;
    private String native_banner_pv;
    private String native_banner_pv_rate;
    /**
     * 前一天的值，用于告警展示
     */
    private String before_native_banner_pv;
    private String system_splash_pv;
    private String plaque_pv;
    private String splash_pv;
    private String suspend_icon_pv;
    private String video_pv;
    private String native_new_plaque_pv;
    //人均pv-(msg/yuan)插屏
    private String native_new_plaque_pv_rate;

    //人均pv-插屏
    private String plaque_pv_rate;
    //人均pv-插屏视频
    private String plaque_video_pv_rate;
    //人均pv-banner
    private String banner_pv_rate;
    //(msg/yuans)banner
    private String native_new_banner_pv_rate;
    //视频
    private String video_pv_rate;
    //悬浮icon
    private String suspend_icon_pv_rate;

    /**
     * 前一天的值，用于告警展示
     */
    private String before_native_new_plaque_pv;
    private String total_banner_pv;
    private String total_splash_pv;
    private String total_video_pv;
    private String total_plaque_pv;
    private String total_suspend_icon_pv;
    private String total_plaque_video_pv;
    private String total_native_msg_pv;

    private String total_banner_pv_rate;
    private String total_splash_pv_rate;
    private String total_video_pv_rate;
    private String total_plaque_pv_rate;
    private String total_suspend_icon_pv_rate;
    private String total_plaque_video_pv_rate;
    private String total_native_msg_pv_rate;

    private String total_banner_click;
    private String total_splash_click;
    private String total_video_click;
    private String total_plaque_click;
    private String total_suspend_icon_click;
    private String total_plaque_video_click;
    private String total_native_msg_click;

    private String total_banner_click_rate;
    private String total_splash_click_rate;
    private String total_video_click_rate;
    private String total_plaque_click_rate;
    private String total_suspend_icon_click_rate;
    private String total_plaque_video_click_rate;
    private String total_native_msg_click_rate;

    private String banner_avg_click;
    private String native_splash_avg_click;
    private String native_plaque_avg_click;
    private String native_new_plaque_avg_click;
    private String native_msg_avg_click;
    private String video_avg_click;
    private String splash_avg_click;
    private String plaque_video_avg_click;
    private String system_splash_avg_click;
    private String suspend_icon_avg_click;
    private String plaque_avg_click;
    private String native_banner_avg_click;
    private String native_new_banner_avg_click;

    /* 分类型的点击数 */
    private String banner_click;
    private String native_splash_click;
    private String native_plaque_click;
    private String native_new_plaque_click;
    private String native_msg_click;
    private String video_click;
    private String splash_click;
    private String plaque_video_click;
    private String system_splash_click;
    private String suspend_icon_click;
    private String plaque_click;
    private String native_banner_click;
    private String native_new_banner_click;


    private String plaque_video_ctr;
    private String plaque_video_ctr_rate;
    private String native_msg_ctr;
    private String native_msg_ctr_rate;
    private String splash_ctr;

    //ctr-开屏汇总
    private String total_splash_ctr;
    //ctr-开屏汇总增长率
    private String total_splash_ctr_rate;

    private String splash_ctr_rate;
    private String banner_ctr;
    private String banner_ctr_rate;
    private String video_ctr;
    private String video_ctr_rate;
    private String native_splash_ctr;
    private String native_splash_ctr_rate;
    private String plaque_ctr;
    private String plaque_ctr_rate;
    private String native_new_plaque_ctr;
    private String native_new_plaque_ctr_rate;
    private String native_new_banner_ctr;
    private String native_new_banner_ctr_rate;
    private String native_plaque_ctr;
    private String native_plaque_ctr_rate;
    private String native_banner_ctr;
    private String native_banner_ctr_rate;
    private String suspend_icon_ctr;
    private String suspend_icon_ctr_rate;


    private String banner_ecpm_rate;
    private String native_msg_ecpm_rate;
    private String native_plaque_ecpm_rate;
    private String suspend_icon_ecpm_rate;
    private String plaque_video_ecpm_rate;
    private String splash_ecpm_rate;
    private String system_splash_ecpm_rate;
    private String native_splash_ecpm_rate;
    private String native_new_plaque_ecpm_rate;
    private String native_banner_ecpm_rate;
    private String native_new_banner_ecpm_rate;
    private String video_ecpm_rate;
    private String plaque_ecpm_rate;
    private String dau_arpu_rate;

    private String avgNumRate;
    private String dailyDurationRate;


    private String video_arpu;
    private String total_banner_arpu;
    private String native_new_plaque_arpu;
    private String native_new_banner_arpu;
    private String total_suspend_icon_arpu;
    private String total_plaque_arpu;
    private String total_plaque_video_arpu;
    private String banner_arpu;
    private String total_splash_arpu;
    private String dau_arpu;


    private String msg_arpu;
    private String splash_arpu;
    private String total_video_arpu;
    private String total_native_msg_arpu;
    private String plaque_arpu;
    private String plaque_video_arpu;
    private String system_splash_arpu;
    private String suspend_icon_arpu;

    /* 分类型的dau_arpu和rate */
    private String total_video_arpu_rate;
    private String total_native_msg_arpu_rate;
    private String total_splash_arpu_rate;
    private String total_plaque_arpu_rate;
    private String total_plaque_video_arpu_rate;
    private String total_banner_arpu_rate;
    private String total_suspend_icon_arpu_rate;


    /* 分类型的展示数 */
    private String native_splash_show;
    private String splash_show;
    private String native_new_banner_show;
    private String native_plaque_show;
    private String native_new_plaque_show;
    private String native_banner_show;
    private String banner_show;
    private String suspend_icon_show;
    private String system_splash_show;
    private String video_show;
    private String plaque_video_show;
    private String native_msg_show;
    private String plaque_show;


    private String banner_ecpm;
    private String native_banner_ecpm;
    private String plaque_video_ecpm;
    private String native_new_plaque_ecpm;
    private String native_msg_ecpm;
    private String native_splash_ecpm;
    private String suspend_icon_ecpm;
    private String splash_ecpm;
    private String system_splash_ecpm;
    private String native_plaque_ecpm;
    private String video_ecpm;
    private String plaque_ecpm;
    private String native_new_banner_ecpm;


    private String total_income;
    private String native_plaque_income;
    private String video_income;
    private String native_msg_income;
    private String suspend_icon_income;
    private String splash_income;
    private String banner_income;
    private String plaque_video_income;
    private String system_splash_income;
    private String native_banner_income;
    private String native_new_banner_income;
    private String native_splash_income;
    private String plaque_income;
    private String native_new_plaque_income;


    private String plaque_video_cpc;
    private String native_msg_cpc;
    private String native_new_banner_cpc;
    private String banner_cpc;
    private String native_new_plaque_cpc;
    private String video_cpc;
    private String native_splash_cpc;
    private String plaque_cpc;
    private String native_plaque_cpc;
    private String native_banner_cpc;
    private String suspend_icon_cpc;
    private String splash_cpc;


    /*
    cpc增长率字段
     */
    private String plaque_video_cpc_rate;
    private String native_msg_cpc_rate;
    private String native_new_banner_cpc_rate;
    private String banner_cpc_rate;
    private String native_new_plaque_cpc_rate;
    private String video_cpc_rate;
    private String native_splash_cpc_rate;
    private String plaque_cpc_rate;
    private String native_plaque_cpc_rate;
    private String native_banner_cpc_rate;
    private String suspend_icon_cpc_rate;
    private String splash_cpc_rate;


    //2023-06-28 新增
    private String avgnum_rate;             //新增占比周对比
    private String daily_duration_rate;     //日均在线时长周对比

    //来源为 1-媒体& 2-自统计
    private String source;

    //总展示数
    private String total_show_num;

    //总点击数
    private String total_click_num;


    //2024-01-03 新增
    private String banner_show_rate; // banner分配比例
    private String new_banner_show_rate; // 新样式banner分配比例
    private String sum_total_banner; // 总pv-banner banner_show+native_new_banner_show
    private String sum_pv_banner; // pv-banner占比
    private String sum_pv_new_banner; // pv-新样式banner占比

    private String all_total_pv; // 总人均pv=所有pv相加/活跃
    private String all_total_click; // 总人均点击=所有点击相加/活跃
    private String all_total_pv_rate;
    private String all_total_click_rate;

    private String active_temp_id;    //活跃模块 功能标识id

    private String active_temp_name;    //活跃模块 功能名称


    private String all_total_ctr;       //总ctr=所有广告类型的点击量/所有广告类型的pv
    private String all_total_ctr_rate;

    private String banner_request;//请求-banner
    private String plaque_request; //请求-插屏
    private String splash_request; // 开屏请求数
    private String video_request; // 视频请求
    private String native_banner_request; // 原生banner请求
    private String native_plaque_request; // 原生插屏请求
    private String native_splash_request; // 原生开屏请求
    private String plaque_video_request; // 插屏视频请求
    private String native_msg_request; // 原生msg 请求
    private String system_splash_request;   //系统开屏 请求
    private String native_new_plaque_request; //原生新样式插屏 请求
    private String native_new_banner_request; //原生新样式banner 请求
    private String suspend_icon_request;  //悬浮icon 请求

    private String all_total_request;    //总人均请求
    private String all_total_request_rate;    //总人均请求
    private String total_splash_request;    //总人均请求-开屏
    private String total_splash_request_rate;    //总人均请求-开屏
    private String total_plaque_request;        //总人均请求-插屏
    private String total_plaque_request_rate;
    private String total_plaque_video_request;    //总人均请求-插屏视频
    private String total_plaque_video_request_rate;
    private String total_banner_request;        //总人均请求-banner
    private String total_banner_request_rate;
    private String total_video_request;        //总人均请求-视频
    private String total_video_request_rate;
    private String total_native_msg_request;    //总人均请求-原生msg
    private String total_native_msg_request_rate;
    private String total_suspend_icon_request;    //总人均请求-悬浮icon
    private String total_suspend_icon_request_rate;

    private String system_splash_avg_request;    //人均请求-系统开屏
    private String splash_avg_request;        //人均请求-开屏
    private String native_splash_avg_request;    //人均请求原生开屏
    private String plaque_avg_request;            //人均请求-插屏
    private String native_new_plaque_avg_request;    //人均请求-（msg/yuan）插屏
    private String plaque_video_avg_request;    //人均请求-插屏视频
    private String banner_avg_request;        //人均请求-banner
    private String native_new_banner_avg_request;    //人均请求-（msg/yuan）banner
    private String video_avg_request;        //人均请求-视频
    private String native_msg_avg_request;    //人均请求-原生msg
    private String suspend_icon_avg_request;    //人均请求-悬浮icon

    private String banner_fill;//填充-banner
    private String plaque_fill;//填充-插屏
    private String splash_fill;//开屏填充数
    private String video_fill; // 视频填充
    private String native_banner_fill; // 原生banner填充
    private String native_plaque_fill; // 原生插屏填充
    private String native_splash_fill; // 原生开屏填充
    private String plaque_video_fill; // 插屏视频填充
    private String native_msg_fill; // 原生msg 填充
    private String system_splash_fill;   //系统开屏 填充
    private String native_new_plaque_fill; //原生新样式插屏 填充
    private String native_new_banner_fill; //原生新样式banner 填充
    private String suspend_icon_fill;  //悬浮icon 填充

    private String all_total_avg_fill;    //总人均填充
    private String all_total_avg_fill_rate;    //总人均填充
    private String total_splash_avg_fill;    //总人均填充-开屏
    private String total_splash_avg_fill_rate;    //总人均填充-开屏
    private String total_plaque_avg_fill;        //总人均填充-插屏
    private String total_plaque_avg_fill_rate;
    private String total_plaque_video_avg_fill;    //总人均填充-插屏视频
    private String total_plaque_video_avg_fill_rate;
    private String total_banner_avg_fill;        //总人均填充-banner
    private String total_banner_avg_fill_rate;
    private String total_video_avg_fill;        //总人均填充-视频
    private String total_video_avg_fill_rate;
    private String total_native_msg_avg_fill;    //总人均填充-原生msg
    private String total_native_msg_avg_fill_rate;
    private String total_suspend_icon_avg_fill;    //总人均填充-悬浮icon
    private String total_suspend_icon_avg_fill_rate;

    private String system_splash_avg_fill;    //人均填充-系统开屏
    private String splash_avg_fill;        //人均填充-开屏
    private String native_splash_avg_fill;    //人均填充原生开屏
    private String plaque_avg_fill;            //人均填充-插屏
    private String native_new_plaque_avg_fill;    //人均填充-（msg/yuan）插屏
    private String plaque_video_avg_fill;    //人均填充-插屏视频
    private String banner_avg_fill;        //人均填充-banner
    private String native_new_banner_avg_fill;    //人均填充-（msg/yuan）banner
    private String video_avg_fill;        //人均填充-视频
    private String native_msg_avg_fill;    //人均填充-原生msg
    private String suspend_icon_avg_fill;    //人均填充-悬浮icon


    private String all_total_fill;        //总填充率=所有广告类型填充/所有广告类型请求
    private String all_total_fill_rate;
    private String total_system_splash_fill;        //填充率-系统开屏
    private String total_system_splash_fill_rate;
    private String total_splash_fill;       //填充率-开屏
    private String total_splash_fill_rate;
    private String total_native_splash_fill;    //填充率-原生开屏
    private String total_native_splash_fill_rate;
    private String total_plaque_fill;       //填充率-插屏
    private String total_plaque_fill_rate;
    private String total_native_new_plaque_fill;    //填充率-（msg/yuans）插屏
    private String total_native_new_plaque_fill_rate;
    private String total_plaque_video_fill;         //填充率-插屏视频
    private String total_plaque_video_fill_rate;
    private String total_banner_fill;           //填充率-banner
    private String total_banner_fill_rate;
    private String total_native_new_banner_fill;    //填充率-（msg/yuans）banner
    private String total_native_new_banner_fill_rate;
    private String total_video_fill;        //填充率-视频
    private String total_video_fill_rate;
    private String total_native_msg_fill;   //填充率-原生msg
    private String total_native_msg_fill_rate;
    private String total_suspend_icon_fill; //填充率-悬浮icon
    private String total_suspend_icon_fill_rate;

}
