package com.wbgame.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-28
 */
@Data
public class PlatformAdDataRequestParam {

    /**
     * 开始页码
     */
    private Integer start;

    /**
     * 每页大小
     */
    private Integer limit;

    /**
     * 查询开始时间
     */
    private String start_date;

    /**
     * 查询结束时间
     */
    private String end_date;

    /**
     * 应用ID列表
     */
    private String appid;

    /**
     * 媒体列表
     */
    private String media;

    /**
     * 子渠道
     */
    private String channel;

    /**
     * 维度
     */
    private String group;

    /**
     * 渠道产品名称
     */
    private String gameName;

    /**
     * 有无x模式
     */
    private String is_x;

    /**
     * 环比/同步：dimension: yesterday-环比，dimension: week-同比
     */
    private String dimension;

    /**
     * 功能标识
     */
    private String temp_id;

    /**
     * 功能名称
     */
    private String temp_name;

    /**
     * 状态
     */
    private String state;

    /**
     * 活跃标志
     */
    private String active_temp_id;

    /**
     * 活跃名称
     */
    private String active_temp_name;

    /**
     * 数据来源：1-媒体，2-自统计
     */
    private String source;

    /**
     * 渠道产品广告数据查询新增日期汇总功能
     */
    private String custom_date;

    /**
     * 应用自定义分组
     */
    private String appid_tag;

    /**
     * 渠道产品名称
     */
    //应用自定义分组是否反选
    private String appid_tag_rev;

    /**
     * 渠道产品名称
     */
    private String prjid_group_id;


    /**
     * 渠道产品名称
     */
    private String order_str;

    /**
     * 渠道产品名称
     */
    // dataSource 1 友盟 2 oppo
    private String dataSource;

    /**
     * 渠道产品名称
     */
    private String tableName;


    private String match_str;

    /**
     * 自定义导出列
     */
    private String value;

    /**
     * 导出文件名
     */
    private String export_file_name;

    /**
     * 国家码
     */
    private String country;

}
