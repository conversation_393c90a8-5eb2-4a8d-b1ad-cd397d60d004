package com.wbgame.pojo.game.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Classname MGReportVo
 * @Description TODO
 * @Date 2022/8/16 14:56
 */
@ApiModel(value = "小游戏事件上报查询返回信息")
public class MGEventReportVo {

    @ApiModelProperty(value = "日期", dataType = "String")
    private String tdate;
    @ApiModelProperty(value = "应用id", dataType = "String")
    private String appid;
    @ApiModelProperty(value = "应用名称", dataType = "String")
    private String appname;
    @ApiModelProperty(value = "事件id", dataType = "String")
    private String event_id;
    @ApiModelProperty(value = "新用户上报人数", dataType = "String")
    private String new_user_cnt;
    @ApiModelProperty(value = "老用户上报人数", dataType = "String")
    private String old_user_cnt;
    @ApiModelProperty(value = "版本号", dataType = "String")
    private String version;
    @ApiModelProperty(value = "设备", dataType = "String")
    private String brand;
    @ApiModelProperty(value = "机型", dataType = "String")
    private String model;

    public String getTdate() {
        return tdate;
    }

    public void setTdate(String tdate) {
        this.tdate = tdate;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getEvent_id() {
        return event_id;
    }

    public void setEvent_id(String event_id) {
        this.event_id = event_id;
    }

    public String getNew_user_cnt() {
        return new_user_cnt;
    }

    public void setNew_user_cnt(String new_user_cnt) {
        this.new_user_cnt = new_user_cnt;
    }

    public String getOld_user_cnt() {
        return old_user_cnt;
    }

    public void setOld_user_cnt(String old_user_cnt) {
        this.old_user_cnt = old_user_cnt;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
