package com.wbgame.pojo.game;

/**
 * <AUTHOR>
 * @Classname ActivityEffectVo
 * @Description 活动影响 ads_activity_effect_daily
 * @Date 2022/5/31 19:51
 */
public class ActivityEffectVo {

    private String data_date;
    private String appid;
    private String appname;
    private String pid;
    private String download_channel;
    private String activity_id;
    private String activity_progress;   //进度
    private String progress_new;        //进度达成人数(新)
    private String progress_old;        //进度达成人数(老)

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getData_date() {
        return data_date;
    }

    public void setData_date(String data_date) {
        this.data_date = data_date;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getDownload_channel() {
        return download_channel;
    }

    public void setDownload_channel(String download_channel) {
        this.download_channel = download_channel;
    }

    public String getActivity_id() {
        return activity_id;
    }

    public void setActivity_id(String activity_id) {
        this.activity_id = activity_id;
    }

    public String getActivity_progress() {
        return activity_progress;
    }

    public void setActivity_progress(String activity_progress) {
        this.activity_progress = activity_progress;
    }

    public String getProgress_new() {
        return progress_new;
    }

    public void setProgress_new(String progress_new) {
        this.progress_new = progress_new;
    }

    public String getProgress_old() {
        return progress_old;
    }

    public void setProgress_old(String progress_old) {
        this.progress_old = progress_old;
    }
}
