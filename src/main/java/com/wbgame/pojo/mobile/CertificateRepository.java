package com.wbgame.pojo.mobile;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class CertificateRepository {
    /**
     * 新增自动生成,定位用
     */
    private Integer id;

    /**
     * 访问域名,前端提供
     */
    private String url;

    /**
     * dns解析域名,申请ssl用,前端提供
     */
    private String dns_url;

    /**
     * dns服务器归属,前端提供
     */
    private String dns_type;

    /**
     * 证书生成时加密方式
     */
    private String ssl_des;

    /**
     * 证书申请完成,发送邮件地址
     */
    private String email;

    /**
     * 是否为测试模式:0-否,1-是
     */
    private Integer test_dns;

    /**
     * 毫秒级时间戳,前端提供
     */
    private String ssl_exp;

    /**
     * 证书下载地址,前端提供
     */
    private String dow_ssl;

    /**
     * 宝塔发送地址
     */
    private String bt_send;

    /**
     * 宝塔文件地址
     */
    private String bt_file;

    /**
     * 申请时间
     */
    private String ap_time;

    /**
     * 当前操作
     */
    private String url_st;

    /**
     * 创建人
     */
    private String create_user;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date create_time;

    /**
     * 更新人
     */
    private String update_user;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date update_time;

    /**
     * 地址名称
     */
    private String name;

    /**
     * 当前地址备注信息
     */
    private String msg;

    /**
     * 部署账号
     */
    private String dns_user;

    /**
     * 最新生成的证书id
     */
    private Long latest_cert_id;
    /**
     * 最近一次的操作的异常信息记录
     */
    private String fail_msg;


    // Getters and Setters

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDns_url() {
        return dns_url;
    }

    public void setDns_url(String dns_url) {
        this.dns_url = dns_url;
    }

    public String getDns_type() {
        return dns_type;
    }

    public void setDns_type(String dns_type) {
        this.dns_type = dns_type;
    }

    public String getSsl_des() {
        return ssl_des;
    }

    public void setSsl_des(String ssl_des) {
        this.ssl_des = ssl_des;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getTest_dns() {
        return test_dns;
    }

    public void setTest_dns(Integer test_dns) {
        this.test_dns = test_dns;
    }

    public String getSsl_exp() {
        return ssl_exp;
    }

    public void setSsl_exp(String ssl_exp) {
        this.ssl_exp = ssl_exp;
    }

    public String getDow_ssl() {
        return dow_ssl;
    }

    public void setDow_ssl(String dow_ssl) {
        this.dow_ssl = dow_ssl;
    }

    public String getBt_send() {
        return bt_send;
    }

    public void setBt_send(String bt_send) {
        this.bt_send = bt_send;
    }

    public String getBt_file() {
        return bt_file;
    }

    public void setBt_file(String bt_file) {
        this.bt_file = bt_file;
    }

    public String getAp_time() {
        return ap_time;
    }

    public void setAp_time(String ap_time) {
        this.ap_time = ap_time;
    }

    public String getUrl_st() {
        return url_st;
    }

    public void setUrl_st(String url_st) {
        this.url_st = url_st;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getDns_user() {
        return dns_user;
    }

    public void setDns_user(String dns_user) {
        this.dns_user = dns_user;
    }

    public Long getLatest_cert_id() {
        return latest_cert_id;
    }

    public void setLatest_cert_id(Long latest_cert_id) {
        this.latest_cert_id = latest_cert_id;
    }

    public String getFail_msg() {
        return fail_msg;
    }

    public void setFail_msg(String fail_msg) {
        this.fail_msg = fail_msg;
    }
}
