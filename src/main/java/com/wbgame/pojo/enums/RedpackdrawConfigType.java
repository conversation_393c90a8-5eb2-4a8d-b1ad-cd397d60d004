package com.wbgame.pojo.enums;

/**
 * 红包提现配置enum
 * <AUTHOR>
 * @date 2020年1月10日
 */
public enum RedpackdrawConfigType {

	ONE(1, "0.3"), TOW(2, "1"), THREE(3, "20"), FOUR(4, "30");

	private int type; 
	private String amount; 

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	private RedpackdrawConfigType(int type, String amount) {
		this.type = type;
		this.amount = amount;
	}

	public static String getAmount(int type) {
		for (RedpackdrawConfigType c : RedpackdrawConfigType.values()) {
			if (c.getType() == type) {
				return c.amount;
			}
		}
		return null;
	}

}
