package com.wbgame.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.mapper.master.AdMapper;
import com.wbgame.mapper.master.UserInfoMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.SysUfsVo;
import com.wbgame.pojo.UmengAdcodeVo;
import com.wbgame.service.AdService;
import com.wbgame.service.PlatformDataService;
import com.wbgame.service.UmengService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.FeishuUtils;
import com.wbgame.utils.MailToolTwo;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.mail.Address;
import javax.mail.internet.InternetAddress;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname AdChannelTask
 * @Description 渠道产品广告数据查询
 * @Date 2021/9/2 15:43
 */
@Component
@RequestMapping("ad-channel")
public class AdChannelTask {

    Logger logger = LoggerFactory.getLogger(AdChannelTask.class);

    private final static String SYS_USER_FS_KEY = "dnwx_sys_fs_user";

    public final static String TENANT_ACCESS_TOEKN_KEY = "tenant_access_token_key";

    /* 华为小米站内信 */
    public final static String HUAWEIMI_CHANNEL_CHAT_ID = "oc_46ce378de12a3ca025d7088e54d52aa5";
    /* OPPOVIVO站内信 */
    public final static String OV_CHANNEL_CHAT_ID = "oc_462e0f5c6dc747f0b5fa55125dfe79cd";

    @Autowired
    UmengService umengService;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private AdMapper adMapper;

    @Autowired
    AdService adService;

    @Autowired
    PlatformDataService platformDataService;

    //对应关系 sdk_adtype open_type - adtype
    private final static Map<String, String> AD_SHOW_TYPE_MAP = new HashMap<String, String>() {{
        put("video_video", "video");
        put("splash_splash", "splash");
        put("natSplash_splash", "nativesplash");
        put("banner_banner", "banner");
        put("plaque_plaque", "plaque");
        put("plaqueVideo_plaque", "plaquevideo");
        put("msg_msg", "nativemsg");
        put("msg_plaque", "nativenewplaque");
        put("msg_banner", "nativenewbanner");
        put("natPlaque_plaque", "nativeplaque");
        put("natBanner_banner", "nativebanner");
        put("yuans_plaque", "nativenewplaque");
        put("yuans_msg", "nativemsg");
        put("icon_icon", "icon");
        put("msg_icon","suspendicon");
        put("yuans_banner","nativenewbanner");
        put("yuans_splash","nativesplash");
    }};

    /**
     * 每天2点同步 以下平台广告位 oppo/oppoml/oppo2/oppomj/vivo/vivoml/xiaomi/xmyy/xiaomimj/ad4399/huawei/huaweimj
     * 同步广告源表广告位至广告系统渠道产品广告ID表 dn_extend_adsid_manage->umeng_adcode_list
     */
    @Scheduled(cron = "00 00 02 * * ?")
    @RequestMapping("adsid/sys")
    public void syncAdSidToAdCode(){
        List<UmengAdcodeVo> list = new ArrayList<>();
        try {
            //取2021-12-30号后广告源数据,旧数据保持不变
            String sql = " SELECT cha_id as channel , sdk_code as ad_code ,CONCAT(sdk_adtype,'_',open_type) as adtype,appid,b.umeng_key as appkey " +
                    " FROM dnwx_cfg.dn_extend_adsid_manage a INNER JOIN yyhz_0308.app_info b ON a.appid = b.id " +
                    " WHERE cha_id IN ('oppo','oppoml','oppo2','oppomj','vivo','vivoml','vivo2','vivoml2','xiaomi','xmyy'," +
                    " 'xiaomimj','xiaomiml','ad4399','huawei','huaweimj','google_huawei','huawei2','xiaomiwt', 'honor','xiaomiml2') " +
                    " and umeng_key !='' and Date(createtime)>='2021-12-30' and app_category !=3 ";


            List<Map<String,String>> adSidList = adService.queryListMapOne(sql);

            String sqlVivoH5 = "SELECT\n" +
                    "\tcha_id AS channel,\n" +
                    "\tsdk_code AS ad_code,\n" +
                    "\tCONCAT( sdk_adtype, '_', open_type ) AS adtype,\n" +
                    "\tappid,\n" +
                    "\tconvert(appid, CHAR) AS appkey \n" +
                    "FROM\n" +
                    "\tdnwx_cfg.dn_extend_adsid_manage a\n" +
                    "\tINNER JOIN yyhz_0308.app_info b ON a.appid = b.id \n" +
                    "WHERE\n" +
                    "\tcha_id =\n" +
                    "\t\t'h5_vivo' \n" +
                    "\tAND Date( createtime )>= '2021-12-30' \n" +
                    "\tAND app_category != 3";
            List<Map<String,String>> adSidListVivoH5 = adService.queryListMapOne(sqlVivoH5);
            adSidList.addAll(adSidListVivoH5);

            for (Map<String,String> map :adSidList){
                UmengAdcodeVo vo = new UmengAdcodeVo();
                vo.setStatus("open");
                vo.setChannel(map.get("channel"));
                vo.setAppkey(map.get("appkey"));
                vo.setAd_code(map.get("ad_code"));
                if (BlankUtils.checkBlank(AD_SHOW_TYPE_MAP.get(map.get("adtype")))){
                    continue;
                }
                vo.setAdtype(AD_SHOW_TYPE_MAP.get(map.get("adtype")));
                list.add(vo);
            }
            //处理归到huaweiml->huawei
            String sql2 = " SELECT 'huawei' as channel , sdk_code as ad_code ,CONCAT(sdk_adtype,'_',open_type) as adtype,appid,b.umeng_key as appkey " +
                    " FROM dnwx_cfg.dn_extend_adsid_manage a INNER JOIN yyhz_0308.app_info b ON a.appid = b.id " +
                    " WHERE cha_id IN ('huaweiml') " +
                    " and umeng_key !='' and Date(createtime)>='2021-12-30' and app_category !=3 ";

            List<Map<String,String>> adSidList2 = adService.queryListMapOne(sql2);
            for (Map<String,String> map :adSidList2){
                UmengAdcodeVo vo = new UmengAdcodeVo();
                vo.setStatus("open");
                vo.setChannel(map.get("channel"));
                vo.setAppkey(map.get("appkey"));
                vo.setAd_code(map.get("ad_code"));
                if (BlankUtils.checkBlank(AD_SHOW_TYPE_MAP.get(map.get("adtype")))){
                    continue;
                }
                vo.setAdtype(AD_SHOW_TYPE_MAP.get(map.get("adtype")));
                list.add(vo);
            }

            //处理归到huaweiml2->huawei2
            String sql3 = " SELECT 'huawei2' as channel , sdk_code as ad_code ,CONCAT(sdk_adtype,'_',open_type) as adtype,appid,b.umeng_key as appkey " +
                    " FROM dnwx_cfg.dn_extend_adsid_manage a INNER JOIN yyhz_0308.app_info b ON a.appid = b.id " +
                    " WHERE cha_id IN ('huaweiml2') " +
                    " and umeng_key !='' and Date(createtime)>='2021-12-30' and app_category !=3 ";

            List<Map<String,String>> adSidList3 = adService.queryListMapOne(sql3);
            for (Map<String,String> map :adSidList3){
                UmengAdcodeVo vo = new UmengAdcodeVo();
                vo.setStatus("open");
                vo.setChannel(map.get("channel"));
                vo.setAppkey(map.get("appkey"));
                vo.setAd_code(map.get("ad_code"));
                if (BlankUtils.checkBlank(AD_SHOW_TYPE_MAP.get(map.get("adtype")))){
                    continue;
                }
                vo.setAdtype(AD_SHOW_TYPE_MAP.get(map.get("adtype")));
                list.add(vo);
            }

            //先删除全部数据再插入  
            //adMapper.delUmengAdcode();
            adService.batchInsertUmengAdcode2(list);
        }catch (Exception e){
            logger.error("syncAdSidToAdCode error:",e);
        }

    }

    ExecutorService xiaomiExecutor = new ThreadPoolExecutor(8, 8, 1000,TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(8),
                new CustomizableThreadFactory("xm-umeng-pool-"),
                new ThreadPoolExecutor.AbortPolicy());
    @Scheduled(cron = "00 30 10,11,13,16 * * ?")
    public void syncAdChannelData() {
        logger.info("执行渠道产品广告数据同步开始");
        //可自动拉取的平台   2024.4.8 孙文凤确定可以去掉4399，小米起单独的线程去跑任务
        String[] channels = {"oppo","oppoml","oppo2","oppomj", "vivo","vivoml", "vivo2","vivoml2",
                "huawei","huaweimj","google_huawei","google_xiaomi","huawei2","h5_vivo", "honor","h5_oppo","uc","h5_huawei"};
        String[] asyncChannels = {"xiaomi", "xmyy", "xiaomimj" ,"xiaomiml", "xiaomiwt","xiaomiml2"};
        //oppo平台数据
        String[] oppoChannels = {"oppo","oppoml","oppo2","oppomj","vivo","vivoml","vivo2","vivoml2","xiaomi","xmyy","xiaomimj","xiaomiml",
                "huawei","huaweimj","google_huawei","huawei2","xiaomiwt"};
        String fdateTime = DateTime.now().minusDays(1).toString("yyyyMMdd");
        String sdateTime = DateTime.now().minusDays(2).toString("yyyyMMdd");
        String tdateTime = DateTime.now().minusDays(3).toString("yyyyMMdd");
        //拉取前三天数据
        String[] dates = {fdateTime, sdateTime, tdateTime};
        String[] xmDates = {fdateTime, sdateTime};
        try {
            xiaomiExecutor.submit(() -> doSyncAdChannelData(asyncChannels, oppoChannels, xmDates));
            doSyncAdChannelData(channels, oppoChannels, dates);
        } catch (Exception e) {
            logger.error("syncAdChannelData error:", e);
        }finally {
            logger.info("执行渠道产品广告数据同步结束");
        }

    }

    public void doSyncAdChannelData(String[] channels, String[] oppoChannels, String[] dates) {
        Map<String, String> retMap = new HashMap<>();
        for (String channel : channels) {
            for (String date : dates) {
                logger.info("执行渠道产品广告数据同步 {} {}", date, channel);
                String result = "";
                try {
                    result = umengService.syncChannelAppData(date, channel);
                    // 同步自统计数据
                    umengService.syncChannelSelfData(date, channel);
                    //处理oppo新增活跃计算
                    if (Arrays.asList(oppoChannels).contains(channel)){
                        umengService.syncOppoAdData(date,channel);
                        umengService.syncOppoAdSelfData(date, channel);
                    }
                } catch (Exception e) {
                    logger.error("自动拉取渠道产品广告数据出错", e);
                }
                if (BlankUtils.checkBlank(result)) {
                    retMap.put("渠道:" + channel + "--日期:" + date, "没有拉取到数据,请手动拉取");
                } else {
                    JSONObject ret = JSONObject.parseObject(result);
                    if (!"1".equals(ret.getString("ret"))) {
                        retMap.put("渠道:" + channel + "--日期:" + date, "没有拉取到数据,请手动拉取");
                    }
                }
            }
        }
    }

    /**
     * 每20分钟拉取一次 拉取平台应用状态
     */
    @Scheduled(cron = "00 */20 * * * ?")
    public void syncAppInfoTask(){
        String[] platforms = {"oppo","vivo","xiaomi","huawei", "honor"};
        for (String platform:platforms){
            platformDataService.syncPlatformAppInfoData(platform,"");
        }
    }


    /**
     * 拉取渠道后台站内信，记录渠道违规通知
     */
    @Scheduled(cron = "00 00 */1 * * ?")
    public void syncPlatformMsgTask(){
        //停掉小米的信息获取 20250305-sunfw
        //platformDataService.syncPlatformMsgList("xiaomi", null);
        platformDataService.syncPlatformMsgList("vivo", null);
        //platformDataService.syncPlatformMsgList("oppo", null);
    }
    /**
     * 检查渠道违规通知，发送群消息
     */
    @Scheduled(cron = "00 30 */1 * * ?")
    public void syncSendPlatformMsgTask(){

        String today = DateTime.now().toString("yyyy-MM-dd");
        String sql = "select * from `adv_platform_msg_info` where tdate='"+today+"' and `status`=0 ";
        List<Map<String, Object>> msgList = adService.queryListMap(sql);

        /* 发送渠道违规通知到群消息 */
        if(msgList != null && msgList.size() > 0){
            String temp = (
                        "\n" +
                            "日期：%s\n" +
                            "平台：%s\n" +
                            "主体账户：%s\n" +
                            "信息标题：%s\n" +
                            "告警产品：%s\n" +
                            "告警内容：\n%s");
            for (Map<String, Object> act : msgList) {
                // 移除HTML标签和特殊字符
                String htmlContent = act.get("content")+"";
                String plainText = htmlContent.replaceAll("\\<.*?\\>", "").replaceAll("&nbsp;", "");

                String format = String.format(temp, act.get("tdate"), act.get("platform"), act.get("account")+"-"+act.get("company"), act.get("title"), act.get("tappname")+"-"+act.get("tappid"), plainText);
                if("vivo".equals(act.get("platform"))) {
                    FeishuUtils.sendMsgToGroupRobot(OV_CHANNEL_CHAT_ID, format, "zhenghy,wumy,wust,hehp");
                }else if("oppo".equals(act.get("platform"))) {
                    FeishuUtils.sendMsgToGroupRobot(OV_CHANNEL_CHAT_ID, format, "qinguozhen,wumy,wust,linxm");
                }else if("xiaomi".equals(act.get("platform"))) {
//                    FeishuUtils.sendMsgToGroupRobot(HUAWEIMI_CHANNEL_CHAT_ID, format, "linps,liangp,linxm");
                }
            }

            // 更新消息状态，设为已发送
            try {
                String contentIds = msgList.stream().map(act -> act.get("contentId")+"").collect(Collectors.joining(","));
                String update = "update adv_platform_msg_info set `status`=1 where contentId in (" + contentIds + ")";
                adService.execSql(update);
            }catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * vivo小游戏数据报表拉取
     */
    @Scheduled(cron = "00 00 10,11,17 * * ?")
    public void syncVivoQuickGameTask(){

        String yesterday = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
        platformDataService.syncVivoQuickGameList(null,yesterday,yesterday);
    }


    public static void main(String[] args) {
        Map<String, String> retMap = new HashMap<>();
        retMap.put("sdsd", "sdasd");
        if (retMap.size() > 0) {
            StringBuffer msgContent = new StringBuffer();
            for (Map.Entry<String, String> map : retMap.entrySet()) {
                msgContent.append("<td>" + map.getKey() + "</td>");
            }
            try {
                MailToolTwo.sendMail(msgContent.toString(), new Address[]{
                        new InternetAddress("<EMAIL>")
                });
            } catch (Exception e) {

            }

        }

        String result = "";
        JSONObject ret = JSONObject.parseObject(result);
        if (!"1".equals(ret.getString("ret"))) {
            System.out.println("uuuuuu");
        }
    }





}
