package com.wbgame.enums;

/**
 * <AUTHOR>
 * @Description 证书操作状态码
 * @Date 2025/6/23 12:13
 */
public enum CertificateStatusEnum {

    /**
     * "更新成功"
     */
    SUCCESS("0"),
    /**
     * "运行中"
     */
    RUNNING("1"),
    /**
     * "申请失败"
     */
    APPLY_FAIL("2"),
    /**
     * "上传证书失败"
     */
    UPLOAD_FAIL("3"),
    /**
     * "初始化"
     */
    INITIALIZE("-1");

    private final String statusCode;

    CertificateStatusEnum(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getStatusCode() {
        return this.statusCode;
    }

    public String value() {
        return this.statusCode;
    }


}
