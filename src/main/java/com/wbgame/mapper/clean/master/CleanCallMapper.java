package com.wbgame.mapper.clean.master;

import com.wbgame.pojo.clean.call.CallConfigVo;
import com.wbgame.pojo.clean.call.CallTagConfigVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname CleanCallMapper
 * @Description TODO
 * @Date 2021/8/18 20:01
 */
public interface CleanCallMapper {

    List<CallTagConfigVo> getCallTagList(CallTagConfigVo vo);

    List<CallConfigVo> getCallList(CallConfigVo vo);

    int saveCallTag(CallTagConfigVo vo);

    int saveCall(CallConfigVo vo);

    int updateCallTag(CallTagConfigVo vo);

    int updateCall(CallConfigVo vo);

    int delCallTag(CallTagConfigVo vo);

    int delCall(CallConfigVo vo);

    int saveCallBatch(List<CallConfigVo> list);

    int saveCallTagBatch(List<CallTagConfigVo> list);

    int delBatchCall(CallConfigVo vo);

}
