package com.wbgame.mapper.redpack;

import com.wbgame.pojo.HbMsgQueueConfig;
import com.wbgame.pojo.mobile.HbMsgTypeConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NewHbMsgQueueConfigMapper {
    int deleteByPrimaryKey(@Param("pid") String pid, @Param("sessionId") String sessionId);

    int insert(HbMsgQueueConfig record);

    int insertSelective(HbMsgQueueConfig record);

    HbMsgQueueConfig selectByPrimaryKey(@Param("pid") String pid, @Param("sessionId") String sessionId);

    int updateByPrimaryKeySelective(HbMsgQueueConfig record);

    int updateByPrimaryKey(HbMsgQueueConfig record);

    List<HbMsgQueueConfig> selectByAll(HbMsgQueueConfig param);

    void deleteAllMsgTypes(@Param("pid") String pid, @Param("sessionId") String sessionId);

    List<HbMsgTypeConfig> selectAllMsgs(HbMsgTypeConfig param);

    void batchInsertMsgTypes(List<HbMsgTypeConfig> list);

    HbMsgTypeConfig selectMsgTypeByPrimaryKey(@Param("id") String id);

    void updateMsgType(HbMsgTypeConfig hbMsgTypeConfig);

    void batchDeleteMsgTypes(String[] split);

    List<HbMsgQueueConfig> selectByPid(String oldpid);
}