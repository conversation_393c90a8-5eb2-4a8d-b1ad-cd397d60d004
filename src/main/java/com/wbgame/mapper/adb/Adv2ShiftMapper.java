package com.wbgame.mapper.adb;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.wbgame.pojo.ClientPostParamVo;
import com.wbgame.pojo.NpPostVo;
import com.wbgame.pojo.adv2.ApiClickVo;
import com.wbgame.pojo.adv2.DnNativeGroup;
import com.wbgame.pojo.adv2.ExtendAdApiConfigVo;


public interface Adv2ShiftMapper {

	// 通用执行语句和查询语句
	@Update(" ${sql} ")
	public int execSql(@Param("sql")String sql); // 直接执行DML sql语句
	@Select(" ${sql} ")
	public List<String> queryListString(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, Object>> queryListMap(@Param("sql")String sql);
	@MapKey("mapkey")
	@Select(" ${sql} ")
	public Map<String, Map<String, Object>> queryListMapOfKey(@Param("sql")String sql);
	@Update(" ${sql} ")
	int execSqlHandle(@Param("sql")String sql, @Param("obj")Object obj);
	@Select(" ${sql} ")
	public List<NpPostVo> queryNpPost(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, String>> queryListMapOne(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, Object>> queryListMapTwo(@Param("sql")String sql, @Param("obj")Object obj);
	public int batchExecSql(Map<String, Object> paramMap);
	public int batchExecSqlTwo(Map<String, Object> paramMap);

	
	
	/** 游戏分渠道收支数据汇总 */
	public List<Map<String,Object>> selectGameChaRevenueTotal(Map<String, String> paramMap);
	public Map<String,Object> selectGameChaRevenueTotalSum(Map<String, String> paramMap);
	
}