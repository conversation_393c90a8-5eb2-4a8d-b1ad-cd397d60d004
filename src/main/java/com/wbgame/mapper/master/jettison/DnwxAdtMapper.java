package com.wbgame.mapper.master.jettison;

import com.github.pagehelper.Page;
import com.wbgame.pojo.jettison.MaterialCondition;
import com.wbgame.pojo.jettison.param.GuanBaoParam;
import com.wbgame.pojo.jettison.report.dto.*;
import com.wbgame.pojo.jettison.report.param.AccountParam;
import com.wbgame.pojo.jettison.report.param.AccountSpendEnabledParam;
import com.wbgame.pojo.jettison.report.param.AccountSpendGroupParam;
import com.wbgame.pojo.jettison.report.param.BatchReportParam;
import com.wbgame.pojo.jettison.vo.TfChannelVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 主库数据库交互层
 * @Create 2022-11-15
 */
@Repository
public interface DnwxAdtMapper {

    Page<BatchReportDTO> getBatchList(BatchReportParam param);

    int getBatchCount(@Param("id") int id);

    int addBatch(BatchReportDTO dto);

    int deleteBatch(@Param("id") int id);

    int updateBatch(BatchReportDTO dto);

    int enableBatch(@Param("id") Integer id, @Param("status") Integer status);

    List<Map> getTransferType();

    Page<ArtistDTO> getArtist(@Param("artistName")String artistName);
    ArtistDTO getArtistName(@Param("artistName")String artistName,@Param("id")Integer id);
    int addArtist(ArtistDTO artistDTO);
    int updateArtist(ArtistDTO artistDTO);

    Page<PutUserDTO> getPutUser(@Param("putUserName")String putUserName);
    PutUserDTO getPutUserName(@Param("putUserName")String putUserName,@Param("id")Integer id);
    int addPutUser(PutUserDTO putUser);
    int updatePutUser(PutUserDTO putUser);

    void addTTTAccount(List<AccountDTO> accountList);

    List<Map> getStrategy();

    List<String> get233ParentAccount();

    List<AccountDTO> get233Accounts(AccountParam param);

    void add233ParentAccount(AccountDTO account);

    void update233Account(AccountDTO account);

    int updateEnabled(AccountSpendEnabledParam accountSpendEnabledParam);

    int updateGroupAccount(AccountSpendGroupParam accountSpendGroupParam);

    List<GuanBaoReport> selectGuanBaoReport1(GuanBaoParam param);

    List<GuanBaoReport> selectGuanBaoReport2(GuanBaoParam param);

    List<GuanBaoReport> selectGuanBaoReport3(GuanBaoParam param);

    GuanBaoReport countGuanBaoReport1(GuanBaoParam param);

    GuanBaoReport countGuanBaoReport2(GuanBaoParam param);

    GuanBaoReport countGuanBaoReport3(GuanBaoParam param);

    List<Map<String, Object>> selectTfAccount1(@Param("account")String account);

    List<TfChannelVo> getTfChannels();

    List<Map> getMaterialCountByArtistAndWeek(MaterialCondition materialCondition);

    List<Map> getMaterialSign(MaterialCondition materialCondition);

    List<Map<String, Object>> getTfAccountListByMedia(@Param("media") String media,@Param("putUser") String putUser,@Param("groupId") String groupId);
}
