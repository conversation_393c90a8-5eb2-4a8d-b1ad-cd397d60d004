package com.wbgame.mapper.master.mobile;

import com.wbgame.pojo.mobile.CertificateConfig;
import com.wbgame.pojo.mobile.CertificateDTO;
import com.wbgame.pojo.mobile.CertificateRepository;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/26 20:26
 */
public interface CertificateMapper {


    int insert(CertificateRepository certificate);


    int update(CertificateRepository certificate);


    int delete(Integer id);


    List<CertificateRepository> queryList(CertificateDTO dto);


    CertificateRepository queryById(@Param("id") String id);

    CertificateConfig queryCertificateConfig(@Param("accountType") String accountType);

    int updateStatus(@Param("id") String id, @Param("status") String status, @Param("failMsg") String failMsg);
}
