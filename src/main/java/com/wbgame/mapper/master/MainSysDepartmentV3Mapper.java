package com.wbgame.mapper.master;

import com.wbgame.pojo.finance.MainSysDepartmentV3;
import com.wbgame.pojo.finance.MainSysDepartmentV3DTO;
import com.wbgame.pojo.finance.MainSysDepartmentV3VO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MainSysDepartmentV3Mapper {

    int deleteDepartmentById(List<Integer> id);

    int insertDepartment(MainSysDepartmentV3 record);

    MainSysDepartmentV3 selectDepartment(Integer id);

    int updateDepartmentById(MainSysDepartmentV3 record);

    int updateByPrimaryKey(MainSysDepartmentV3 record);

    @Select("select * from main_sys_department_v3 where department_name = #{departmentName}")
    List<MainSysDepartmentV3> selectDepartmentName(String departmentName);

    List<MainSysDepartmentV3VO> selectDepartmentByCondition(MainSysDepartmentV3DTO sysDepartmentV3DTO);

    /**
     * 将 pid 在list中的 部门 上级置为null
     * @param pId
     * @return
     */
    int updateDepartmentPidIsNullByPid(List<Integer> pId);

    /**
     * 根据部门ID查询上级部门信息
     * @param departmentId
     * @return
     */
    @Select("SELECT p.* FROM main_sys_department_v3 c " +
            "LEFT JOIN main_sys_department_v3 p ON c.parent_id = p.id " +
            "WHERE c.id = #{departmentId}")
    MainSysDepartmentV3 selectParentDepartment(@Param("departmentId") Integer departmentId);

}