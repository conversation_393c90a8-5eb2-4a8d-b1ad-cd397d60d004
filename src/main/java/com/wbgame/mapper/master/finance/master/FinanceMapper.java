package com.wbgame.mapper.master.finance.master;

import com.github.pagehelper.Page;
import com.wbgame.pojo.PredictIncomeOverview;
import com.wbgame.pojo.finance.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 财务系统
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
@Repository
public interface FinanceMapper {

    @Select("select CAST(id as char(20)) as id, app_name from app_info ${where}")
    List<Map<String, Object>> selectAppInfoList(@Param("where") String where);

    @Select("select b.incompany incompany  FROM finance_agent_income a LEFT JOIN " +
            "finance_account_config b ON a.member_id = b.account_id " +
            "GROUP BY b.incompany having b.incompany is not null")
    List<Map<String, Object>> selectInComepanyList();

    int updateFinanceAppidMoney(FinanceAppidMoney financeAppidMoney);

    int syncFinanceAppid(FinanceAppidMoney financeAppidMoney);

    int insertFinanceAppidMoney(List<FinanceAppidMoney> list);

    int insertFinanceAppidMoney2(List<FinanceAppidMoney> list);

    int deleteFinanceAppidMoney(FinanceAppidMoney financeAppidMoney);

    List<FinanceAppidMoney> selectFinanceAppidMoney(Map<String, Object> map);

    FinanceAppidMoney selectSumFinanceAppidMoney(Map<String, Object> map);

    List<FinanceAppidMoney> syscFinanceAppidMoney(FinanceAppidMoney financeAppidMoney);

    List<FinanceAppidMoney> syscFinanceAppidMoney2(FinanceAppidMoney financeAppidMoney);

    int deleteFinanceCpsCost(FinanceCpsCost financeCpsCost);

    int insertFinanceCpsCost(List<FinanceCpsCost> list);

    int updateFinanceCpsCost(FinanceCpsCost financeCpsCost);

    List<FinanceCpsCost> selectFinanceCpsCost(Map<String, Object> map);

    FinanceCpsCost selectSumFinanceCpsCost(Map<String, Object> map);

    int deleteFinanceDevCost(FinanceDevCost financeDevCost);

    int insertFinanceDevCost(List<FinanceDevCost> list);

    int updateFinanceDevCost(FinanceDevCost financeDevCost);

    List<FinanceDevCost> selectFinanceDevCost(Map<String, Object> map);

    FinanceDevCost selectSumFinanceDevCost(Map<String, Object> map);

    int insertFinanceOtherCost(List<FinanceOtherCost> list);

    FinancePlfIncome selectSumFinancePlfIncome(Map<String, Object> map);

    int deleteFinancePlfIncome(FinancePlfIncome financePlfIncome);

    int insertFinancePlfIncome(List<FinancePlfIncome> list);

    int updateFinancePlfIncome(FinancePlfIncome financePlfIncome);

    List<FinancePlfIncome> selectFinancePlfIncome(Map<String, Object> map);

    int deleteDnConfigFinance(DnConfigFinance dnConfigFinance);

    int insertDnConfigFinance(DnConfigFinance dnConfigFinance);

    int updateDnConfigFinance(DnConfigFinance dnConfigFinance);

    List<DnConfigFinance> selectDnConfigFinance(DnConfigFinance dnConfigFinance);

    int batchDnConfigFinance(List<DnConfigFinance> list);

    List<DnReportFinance> selectDnReportFinance(Map<String, Object> map);

    DnReportFinance selectSumDnReportFinance(Map<String, Object> map);

    FinancePreIncome selectSumFinancePreIncome(Map<String, Object> map);

    int deleteFinancePreIncome(FinancePreIncome financePreIncome);

    int insertFinancePreIncome(List<FinancePreIncome> list);

    int updateFinancePreIncome(FinancePreIncome financePreIncome);

    List<FinancePreIncome> selectFinancePreIncome(Map<String, Object> map);

    FinanceSltIncome selectSumFinanceSltIncome(Map<String, Object> map);

    int deleteFinanceSltIncome(FinanceSltIncome financeSltIncome);

    int insertFinanceSltIncome(List<FinanceSltIncome> list);

    int updateFinanceSltIncome(FinanceSltIncome financeSltIncome);

    List<FinanceSltIncome> selectFinanceSltIncome(Map<String, Object> map);

    String[] selectAccountList();

    List<Map<String, Object>> selectFinancePreIncomeNew(Map<String, Object> map);

    List<Map<String, Object>> countFinancePreIncomeNew(Map<String, Object> map);

    List<FinancePreIncomeSum> selectFinancePreIncomeSum(Map<String, Object> map);

    List<Map<String, Object>> countFinancePreIncomeSum(Map<String, Object> map);

    List<Map<String, Object>> exportFinancePreIncomeSum(Map<String, Object> map);

    List<FinancePreIncomeSum> selectSyncFinancePreIncomeSum1(Map<String, Object> map);

    List<FinancePreIncomeSum> selectSyncFinancePreIncomeSum2(Map<String, Object> map);

    int insertFinancePreIncomeSum(List<FinancePreIncomeSum> list);

    List<Map<String, Object>> selectAllAgent();

    Long existfinanceAccountConfig(FinanceAccountConfig app);

    void insertExamineFinanceAppidMoney(@Param(value = "year") String year,@Param(value = "month") String month,@Param(value = "table") String table);

    void deleteExamineFinanceAppidMoney(@Param(value = "year") String year,@Param(value = "month") String month,@Param(value = "table") String table);

    Long countExamineFinanceAppidMoney(@Param(value = "year") String year,@Param(value = "month") String month,@Param(value = "table") String table);

    List<Map<String, String>> selectDnReportFinaceGroup();

    void batchInsertAccountAppid(List<Map<String, String>> list);

    List<FinancePreIncomeSum> selectSyncFinancePreIncomeSum3(Map<String, Object> map);

    void batchInsertFinancePreIncomeSum(List<FinancePreIncomeSum> list);

    List<PredictIncomeOverview> selectPredictIncomeOverview(Map param);

    int updatePredictIncomeOverview(PredictIncomeOverview predictIncomeOverview);

    void batchInsertPredictIncomeOverview(List<PredictIncomeOverview> list);

    List<Map<String, Object>> exportNameRelation();

    List<PredictIncomeOverview> selectSyncProIncomeOverview(Map<String, Object> map);

    PredictIncomeOverview countPredictIncomeOverview(Map param);

    List<FinanceAppidMoneyImport> selectFinanceAppidMoneyImport(Map param);

    FinanceAppidMoneyImport selectSumFinanceAppidMoneyImport(Map param);

    void batchInsertFinanceAppidMoneyImport(List<FinanceAppidMoneyImport> list);

    List<Map<String, Object>> selectBaiduAccountConfigs(Map param);

    void batchInsertBaiduAccountConfigImport(List<Map<String, Object>> list);

    void insertBaiduAccountConfig(Map param);

    void updateBaiduAccountConfig(Map param);

    void deleteBaiduAccountConfig(Map param);

    List<Map<String, Object>> selectFinancePreIncomeDetail(Map param);

    Map<String, Object> countFinancePreIncomeDetail(Map param);

    List<Map<String, Object>> selectSynFinancePreIncome(Map param);

    void batchInsertFinancePreIncomeDetail(List<Map<String, Object>> list);

    int updateDnReportFinance(DnReportFinance1 dnReportFinance);

    int insertDnReportFinance(List<DnReportFinance1> list);

    List<DnReportFinance1> selectDnReportFinance1(Map<String, Object> map);

    DnReportFinance1 selectSumDnReportFinance1(Map<String, Object> map);

//    List<FinancePreIncomeSum> selectSyncFinancePreIncomeSum4(Map<String, Object> map);

    void examineFinacePreIncomeSummary(Map<String, Object> param);

    FinancePreIncomeSum selectFinancePreIncomeSumById(Long id);

    void deleteFinancePreIncomeSumNotExamine(Map<String, Object> map);

    FinancePreIncomeSum selectFinancePreIncomeSumOne(FinancePreIncomeSum financePreIncomeSum);

    void deleteFinancePreIncomeDetail(Map<String, Object> param);

    Page<FinanceSpendReportDTO> getFinanceSpendReport(FinanceSpendReportParam financeSpendReportParam);
    FinanceSpendReportDTO getFinanceSpendReportSummary(FinanceSpendReportParam financeSpendReportParam);

    Page<FinanceOtherCost> getFinanceOtherCostList(FinanceOtherCost financeOtherCost);
    FinanceOtherCost getFinanceOtherCostListSummary(FinanceOtherCost financeOtherCost);

    List<FinanceOtherCost> getWithoutAppReport(FinanceOtherCost financeOtherCost);

    List<FinanceOtherCost> getWithoutMediaReport(FinanceOtherCost financeOtherCost);

    List<FinanceOtherCost> getSameReport(FinanceOtherCost financeOtherCost);

    List<Map<String,String>> getBusiness();
    List<Map<String,String>> getAppCategory();
    List<Map<String,String>> getAppName();


    @MapKey("mapkey")
    @Select(" ${sql} ")
    public Map<String, Map<String, Object>> queryListMapOfKey(@Param("sql")String sql);

    int delFinanceReport(@Param("dayList") List<String> dayList, @Param("ad_platform") String ad_platform);

    int batchAddFinanceReport(@Param("list") List<FinanceReport> reportList);

    void batchAddFinanceReportDuplicate(@Param("list") List<FinanceReport> reportList);

    void updateBalance(@Param("date") String date, @Param("media") String media, @Param("list") ArrayList<FinanceReport> res);

    List<FinanceReport> selectOldFinance(@Param("ad_platform") String adPlatform, @Param("day") String day);

    List<FinanceOhayooVo> selectFinanceOhayooConfig(FinanceOhayooVo param);

    void addFinanceOhayooConfig(FinanceOhayooVo param);

    void updateFinanceOhayooConfig(FinanceOhayooVo param);

    void deleteFinanceOhayooConfig(FinanceOhayooVo param);

    FinanceOhayooVo selectFinanceOhayooConfigById(@Param("appid") String appid);


    /* 批量修改变现账号配置 */
    int batchUpdateFinanceAccountConfig(@Param("app") FinanceAccountConfig app,@Param("list") List<String> list);

}