package com.wbgame.mapper.master.finance.master;

import com.wbgame.pojo.finance.DnRebateRateConfig;
import com.wbgame.pojo.finance.DnRebateRateConfigKey;
import com.wbgame.pojo.finance.DnRebateRateConfigVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DnRebateRateConfigMapper {

    int deleteDnRebateRateConfigById(List<DnRebateRateConfigKey> keyList);

    int insertDnRebateRateConfig(DnRebateRateConfig record);

    int updateDnRebateRateConfigBatch(DnRebateRateConfig record);

    int updateDnRebateRateConfig(DnRebateRateConfig record);

    /**
     * 联合主键， 避免抛出异常查询是否已有数据
     */
    @Select("select count(*) from dn_rebate_rate_config " +
            "where media = #{media} and primary_agent = #{primaryAgent} and month = #{month} and app_category = #{app_category}")
    int primaryKeiExist(@Param("media") String media, @Param("primaryAgent") String primaryAgent, @Param("month") String month, @Param("app_category") Integer app_category);

    List<DnRebateRateConfigVO> selectRebateRateByCondition(@Param("media") List<String> media,
                                                           @Param("primaryAgent") List<String> primaryAgent,
                                                           @Param("start_date") String start_date,
                                                           @Param("end_date") String end_date,
                                                           @Param("app_category") List<String> app_category);


    @Insert("replace into dn_rebate_rate_config(media, primary_agent,app_category, rebate_rate, month, create_user) " +
            "select media, primary_agent,app_category, rebate_rate, #{targetMonth}, #{userName} from dn_rebate_rate_config " +
            "where month = #{currentMonth}")
    int copy(@Param("currentMonth") String currentMonth, @Param("targetMonth") String targetMonth, @Param("userName") String userName);
}