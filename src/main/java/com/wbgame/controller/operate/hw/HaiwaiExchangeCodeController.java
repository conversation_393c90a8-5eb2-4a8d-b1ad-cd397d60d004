package com.wbgame.controller.operate.hw;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.aop.ApiNeed;
import com.wbgame.aop.PageLimit;
import com.wbgame.common.Asserts;
import com.wbgame.common.Constants;
import com.wbgame.common.ProtocolConstant;
import com.wbgame.common.ReturnJson;
import com.wbgame.mapper.haiwaiad.HaiwaiPayMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.operate.ExchangeCodeVo;
import com.wbgame.pojo.param.ExchangeCodeParam;
import com.wbgame.service.CommonService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.HttpClientUtils;
import com.wbgame.utils.StringUtils;
import io.swagger.annotations.*;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@RestController
@CrossOrigin
@RequestMapping(value = "/operate/haiwai/exchangeCode")
@Api(tags = "海外单机兑换码v1")
@ApiSupport(author = "huangmb")
public class HaiwaiExchangeCodeController {

    @Resource
    public HaiwaiPayMapper haiwaiPayMapper;

    @Resource
    private CommonService commonService;

    private static final Integer DATA_TYPE_NUMBER =1;
    private static final Integer DATA_TYPE_STRADDNUMBER =2;


    @RequestMapping(value = "/list")
    @PageLimit
    @ApiOperation(value = "查询", notes = "查询兑换码", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = ExchangeCodeVo.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String list(HttpServletRequest request, @ApiNeed({"order","start","limit","code","code_value","status","create_owner"}) ExchangeCodeParam param){
        List<ExchangeCodeVo> list = haiwaiPayMapper.selectExchangeCodes(param);
        JSONObject jsonObject = new JSONObject();
        long size = ((Page) list).getTotal();
        jsonObject.put("totalSize",size);
        jsonObject.put("list",list);
        return ReturnJson.success(jsonObject);
    }


    @RequestMapping(value = "/add")
    @ApiOperation(value = "生成兑换码", notes = "生成兑换码", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public String add(HttpServletRequest request,@ApiNeed({"type","num","value","expire_time","data_type","data_custom"}) ExchangeCodeVo param) {
        if (param.getType() == null || BlankUtils.checkBlank(param.getValue())) {
            return ReturnJson.error(Constants.ParamError);
        }
        if (param.getType() == 2 && param.getNum() == null) {
            return ReturnJson.toErrorJson("多次兑换码需配置兑换次数");
        }
        String code = "";
        if (DATA_TYPE_NUMBER.equals(param.getData_type())){
            code = BlankUtils.getRandomNumber(10);
        }else if (DATA_TYPE_STRADDNUMBER.equals(param.getData_type())){
            code = StringUtils.getStringRandom(10);
        }else {
            // 使用自定义值
            code = param.getData_custom();
        }
        param.setCode(code);
        CurrUserVo loginUser = (CurrUserVo)request.getAttribute("LoginUser");
        param.setCreate_owner(loginUser.getLogin_name());
        haiwaiPayMapper.addExchangeCodes(param);
        return ReturnJson.success("生成成功");
    }

    @RequestMapping(value = "/batchAdd")
    @ApiOperation(value = "批量生成兑换码", notes = "批量新增并导出", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    public void add(HttpServletRequest request, @ApiNeed({"type","num","value","expire_time","data_type"}) ExchangeCodeVo param,
                    @ApiParam(value = "生成数量") Integer generate_num, HttpServletResponse response) {
        if (param.getType() == null || BlankUtils.checkBlank(param.getValue())) {
            Asserts.fail(Constants.ParamError);
        }
        if (param.getType() == 2 && param.getNum() == null) {
            Asserts.fail("多次兑换码需配置兑换次数");
        }
        if (generate_num == null) {
            Asserts.fail("生成数量不能为空");
        }
        List<ExchangeCodeVo> list = new ArrayList<>();
        try {
            for (int i = 0;i<generate_num;i++) {
                ExchangeCodeVo code = new ExchangeCodeVo();
                BeanUtils.copyProperties(param,code);
                String codeStr = "";
                if (DATA_TYPE_NUMBER.equals(param.getData_type())){
                    codeStr = BlankUtils.getRandomNumber(10);
                }else if (DATA_TYPE_STRADDNUMBER.equals(param.getData_type())){
                    codeStr = StringUtils.getStringRandom(10);
                }
                code.setCode(codeStr);
                CurrUserVo loginUser = (CurrUserVo)request.getAttribute("LoginUser");
                code.setCreate_owner(loginUser.getLogin_name());
                list.add(code);
            }
            haiwaiPayMapper.addbatchExchangeCodes(list);
        }catch (Exception e) {
            e.printStackTrace();
            Asserts.fail("生成兑换码异常");
        }


        //导出
        Map<String,String> head = new LinkedHashMap<>();
        head.put("code", "兑换码");
        head.put("code", "兑换值");
        String fileName =  "可使用兑换码_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx";
        ExportExcelUtil.exportXLSX2(response,list,head,fileName);
    }

    @RequestMapping(value = "/batch/upload")
    @ApiOperation(value = "批量上传生成兑换码", notes = "批量上传新增并导出", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 0, message = "操作失败",response = Constants.class)
    })
    @ControllerLoggingEnhancer
    public String upload(@RequestParam(value = "fileName") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException, BiffException {
        Workbook workbook = Workbook.getWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheet(0);

        int row = sheet.getRows();
        List<ExchangeCodeVo> list = new ArrayList<>();
        for (int r = 1; r < row; r++) { // 从第2行开始，第1行为标题，第1列内容为空则不执行
            if (BlankUtils.checkBlank(sheet.getCell(0, r).getContents())) {
                continue;
            }
            int type = BlankUtils.getInt(sheet.getCell(0, r).getContents());
            int data_type = BlankUtils.getInt(sheet.getCell(1, r).getContents());
            String value = sheet.getCell(2, r).getContents();
            int generate_num = BlankUtils.getInt(sheet.getCell(3, r).getContents());
            String expire_time = sheet.getCell(4, r).getContents();
            if (BlankUtils.isBlank(expire_time) || expire_time.length() != 19) {
                return ReturnJson.toErrorJson("第"+ r +"行" +"时间格式错误");
            }
            if (generate_num <= 0) {
                return ReturnJson.toErrorJson("第"+ r +"行" +"生成数量必填且大于0");
            }
            int num = BlankUtils.getInt(sheet.getCell(5, r).getContents());
            if (type == 2 && num<=0) {
                return ReturnJson.toErrorJson("第"+ r +"行" +"多次兑换码需配置兑换次数且大于0");
            }
            

            for (int i = 0; i < generate_num; i++) {
                ExchangeCodeVo code = new ExchangeCodeVo();
                code.setType(type);
                code.setData_type(data_type);
                code.setValue(value);
                code.setExpire_time(expire_time);
                if (type == 2) {
                    code.setNum(num);
                }
                String codeStr = "";
                if (DATA_TYPE_NUMBER.equals(data_type)){
                    codeStr = BlankUtils.getRandomNumber(10);
                }else if (DATA_TYPE_STRADDNUMBER.equals(data_type)){
                    codeStr = StringUtils.getStringRandom(10);
                }
                code.setCode(codeStr);
                CurrUserVo loginUser = (CurrUserVo)request.getAttribute("LoginUser");
                code.setCreate_owner(loginUser.getLogin_name());
                list.add(code);
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            haiwaiPayMapper.addbatchExchangeCodes(list);
            //导出
            Map<String, String> head = new LinkedHashMap<>();
            head.put("code", "兑换码");
            head.put("code", "兑换值");
            String fileName = "可使用兑换码_" + DateTime.now().toString("yyyyMMdd") + ".xlsx";
            ExportExcelUtil.exportXLSX2(response, list, head, fileName);
            return ReturnJson.success("成功");
        }
        return ReturnJson.toErrorJson("生成兑换码为空，请检查导入配置");
    }

    @RequestMapping(value = "/usetest")
    @ApiOperation(value = "兑换码使用测试", notes = "兑换码使用测试", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功",response = Constants.class),
            @ApiResponse(code = 500, message = "操作失败",response = Constants.class)
    })
    public String protocoltest(@ApiParam(value = "产品id") String appid,@ApiParam(value = "兑换码")String code
            ,@ApiParam(value = "版本")String ver,@ApiParam(value = "设备id")String deviceId) throws Exception{
        if (BlankUtils.checkBlank(deviceId)) {
            deviceId = "test";
        }
        JSONObject param = new JSONObject();
        param.put("appid",appid);
        param.put("channel","test");
        param.put("code",code);
        param.put("pid",appid+"001");
        param.put("timestamp",System.currentTimeMillis());
        param.put("deviceId",deviceId);
        param.put("sign",createSign(param,appid));
        String value = Base64.encodeBase64String((param.toJSONString()).getBytes());
        //红包正式刷新缓存地址
        String url = ProtocolConstant.hw_pay_server_url+"/code/"+ver+"/exchange?value="+value;
        String result = HttpClientUtils.getInstance().httpGet(url);
        return result;
    }

    /**
     * 生成签名
     *
     * @param map 参数集合
     * @return
     */
    public String createSign(Map map, String appid) {
        SortedMap<String, String> sort = new TreeMap<String, String>(map);
        //验签-所有参与传参的参数按照accsii排序（升序）
        StringBuffer sb = new StringBuffer();
        Set es = sort.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            Object v = entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k)) {
                sb.append(k + "=" + v + "&");
            }
        }
        Map<String, Object> p = commonService.getSignKey().get(appid);
        String stringSignTemp = sb + "key=" + p.get("app_id");
        String sign = DigestUtils.md5Hex(stringSignTemp).toUpperCase();
        return sign;
    }

    public static void main(String[] args) {
        Integer a = 1;
        Integer b = 1;
        System.out.println(a.equals(b));
    }


    
}
