package com.wbgame.controller.operate;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.wbgame.controller.jettison.BaseController;
import com.wbgame.mapper.adb.LevelDistributionMapper;
import com.wbgame.pojo.jettison.report.InfoResult;
import com.wbgame.pojo.operate.ROIMonthlyVo;
import com.wbgame.pojo.param.ROIMonthlyParam;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.DateUtil;
import com.wbgame.utils.ExportExcelUtil;
import com.wbgame.utils.StringUtils;
import com.wbgame.utils.jettison.JSONResultModel;
import com.wbgame.utils.jettison.JSONResultString;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;


/**
 * desc:月度ROI
 * createBy:xugx
 * date：2022-12-12
 */
@CrossOrigin(maxAge =300)
@RestController
@RequestMapping(value = "/roi/monthly")
@Api(tags = "月度ROI")
@ApiSupport(author = "xugx")
public class ROIMonthlyController extends BaseController {
	
	Logger logger = LoggerFactory.getLogger(ROIMonthlyController.class);
    @Autowired
    private LevelDistributionMapper levelDistributionMapper;
	/**
	 * 月度ROI报表 
	 * @param param  
	 * @return
	 */
	@RequestMapping(value="/list", method={RequestMethod.POST})
	@ApiOperation(value = "查询", notes = "月度ROI查询", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 1002, message = "参数校验失败",response = JSONResultModel.class),
            @ApiResponse(code = 1001, message = "查询异常",response = JSONResultModel.class),
            @ApiResponse(code = 0, message = "成功",response = JSONResultModel.class)
    })
    public InfoResult list(@RequestBody ROIMonthlyParam param ){
		InfoResult result = new InfoResult();
		if(!checkToken(param.getToken())){
			result.setMsg(msg);
			result.setRet(0);
			return result;
		}
        try {
        	PageHelper.startPage(param.getPageNum(), param.getPageSize()); // 进行分页
            List<ROIMonthlyVo> list = levelDistributionMapper.queryRoiMonthly(param);
            ROIMonthlyVo total=null;
            //处理百分比字段
            List<String> percentList = Arrays.asList("roi_1","roi_2","roi_3","roi_4","roi_5","roi_6",
                    "roi_7","roi_8","roi_9","roi_10","roi_11","roi_12");
			// 赋值应用名称
		    Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
		    Map<String ,Object> data=new HashMap<>();
		    if(null!=list&&list.size()>0){
				for (int i = 0; i < list.size(); i++) {
					ROIMonthlyVo dto=list.get(i);
					if(null!=appMap){
						Map<String, Object> app = appMap.get(dto.getApp_id());
				        if(null !=app ){
				        	dto.setAppName((String)app.get("app_name"));;
				        }
					}
					StringUtils.getAndSetObjValue(dto,new ArrayList<>(),percentList);
				}
				total = levelDistributionMapper.queryTotalRoiMonthly(param);
			}
		    @SuppressWarnings("rawtypes")
	        long size = ((Page) list).getTotal();
		    data.put("total", total);
	        data.put("totalSize", size);
	        data.put("list", list);
	        result.setRet(1);
	        result.setData(data);
        } catch (Exception e) {
        	msg=e.getMessage();
			result.setMsg(msg);
			result.setRet(-1);
			logger.error("月度ROI报表查询异常:"+msg);
			e.printStackTrace();
        }
        return result;
    }

    @RequestMapping("/export")
    public JSONResultString export( ROIMonthlyParam param , HttpServletResponse response, HttpServletRequest request){
    	JSONResultString js=new JSONResultString();
		try {
			String token = param.getToken();
	        if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)){
	        	js.setMsg("token验证失败");
	        	js.setRet(0);
	        	return js;
	         }else {
	             redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
	         }
			List<ROIMonthlyVo> list = levelDistributionMapper.queryRoiMonthly(param);
			//处理百分比字段
            List<String> percentList = Arrays.asList("roi_1","roi_2","roi_3","roi_4","roi_5","roi_6",
                    "roi_7","roi_8","roi_9","roi_10","roi_11","roi_12");
			// 赋值应用名称
		    Map<String, Map<String, Object>> appMap = adService.getAppInfoMap("10118");
	        //数据计算
			if(null!=list&&list.size()>0){
				for (int i = 0; i < list.size(); i++) {
					ROIMonthlyVo dto=list.get(i);
					if(null!=appMap){
						Map<String, Object> app = appMap.get(dto.getApp_id());
				        if(null !=app ){
				        	dto.setAppName((String)app.get("app_name"));;
				        }
					}
					StringUtils.getAndSetObjValue(dto,new ArrayList<>(),percentList);
				}
			}
			Map<String,String> head = new LinkedHashMap<>();
			//自定义列
	        String value = param.getCustomizes();
			String[] split = value.split(";");
            for (int i = 0;i<split.length;i++) {
                String[] s = split[i].split(",");
                head.put(s[0],s[1]);
            }
            String export_file_name = request.getParameter("export_file_name");
            if (BlankUtils.checkBlank(export_file_name)){
            	export_file_name = "月度ROI-大数据";
			}
            ExportExcelUtil.exportXLSX2(response,list,head,export_file_name +"_" + DateUtil.getToday()+ ".xlsx");
		} catch (Exception e) {
			msg=e.getMessage();
			js.setRet(-1);
			js.setMsg(msg);
			logger.error("月度ROI报表导出异常:"+msg);
			e.printStackTrace();
		}
		return js;
    }
}
