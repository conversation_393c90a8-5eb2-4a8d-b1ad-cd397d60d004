package com.wbgame.controller.product;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.ReturnJson;
import com.wbgame.common.response.Result;
import com.wbgame.common.response.ResultUtils;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.product.SingularLandingConfig;
import com.wbgame.service.product.SingularLandingConfigService;
import com.wbgame.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Singular落地页配置Controller
 * @Date 2025/06/27
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/product/singular")
@Api(tags = "Singular落地页配置管理")
public class SingularLandingConfigController {

    private static final Logger logger = LoggerFactory.getLogger(SingularLandingConfigController.class);

    @Autowired
    private SingularLandingConfigService singularLandingConfigService;

    /**
     * 分页查询配置列表
     *
     * @param config 查询条件
     * @return 分页结果
     */
    @ApiOperation(value = "分页查询配置列表", notes = "分页查询Singular落地页配置列表")
    @GetMapping("/list")
    @LoginCheck
    public Result<PageResult<SingularLandingConfig>> queryList(@RequestBody SingularLandingConfig config) {
        return singularLandingConfigService.queryList(config);
    }

    /**
     * 根据ID查询配置详情
     *
     * @param id 主键ID
     * @return 配置详情
     */
    @ApiOperation(value = "查询配置详情", notes = "根据ID查询Singular落地页配置详情")
    @GetMapping("/detail/{id}")
    @LoginCheck
    public Result<SingularLandingConfig> queryById(@ApiParam(value = "配置ID", required = true) @PathVariable Long id) {
        return singularLandingConfigService.queryById(id);
    }

    /**
     * 新增配置
     *
     * @param request HttpServletRequest
     * @param config  配置信息
     * @return 操作结果
     */
    @ApiOperation(value = "新增配置", notes = "新增Singular落地页配置")
    @PostMapping("/insert")
    @LoginCheck
    public Result<Integer> insert(HttpServletRequest request, @RequestBody SingularLandingConfig config) {
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        if (loginUser != null) {
            config.setCreateUser(loginUser.getLogin_name());
        }
        return singularLandingConfigService.insert(config);
    }

    /**
     * 更新配置
     *
     * @param request HttpServletRequest
     * @param config  配置信息
     * @return 操作结果
     */
    @ApiOperation(value = "更新配置", notes = "更新Singular落地页配置")
    @PostMapping("/update")
    @LoginCheck
    public Result<Integer> update(HttpServletRequest request, @RequestBody SingularLandingConfig config) {
        CurrUserVo loginUser = (CurrUserVo) request.getAttribute("LoginUser");
        if (loginUser != null) {
            config.setUpdateUser(loginUser.getLogin_name());
        }
        return singularLandingConfigService.update(config);
    }

    /**
     * 删除配置
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @ApiOperation(value = "删除配置", notes = "删除Singular落地页配置")
    @DeleteMapping("/delete/{id}")
    @LoginCheck
    public Result<Integer> deleteById(@ApiParam(value = "配置ID", required = true) @PathVariable Long id) {
        return singularLandingConfigService.deleteById(id);
    }

    /**
     * 批量删除配置
     *
     * @param ids 主键ID列表
     * @return 操作结果
     */
    @ApiOperation(value = "批量删除配置", notes = "批量删除Singular落地页配置")
    @PostMapping("/batchDelete")
    @LoginCheck
    public Result<Integer> batchDelete(@RequestBody List<Long> ids) {
        return singularLandingConfigService.batchDelete(ids);
    }

    /**
     * 根据SDK Key获取配置（供落地页使用，无需登录）
     *
     * @param sdkKey SDK Key
     * @return 配置信息
     */
    @ApiOperation(value = "根据SDK Key获取配置", notes = "根据SDK Key获取Singular落地页配置，供前端落地页使用")
    @GetMapping("/config/sdk/{sdkKey}")
    public Result<SingularLandingConfig> getConfigBySdkKey(@ApiParam(value = "SDK Key", required = true) @PathVariable String sdkKey) {
        return singularLandingConfigService.getConfigBySdkKey(sdkKey);
    }

    /**
     * 根据Bundle ID获取配置（供落地页使用，无需登录）
     *
     * @param bundleId Bundle ID
     * @return 配置信息
     */
    @ApiOperation(value = "根据Bundle ID获取配置", notes = "根据Bundle ID获取Singular落地页配置，供前端落地页使用")
    @GetMapping("/config/bundle/{bundleId}")
    public Result<SingularLandingConfig> getConfigByBundleId(@ApiParam(value = "Bundle ID", required = true) @PathVariable String bundleId) {
        return singularLandingConfigService.getConfigByBundleId(bundleId);
    }

    /**
     * 根据URL参数获取配置（供落地页使用，无需登录）
     *
     * @param sdkKey   SDK Key（可选）
     * @param bundleId Bundle ID（可选）
     * @return 配置信息
     */
    @ApiOperation(value = "根据URL参数获取配置", notes = "根据URL参数获取Singular落地页配置，优先使用SDK Key，其次使用Bundle ID")
    @GetMapping("/config")
    public Result<SingularLandingConfig> getConfig(
            @ApiParam(value = "SDK Key") @RequestParam(required = false) String sdkKey,
            @ApiParam(value = "Bundle ID") @RequestParam(required = false) String bundleId) {
        
        // 优先使用SDK Key
        if (sdkKey != null && !sdkKey.trim().isEmpty()) {
            return singularLandingConfigService.getConfigBySdkKey(sdkKey.trim());
        }
        
        // 其次使用Bundle ID
        if (bundleId != null && !bundleId.trim().isEmpty()) {
            return singularLandingConfigService.getConfigByBundleId(bundleId.trim());
        }
        
        // 参数都为空时返回错误
        return ResultUtils.failure("SDK Key和Bundle ID不能都为空");
    }
}
