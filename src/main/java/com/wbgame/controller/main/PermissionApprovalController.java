package com.wbgame.controller;

import com.alibaba.fastjson.JSONObject;
import com.wbgame.aop.LoginCheck;
import com.wbgame.pojo.PermissionApprovalResponseVO;
import com.wbgame.service.PermissionApprovalService;
import com.wbgame.utils.BlankUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * 权限申请审批控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/main/permission")
@Api(tags = "权限申请审批管理")
@CrossOrigin
public class PermissionApprovalController {

    @Autowired
    private PermissionApprovalService permissionApprovalService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 提交权限申请
     */
    @PostMapping("/apply")
    @ApiOperation(value = "提交权限申请", notes = "用户申请应用或页面权限，返回需要审批的人员信息")
    public String submitPermissionApplication(@RequestBody PermissionApprovalResponseVO request,
                                            HttpServletRequest httpRequest) {
        JSONObject result = new JSONObject();
        
        try {
            // Token验证
            String token = request.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }

            // 处理权限申请
            PermissionApprovalResponseVO response = permissionApprovalService.submitPermissionApplication(request);
            
//            // 创建申请记录
//            Integer applicationId = permissionApprovalService.createPermissionApplicationRecord(request, response);
//            response.setApplicationId(applicationId);

            result.put("ret", 1);
            result.put("msg", "权限申请提交成功");
            result.put("data", response);
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "权限申请提交失败：" + e.getMessage());
        }
        
        return result.toJSONString();
    }

    /**
     * 查询权限申请审批人信息（不创建申请记录）
     */
    @PostMapping("/query-approvers")
    @ApiOperation(value = "查询审批人信息", notes = "查询指定权限申请的审批人信息，不创建申请记录")
    public String queryApprovers(@RequestBody PermissionApprovalResponseVO request,
                               HttpServletRequest httpRequest) {
        JSONObject result = new JSONObject();
        
        try {
            // Token验证
            String token = request.getToken();
            if (BlankUtils.checkBlank(token) || !redisTemplate.hasKey(token)) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.expire(token, 20 * 60, TimeUnit.SECONDS);
            }

            // 查询审批人信息
            PermissionApprovalResponseVO response = permissionApprovalService.submitPermissionApplication(request);

            result.put("ret", 1);
            result.put("msg", "查询成功");
            result.put("data", response);
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("ret", 0);
            result.put("msg", "查询失败：" + e.getMessage());
        }
        
        return result.toJSONString();
    }
}
