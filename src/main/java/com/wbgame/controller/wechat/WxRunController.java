package com.wbgame.controller.wechat;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.mapper.master.WbConfigMapper;
import com.wbgame.mapper.master.YdMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.DnChannelInfo;
import com.wbgame.pojo.DnwxMmparamConfig;
import com.wbgame.pojo.custom.AdMsgTotalVo;
import com.wbgame.service.AdService;
import com.wbgame.service.AdmsgService;
import com.wbgame.servlet.WxDataDeCrypt;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;
import com.wbgame.utils.JxlUtil;

/**
 * 微信业务相关接口 -待删除
 * <AUTHOR>
 */
//@CrossOrigin
//@RestController
public class WxRunController {
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private AdService adService;
	public static String WX_RUNDATA_HASH = "wx_rundata_hash";
	
	/**
	 * 接收客户端上报小程序步数信息
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/wechat/postWeRunData", method={RequestMethod.GET, RequestMethod.POST})
	public String postWeRunData(HttpServletRequest request,HttpServletResponse response) throws IOException {
		
		String unionid = request.getParameter("unionid");
		String sessionKey = request.getParameter("sessionKey");
		String encryptedData = request.getParameter("encryptedData");
		String iv = request.getParameter("iv");
		
		if(BlankUtils.checkBlank(unionid) 
				|| BlankUtils.checkBlank(sessionKey)
				|| BlankUtils.checkBlank(encryptedData)
				|| BlankUtils.checkBlank(iv)){
			return "{\"errMsg\":\"request param error!\", \"errCode\":-1}";
		}
		
		JSONObject result = new JSONObject();
		try {
			// 解析返回步数信息列表
			String decryptData = WxDataDeCrypt.decryptData(encryptedData, sessionKey, iv);
			if(BlankUtils.isJSONObject(decryptData)){
				HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
				
				JSONArray stepInfoList = JSONObject.parseObject(decryptData).getJSONArray("stepInfoList");
				if(stepInfoList != null && !stepInfoList.isEmpty()){
					for (int i = 0; i < stepInfoList.size(); i++) {
						JSONObject object = stepInfoList.getJSONObject(i);
						String tdate = new DateTime(object.getLongValue("timestamp") * 1000).toString("yyyyMMdd");
						
						// 每次接收到数据覆盖最新的即可
						opsForHash.put(WX_RUNDATA_HASH+tdate, unionid, object.getIntValue("step"));
					}
				}
				result.put("stepInfoList", stepInfoList);
			}else{
				return "{\"errMsg\":\"解密数据错误！\", \"errCode\":-1}";
			}
			
			result.put("errCode", 0);
			result.put("errMsg", "postWeRunData:ok");
			
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("errCode", -1);
			result.put("errMsg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	
	/**
	 * 查询小程序步数信息返回客户端
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@RequestMapping(value="/wechat/getWeRunData", method={RequestMethod.GET, RequestMethod.POST})
	public String getWeRunData(HttpServletRequest request,HttpServletResponse response) throws IOException {
		
		String unionid = request.getParameter("unionid");
		String timestamp = request.getParameter("timestamp");
		
		if(BlankUtils.checkBlank(unionid) 
				|| !BlankUtils.isNumeric(timestamp)){
			return "{\"errMsg\":\"request param error!\", \"errCode\":-1}";
		}
		
		JSONObject result = new JSONObject();
		try {
			String tdate = new DateTime(Long.valueOf(timestamp) * 1000).toString("yyyyMMdd");
			
			// 解析返回步数信息列表
			HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
			Integer step = (Integer)opsForHash.get(WX_RUNDATA_HASH+tdate, unionid);
			if(step == null)
				return "{\"errMsg\":\"没有该用户的步数信息\", \"errCode\":-1, \"stepInfo\":{}}";
			
			result.put("errCode", 0);
			result.put("errMsg", "getWeRunData:ok");
			JSONObject stepInfo = new JSONObject();
			stepInfo.put("step", step);
			result.put("stepInfo", stepInfo);
			
		} catch (Exception e) {
			e.printStackTrace();
			
			result.put("errCode", -1);
			result.put("errMsg", "错误信息: "+e.getMessage());
		}
		return JSONObject.toJSONString(result, SerializerFeature.WriteNullStringAsEmpty);
	}
	
}
	
