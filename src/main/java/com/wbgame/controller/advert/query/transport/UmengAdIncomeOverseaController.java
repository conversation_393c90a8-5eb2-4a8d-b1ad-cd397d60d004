package com.wbgame.controller.advert.query.transport;

import com.wbgame.pojo.PlatformAdDataRequestParam;
import com.wbgame.pojo.advert.oversea.BaseResult;
import com.wbgame.service.UmengOverseaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description 渠道产品广告数据查询-海外
 * @Date 2025/6/24 14:25
 */
@CrossOrigin
@RestController
@RequestMapping("/advert/umengAdvIncome/oversea")
public class UmengAdIncomeOverseaController {

    @Autowired
    private UmengOverseaService umengOverseaService;


    @PostMapping("/selectPlatformAdDataList")
    public BaseResult<?> selectPlatformAdDataList(@RequestBody PlatformAdDataRequestParam param){
        return umengOverseaService.selectPlatformAdDataList(param);
    }

    @PostMapping("/export")
    public void exportPlatformAdDataList(HttpServletResponse response, @RequestBody(required = false) PlatformAdDataRequestParam param){
        umengOverseaService.exportPlatformAdDataList(response,param);
    }





}
