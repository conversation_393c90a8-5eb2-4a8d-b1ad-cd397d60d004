package com.wbgame.controller.advert.query.game;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.mapper.master.SomeMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.UmengChannelTotalVo;
import com.wbgame.service.SomeService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.JxlUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 友盟新增活跃表
 * @author: huangmb
 * @date: 2021/06/05
 **/
@CrossOrigin
@RestController
@RequestMapping("/advert/umengChangeInfo")
public class UmengChangeInfoController {

    @Autowired
    private SomeService someService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SomeMapper someMapper;

    @RequestMapping(value="/list", method={RequestMethod.GET,RequestMethod.POST})
    public String list(CurrUserVo cu, HttpServletRequest request, HttpServletResponse response) {
            String start = BlankUtils.checkNull(request, "start");
            String limit = BlankUtils.checkNull(request, "limit");
            int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // 当前页
            int pageNo = (pageStart / pageSize) + 1;
            PageHelper.startPage(pageNo, pageSize); // 进行分页
            String start_date = BlankUtils.checkNull(request, "start_date");
            String end_date = BlankUtils.checkNull(request, "end_date");
            String appid = BlankUtils.checkNull(request, "appid");
            String order = BlankUtils.checkNull(request, "order");

            Map<String, Object> parmMap = new HashMap<>();
            parmMap.put("start_date", start_date);
            parmMap.put("end_date", end_date);
            parmMap.put("appid", appid);
            if(order == null && "".equals(order)){
                parmMap.put("order", "tdate asc");
            }else{
                parmMap.put("order", order);
            }
            List<UmengChannelTotalVo> list = someService.selectUmengChangeInfo(parmMap);
            //导出的excel数据存储
            redisTemplate.opsForValue().set("selectUmengChangeInfo-" + cu.getToken(),
                    someService.selectUmengChangeInfo(parmMap), 20 * 60, TimeUnit.SECONDS);

            long size = ((Page) list).getTotal();
            JSONObject result = new JSONObject();
            result.put("data", list);
            result.put("ret",1);
            result.put("totalCount", size);
            result.put("Summary", someMapper.selectUmengChangeSummary(parmMap));
            return result.toJSONString();
    }

    @RequestMapping(value="/export", method={RequestMethod.GET,RequestMethod.POST})
    public String export(ModelMap map, HttpServletRequest request, HttpServletResponse response) {
        // 数据头
        Map<String, String> headerMap = new LinkedHashMap<String, String>();
        String token = request.getParameter("token");
        // 数据内容
        List<UmengChannelTotalVo> list = (List<UmengChannelTotalVo>) redisTemplate.opsForValue().get("selectUmengChangeInfo-" + token);

        Map<String, String> inMap = new LinkedHashMap<String, String>();
        inMap.put("strDt", "yyyy-MM-dd");
        List<Map<String, Object>> contentList = new ArrayList<Map<String, Object>>();
        Map<String, Object> contentMap = null;
        for(UmengChannelTotalVo temp : list){
            headerMap.put("tdate","日期");
            headerMap.put("appKey","产品id");
            headerMap.put("appname","产品名称");
            headerMap.put("id","公司appid");
            headerMap.put("cname","渠道标识");
            headerMap.put("chaMedia","媒体");
            headerMap.put("chaSubLaunch","投放子渠道");
            headerMap.put("addnum","新增用户");
            headerMap.put("actnum","活跃用户");
            headerMap.put("crashnum","崩溃数");
            headerMap.put("crashrate","崩溃率");

            contentMap = new LinkedHashMap<String, Object>();
            contentMap.put("tdate", temp.getTdate());
            contentMap.put("appKey",temp.getAppkey());
            contentMap.put("appname", temp.getAppname());
            contentMap.put("id",temp.getId());
            contentMap.put("cname",temp.getCname());
            contentMap.put("chaMedia",temp.getChaMedia());
            contentMap.put("chaSubLaunch",temp.getChaSubLaunch());
            contentMap.put("addnum", temp.getAddnum());
            contentMap.put("actnum", temp.getActnum());
            contentMap.put("crashnum", temp.getCrashnum());
            contentMap.put("crashrate", temp.getCrashrate());

            contentList.add(contentMap);

        }
        String fileName = "友盟新增活跃表_" + DateTime.now().toString("yyyyMMdd")
                + ".xls";
        JxlUtil.doSave(fileName, headerMap, contentList, inMap, null, null,request, response);
        return null;
    }

}
