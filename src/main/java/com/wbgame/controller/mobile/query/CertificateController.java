package com.wbgame.controller.mobile.query;

import com.wbgame.annotation.ControllerLoggingEnhancer;
import com.wbgame.common.response.Result;
import com.wbgame.pojo.mobile.CertificateDTO;
import com.wbgame.pojo.mobile.CertificateRepository;
import com.wbgame.service.mobile.CertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description 证书管理
 * @Date 2025/3/26 20:21
 */
@CrossOrigin
@RestController
@RequestMapping("/mobile/certificates")
@ControllerLoggingEnhancer
public class CertificateController {


    @Autowired
    private CertificateService certificateService;

    // 创建证书
    @PostMapping("/insert")
    public Object createCertificate(@RequestBody CertificateRepository certificate) {
        return certificateService.createCertificate(certificate);
    }

    // 获取所有证书
    @PostMapping("/list")
    public Object queryCertificates(@RequestBody CertificateDTO dto) {
        return certificateService.queryCertificates(dto);
    }


    // 更新证书
    @PostMapping("/update")
    public Object updateCertificate(@RequestBody CertificateRepository certificate) {
        return certificateService.updateCertificate(certificate);
    }

    // 删除证书
    @DeleteMapping("/{id}")
    public Object deleteCertificate(@PathVariable Integer id) {
        return certificateService.deleteCertificate(id);
    }

    // 上传证书
    @GetMapping("/uploadCertificate")
    public Object uploadCertificate(@RequestParam String id) {
        return certificateService.uploadCertificate(id);
    }

}
