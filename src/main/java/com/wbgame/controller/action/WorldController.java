package com.wbgame.controller.action;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wbgame.pojo.CityVo;
import com.wbgame.pojo.GameLogVo;
import com.wbgame.pojo.RewardInfoVo;
import com.wbgame.pojo.SuperMathVo;
import com.wbgame.pojo.SysConfigVo;
import com.wbgame.pojo.UserInfoVo;
import com.wbgame.pojo.WordVo;
import com.wbgame.service.WorldService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.CommonUtil;


@Controller
public class WorldController { // 算数高手
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private WorldService worldService;
	
	@RequestMapping(value="/ssds/sysconfig", method=RequestMethod.POST)
	public @ResponseBody String sysconfig(){
		ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
		SysConfigVo config = (SysConfigVo)opsForValue.get(CommonUtil.REDIS_STRING_SYSCONFIG);
		if(config != null){
			return JSONObject.toJSONString(config);
		}else{
			SysConfigVo sys = worldService.selectSysConfigVo(null);
			JSONArray array = new JSONArray();
			String[] split = sys.getReward_img().split(",");
			for (String str : split) {
				JSONObject ob = new JSONObject();
				String[] val = str.split("#_&");
				ob.put("name", val[0]);
				ob.put("img", val[1]);
				array.add(ob);
			}
			sys.setReward_img(array.toJSONString());
			opsForValue.set(CommonUtil.REDIS_STRING_SYSCONFIG, sys);
			return JSONObject.toJSONString(sys);
		}
	}
	
	// 提交用户分数，游戏结束时提交一次
	@RequestMapping(value="/ssds/postscore", method=RequestMethod.POST)
	public @ResponseBody String postscore(HttpServletRequest req){
		try {
			String value = new String(Base64.decodeBase64(req.getParameter("value")),"UTF-8");
			GameLogVo gv = JSONObject.parseObject(value, GameLogVo.class);
			gv.setCreate_date(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
			if(BlankUtils.checkBlank(gv.getWx_id())){
				return "{\"res\":0,\"msg\":\"request value is error!\"}";
			}
			
			ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
			ZSetOperations<String, Object> opsForZSet = redisTemplate.opsForZSet();
			
			redisTemplate.opsForList().leftPush(CommonUtil.REDIS_LIST_GAMELOG, gv);
			
			// 更新使用次数数据
			JSONObject playJson = (JSONObject)opsForValue.get(
					CommonUtil.REDIS_STRING_PLAYSURPLUS+gv.getWx_id()+DateTime.now().toString("yyyyMMdd"));
			Integer pt = playJson.getInteger("play_times");
			Integer st = playJson.getInteger("surplus_times");
			Integer ps = playJson.getInteger("surplus_share");
			playJson.put("play_times", pt + gv.getTimes());
			playJson.put("surplus_times", st + gv.getShare_num() - gv.getTimes());
			playJson.put("surplus_share", ps - gv.getShare_num());
			opsForValue.set(
					CommonUtil.REDIS_STRING_PLAYSURPLUS+gv.getWx_id()+DateTime.now().toString("yyyyMMdd"), playJson);
			
			// 提交分数大于缓存中的则更新
			UserInfoVo user = (UserInfoVo)opsForValue.get(CommonUtil.REDIS_STRING_USERINFO+gv.getWx_id());
			if(user == null){ // 缓存中没有用户
				user = worldService.selectUserInfoVo(gv.getWx_id());
				if(user == null){ // 数据库中没有
					opsForZSet.add(CommonUtil.REDIS_ZSET_SCORE, gv.getWx_id(), gv.getScore());
					int newRank = opsForZSet.reverseRank(CommonUtil.REDIS_ZSET_SCORE, gv.getWx_id()).intValue();
					
					user = new UserInfoVo();
					user.setWx_id(gv.getWx_id());
					user.setPro_id(gv.getPro_id());
					user.setU_name(gv.getU_name());
					user.setU_icon(gv.getU_icon());
					user.setTop_score(gv.getScore());
					user.setTop_high(newRank+1);
					user.setReward_num(0);
					user.setCreate_date(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
					opsForValue.set(CommonUtil.REDIS_STRING_USERINFO+gv.getWx_id(), user);
					worldService.insertUserInfoVo(user);
				}
					
			}
			
			if(gv.getScore() > user.getTop_score()){ // 当分数和排名发生变化时同步数据库信息
				user.setTop_score(gv.getScore());
				opsForZSet.add(CommonUtil.REDIS_ZSET_SCORE, gv.getWx_id(), gv.getScore());
				int newRank = opsForZSet.reverseRank(CommonUtil.REDIS_ZSET_SCORE, gv.getWx_id()).intValue();
				newRank += 1;
				
				if(newRank < user.getTop_high()){
					user.setTop_high(newRank);
				}
				user.setLast_date(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
				
				redisTemplate.opsForList().leftPush(CommonUtil.REDIS_LIST_USERINFOLOG, user);
			}
			
			if(user.getTop_high() < 300)
				opsForValue.set(CommonUtil.REDIS_STRING_USERINFO+gv.getWx_id(), user);
			else
				opsForValue.set(CommonUtil.REDIS_STRING_USERINFO+gv.getWx_id(), user, 3, TimeUnit.DAYS);
				
			
			JSONObject result = new JSONObject();
			result.put("res", 1);
			return result.toJSONString();
		} catch (Exception e) {
//			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("res", 0);
			result.put("msg", "错误信息 "+e.getMessage());
			return result.toJSONString();
		}
		
	}
	
	
	// 满足获取实物奖励时提供，提交手机号码、有实物奖励时提交一次
	@RequestMapping(value="/ssds/postphone", method=RequestMethod.POST)
	public @ResponseBody String postphone(HttpServletRequest req){
		try {
			String value = new String(Base64.decodeBase64(req.getParameter("value")),"UTF-8");
			UserInfoVo ui = JSONObject.parseObject(value, UserInfoVo.class);
			ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
			ZSetOperations<String, Object> opsForZSet = redisTemplate.opsForZSet();
			
			UserInfoVo user = (UserInfoVo)opsForValue.get(CommonUtil.REDIS_STRING_USERINFO+ui.getWx_id());
			if(user == null){ // 缓存中没有用户
				user = worldService.selectUserInfoVo(ui.getWx_id());
			}
			if(user.getReward_num() == null)
				user.setReward_num(0);
			user.setReward_num(user.getReward_num() + 1);
			user.setPhone(ui.getPhone()); // 更新手机号
			opsForValue.set(CommonUtil.REDIS_STRING_USERINFO+ui.getWx_id(), user);
			redisTemplate.opsForList().leftPush(CommonUtil.REDIS_LIST_USERINFOLOG, user);
			
			// 获取奖励次数作为积分的小数来排名
//			int intScore = opsForZSet.score(CommonUtil.REDIS_ZSET_SCORE, ui.getWx_id()).intValue();
//			opsForZSet.add(CommonUtil.REDIS_ZSET_SCORE, ui.getWx_id(), Double.valueOf(intScore+"."+user.getReward_num()));
			
			// 获奖信息入库
			RewardInfoVo ri = new RewardInfoVo();
			ri.setCdate(DateTime.now().toString("yyyy-MM-dd"));
			ri.setWx_id(ui.getWx_id());
			ri.setPro_id(ui.getPro_id());
			ri.setPhone(ui.getPhone());
			worldService.insertRewardInfoVo(ri);
			
			JSONObject result = new JSONObject();
			result.put("res", 1);
			return result.toJSONString();
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("res", 0);
			result.put("msg", "错误信息 "+e.getMessage());
			return result.toJSONString();
		}
		
	}
	
	// 获取全国排行数据-游戏启动时获取一次
	@RequestMapping(value="/ssds/gettop", method=RequestMethod.POST)
	public @ResponseBody String gettop(HttpServletRequest req){
		try {
			String value = new String(Base64.decodeBase64(req.getParameter("value")),"UTF-8");
			UserInfoVo ui = JSONObject.parseObject(value, UserInfoVo.class);
			ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
			ZSetOperations<String, Object> opsForZSet = redisTemplate.opsForZSet();
			
			// 系统配置
			SysConfigVo config = (SysConfigVo)opsForValue.get(CommonUtil.REDIS_STRING_SYSCONFIG);
			String today = DateTime.now().toString("yyyyMMdd");
			JSONObject playJson = (JSONObject)opsForValue.get(
					CommonUtil.REDIS_STRING_PLAYSURPLUS+ui.getWx_id()+today);
			
			JSONObject result = new JSONObject();
			if(playJson == null){
				// 初始值，日更新
				result.put("play_times", 0);
				result.put("surplus_times", config.getChallenge_num());
				result.put("surplus_share", config.getShare_num());
				opsForValue.set(
						CommonUtil.REDIS_STRING_PLAYSURPLUS+ui.getWx_id()+today, result);
				// 设置明天11点过期
				redisTemplate.expireAt(CommonUtil.REDIS_STRING_PLAYSURPLUS+ui.getWx_id()
						+today, new DateTime().plusDays(1).withHourOfDay(11).toDate());
			}else{
				result.put("play_times", playJson.getInteger("play_times"));
				result.put("surplus_times", playJson.getInteger("surplus_times"));
				result.put("surplus_share", playJson.getInteger("surplus_share"));
			}
			UserInfoVo user = (UserInfoVo)opsForValue.get(CommonUtil.REDIS_STRING_USERINFO+ui.getWx_id());
			if(user == null) // 缓存中没有用户，尝试数据库
				user = worldService.selectUserInfoVo(ui.getWx_id());
			
			if(user == null){
				result.put("top_score", 0);
				result.put("top_high", 0);
				result.put("reward_num", 0);
				// 数据不存在，不发送玩家排行信息
			}else{
				result.put("top_score", user.getTop_score());
				result.put("top_high", user.getTop_high());
				result.put("reward_num", user.getReward_num()); // 周更新
				
				// 玩家排行信息
				int newRank = opsForZSet.reverseRank(CommonUtil.REDIS_ZSET_SCORE, ui.getWx_id()).intValue();
				JSONObject curr = new JSONObject();
				curr.put("top_high", newRank + 1); // 排名从0开始算，所以+1
				curr.put("u_name", user.getU_name());
				curr.put("u_icon", user.getU_icon());
				curr.put("top_score", user.getTop_score());
				result.put("curr_list", curr);
			}
			
			
			// top.50排行数据
			Iterator<TypedTuple<Object>> iterator = opsForZSet
					.reverseRangeWithScores(CommonUtil.REDIS_ZSET_SCORE, 0, 49).iterator();
//			System.out.println("排行数据 ---------------------");
			List<String> list = new ArrayList<String>();
			List<Integer> scores = new ArrayList<Integer>();
			while(iterator.hasNext()){ // 获取前10名集合
				TypedTuple<Object> next = iterator.next();
				list.add(CommonUtil.REDIS_STRING_USERINFO+next.getValue());
				scores.add(next.getScore().intValue());
//				System.out.println(CommonUtil.REDIS_STRING_USERINFO+next.getValue()+"\t"+next.getScore());
			}
			List<Object> multiGet = opsForValue.multiGet(list);
			JSONArray array = new JSONArray();
			for (int m = 0; m < multiGet.size(); m++) {
				UserInfoVo uu = (UserInfoVo)multiGet.get(m);
				if(uu != null){
					opsForValue.set(list.get(m), uu);
					
					JSONObject jt = new JSONObject();
					jt.put("top_high", m + 1);
					jt.put("u_name", uu.getU_name());
					jt.put("u_icon", uu.getU_icon());
					jt.put("top_score", scores.get(m)+"分");
					array.add(jt);
				}else{
					// 从数据库获取到存入
					String openid = list.get(m).replace(CommonUtil.REDIS_STRING_USERINFO, "");
					UserInfoVo uuu = worldService.selectUserInfoVo(openid);
					if(uuu != null){
						JSONObject jt = new JSONObject();
						jt.put("top_high", m + 1);
						jt.put("u_name", uuu.getU_name());
						jt.put("u_icon", uuu.getU_icon());
						jt.put("top_score", scores.get(m)+"分");
						array.add(jt);
						
						opsForValue.set(list.get(m), uuu);
					}
				}
			}
			result.put("top_list", array);
			
			return result.toJSONString();
			
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject result = new JSONObject();
			result.put("res", 0);
			result.put("msg", "错误信息 "+e.getMessage());
			return result.toJSONString();
		}
		
	}
	
	// 猜词语获取配置
	@RequestMapping(value="/ccy/getlevels", method= RequestMethod.GET)
    public @ResponseBody String getLevels(HttpServletRequest request, HttpServletResponse response){
        String start = request.getParameter("start_lev");
        String num = request.getParameter("lev_num");
        if(BlankUtils.isNumeric(start) && BlankUtils.isNumeric(num)){
            int lev_start = Integer.valueOf(start)-1;
            int lev_num = Integer.valueOf(num);
            List<WordVo> list = worldService.selectWordInfo();
            List<WordVo> resultList = new ArrayList<WordVo>();
            for(int i=lev_start;i<lev_start+lev_num;i++){
                WordVo wordVo = new WordVo();
                wordVo.setImg(list.get(i).getImg());
                wordVo.setWords(URLEncoder.encode(list.get(i).getWords()));
                wordVo.setAnswer(URLEncoder.encode(list.get(i).getAnswer()));
                wordVo.setLev(list.get(i).getLev());
                resultList.add(wordVo);
            }
            int levNum = worldService.selectWordNum();
            JSONObject result = new JSONObject();
            result.put("Lev_arr",resultList);
            result.put("lev_total",levNum);
            return result.toString();
        }
        return null;
    }
	
}
